#############################################################################
# Makefile for building: qt_cpp
# Generated by qmake (3.1) (Qt 5.15.15)
# Project:  ../qt_cpp.pro
# Template: app
# Command: /usr/lib/qt5/bin/qmake -o Makefile ../qt_cpp.pro
#############################################################################

MAKEFILE      = Makefile

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DNO_XUNFEI_SDK -DQT_DEPRECATED_WARNINGS -DQT_NO_DEBUG -DQT_MULTIMEDIAWIDGETS_LIB -DQT_WIDGETS_LIB -DQT_MULTIMEDIA_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB
CFLAGS        = -pipe -std=c99 -O2 -Wall -Wextra -D_REENTRANT -fPIC $(DEFINES)
CXXFLAGS      = -pipe -std=c++17 -O2 -std=gnu++1z -Wall -Wextra -D_REENTRANT -fPIC $(DEFINES)
INCPATH       = -I../../qt_cpp -I. -I../src/core -I../src/speech -I../src/emotion -I../src/sensor -I../src/hardware -I../src/ui -I../src/include -Ibuild/src/ui -I../../aip-cpp-sdk-4.16.7 -I../../aip-cpp-sdk-4.16.7/base -I/usr/include/jsoncpp -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtMultimedia -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -Ibuild -Ibuild/src/ui -I/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++
QMAKE         = /usr/lib/qt5/bin/qmake
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = install -m 644 -p
INSTALL_PROGRAM = install -m 755 -p
INSTALL_DIR   = cp -f -R
QINSTALL      = /usr/lib/qt5/bin/qmake -install qinstall
QINSTALL_PROGRAM = /usr/lib/qt5/bin/qmake -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
TAR           = tar -cf
COMPRESS      = gzip -9f
DISTNAME      = qt_cpp1.0.0
DISTDIR = /home/<USER>/桌面/系统能力大赛/qt_cpp/build/build/qt_cpp1.0.0
LINK          = g++
LFLAGS        = -Wl,-O1 -Wl,-rpath-link,/usr/lib/x86_64-linux-gnu
LIBS          = $(SUBLIBS) -lasound -lrt -ldl -lstdc++ -lcurl -ljsoncpp -lssl -lcrypto /usr/lib/x86_64-linux-gnu/libQt5MultimediaWidgets.so /usr/lib/x86_64-linux-gnu/libQt5Widgets.so /usr/lib/x86_64-linux-gnu/libQt5Multimedia.so /usr/lib/x86_64-linux-gnu/libQt5Gui.so /usr/lib/x86_64-linux-gnu/libQt5Network.so /usr/lib/x86_64-linux-gnu/libQt5Core.so -lGL -lpthread   
AR            = ar cqs
RANLIB        = 
SED           = sed
STRIP         = strip

####### Output directory

OBJECTS_DIR   = build/

####### Files

SOURCES       = ../src/core/main.cpp \
		../src/core/mainwindow.cpp \
		../src/core/devicemanager.cpp \
		../src/core/chatdatabase.cpp \
		../src/ui/stylemanager.cpp \
		../src/ui/iconmanager.cpp \
		../src/speech/speechmanager.cpp \
		../src/speech/advanced_speech_engine.cpp \
		../src/speech/baidu_speech_client.cpp \
		../src/speech/baidu_official_client.cpp \
		../src/speech/baidu_tts_client.cpp \
		../src/emotion/emotioncontroller.cpp \
		../src/sensor/sensormanager.cpp \
		../src/sensor/weathermanager.cpp \
		../src/hardware/networkcontroller.cpp \
		../src/hardware/cameramanager.cpp \
		../src/hardware/gpio_control.c \
		../src/hardware/wifihotspotcontroller.cpp \
		../src/hardware/ledcontroller.cpp \
		../src/hardware/hi3861_client.cpp build/qrc_resources.cpp \
		build/moc_mainwindow.cpp \
		build/moc_devicemanager.cpp \
		build/moc_stylemanager.cpp \
		build/moc_speechmanager.cpp \
		build/moc_advanced_speech_engine.cpp \
		build/moc_baidu_speech_client.cpp \
		build/moc_baidu_official_client.cpp \
		build/moc_baidu_tts_client.cpp \
		build/moc_emotioncontroller.cpp \
		build/moc_sensormanager.cpp \
		build/moc_weathermanager.cpp \
		build/moc_networkcontroller.cpp \
		build/moc_cameramanager.cpp \
		build/moc_wifihotspotcontroller.cpp \
		build/moc_ledcontroller.cpp \
		build/moc_hi3861_client.cpp
OBJECTS       = build/main.o \
		build/mainwindow.o \
		build/devicemanager.o \
		build/chatdatabase.o \
		build/stylemanager.o \
		build/iconmanager.o \
		build/speechmanager.o \
		build/advanced_speech_engine.o \
		build/baidu_speech_client.o \
		build/baidu_official_client.o \
		build/baidu_tts_client.o \
		build/emotioncontroller.o \
		build/sensormanager.o \
		build/weathermanager.o \
		build/networkcontroller.o \
		build/cameramanager.o \
		build/gpio_control.o \
		build/wifihotspotcontroller.o \
		build/ledcontroller.o \
		build/hi3861_client.o \
		build/qrc_resources.o \
		build/moc_mainwindow.o \
		build/moc_devicemanager.o \
		build/moc_stylemanager.o \
		build/moc_speechmanager.o \
		build/moc_advanced_speech_engine.o \
		build/moc_baidu_speech_client.o \
		build/moc_baidu_official_client.o \
		build/moc_baidu_tts_client.o \
		build/moc_emotioncontroller.o \
		build/moc_sensormanager.o \
		build/moc_weathermanager.o \
		build/moc_networkcontroller.o \
		build/moc_cameramanager.o \
		build/moc_wifihotspotcontroller.o \
		build/moc_ledcontroller.o \
		build/moc_hi3861_client.o
DIST          = /usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/spec_pre.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/unix.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/linux.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/sanitize.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/gcc-base.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/gcc-base-unix.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/g++-base.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/g++-unix.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/qconfig.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_core.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_core_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_designer.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_edid_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_egl_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_fb_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_glx_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_help.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_input_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_kms_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_multimedia.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_network.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_network_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_service_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_theme_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_uiplugin.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_uitools.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xkbcommon_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt_functions.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt_config.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++/qmake.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/spec_post.prf \
		../../.qmake.stash \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/exclusive_builds.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/toolchain.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/default_pre.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resolve_config.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/default_post.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/warn_on.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resources_functions.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resources.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/moc.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/unix/opengl.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/uic.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/unix/thread.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qmake_use.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/file_copies.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/testcase_targets.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/exceptions.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/yacc.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/lex.prf \
		../../qt_cpp.pro ../src/core/mainwindow.h \
		../src/core/devicemanager.h \
		../src/core/config.h \
		../src/core/chatdatabase.h \
		../src/ui/stylemanager.h \
		../src/ui/iconmanager.h \
		../src/speech/speechmanager.h \
		../src/speech/advanced_speech_engine.h \
		../src/speech/baidu_speech_client.h \
		../src/speech/baidu_official_client.h \
		../src/speech/baidu_tts_client.h \
		../src/emotion/emotioncontroller.h \
		../src/sensor/sensormanager.h \
		../src/sensor/weathermanager.h \
		../src/hardware/networkcontroller.h \
		../src/hardware/cameramanager.h \
		../src/hardware/wifihotspotcontroller.h \
		../src/hardware/ledcontroller.h \
		../src/hardware/hi3861_client.h \
		../src/include/rk3588_gpio_pins.h \
		../src/include/hi3861_gpio_pins.h ../src/core/main.cpp \
		../src/core/mainwindow.cpp \
		../src/core/devicemanager.cpp \
		../src/core/chatdatabase.cpp \
		../src/ui/stylemanager.cpp \
		../src/ui/iconmanager.cpp \
		../src/speech/speechmanager.cpp \
		../src/speech/advanced_speech_engine.cpp \
		../src/speech/baidu_speech_client.cpp \
		../src/speech/baidu_official_client.cpp \
		../src/speech/baidu_tts_client.cpp \
		../src/emotion/emotioncontroller.cpp \
		../src/sensor/sensormanager.cpp \
		../src/sensor/weathermanager.cpp \
		../src/hardware/networkcontroller.cpp \
		../src/hardware/cameramanager.cpp \
		../src/hardware/gpio_control.c \
		../src/hardware/wifihotspotcontroller.cpp \
		../src/hardware/ledcontroller.cpp \
		../src/hardware/hi3861_client.cpp
QMAKE_TARGET  = qt_cpp
DESTDIR       = 
TARGET        = qt_cpp


first: all
####### Build rules

qt_cpp: build/src/ui/ui_mainwindow.h $(OBJECTS)  
	$(LINK) $(LFLAGS) -o $(TARGET) $(OBJECTS) $(OBJCOMP) $(LIBS)

Makefile: ../qt_cpp.pro /usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++/qmake.conf /usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/spec_pre.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/unix.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/linux.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/sanitize.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/gcc-base.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/gcc-base-unix.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/g++-base.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/g++-unix.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/qconfig.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_core.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_core_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_designer.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_edid_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_egl_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_fb_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_glx_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_help.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_input_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_kms_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_multimedia.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_network.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_network_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_service_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_theme_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_uiplugin.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_uitools.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xkbcommon_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt_functions.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt_config.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++/qmake.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/spec_post.prf \
		../.qmake.stash \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/exclusive_builds.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/toolchain.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/default_pre.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resolve_config.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/default_post.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/warn_on.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resources_functions.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resources.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/moc.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/unix/opengl.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/uic.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/unix/thread.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qmake_use.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/file_copies.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/testcase_targets.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/exceptions.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/yacc.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/lex.prf \
		../qt_cpp.pro \
		../resources.qrc
	$(QMAKE) -o Makefile ../qt_cpp.pro
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/spec_pre.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/unix.conf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/linux.conf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/sanitize.conf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/gcc-base.conf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/gcc-base-unix.conf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/g++-base.conf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/common/g++-unix.conf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/qconfig.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_accessibility_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_bootstrap_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_core.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_core_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_designer.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_edid_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_egl_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_fb_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_fontdatabase_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_glx_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_help.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_input_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_kms_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_multimedia.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_multimediawidgets.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_network.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_network_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_platformcompositor_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_service_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_theme_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_uiplugin.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_uitools.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_vulkan_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xkbcommon_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt_functions.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt_config.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++/qmake.conf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/spec_post.prf:
../.qmake.stash:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/exclusive_builds.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/toolchain.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/default_pre.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resolve_config.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/default_post.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/warn_on.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resources_functions.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resources.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/moc.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/unix/opengl.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/uic.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/unix/thread.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qmake_use.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/file_copies.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/testcase_targets.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/exceptions.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/yacc.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/lex.prf:
../qt_cpp.pro:
../resources.qrc:
qmake: FORCE
	@$(QMAKE) -o Makefile ../qt_cpp.pro

qmake_all: FORCE


all: Makefile qt_cpp

dist: distdir FORCE
	(cd `dirname $(DISTDIR)` && $(TAR) $(DISTNAME).tar $(DISTNAME) && $(COMPRESS) $(DISTNAME).tar) && $(MOVE) `dirname $(DISTDIR)`/$(DISTNAME).tar.gz . && $(DEL_FILE) -r $(DISTDIR)

distdir: FORCE
	@test -d $(DISTDIR) || mkdir -p $(DISTDIR)
	$(COPY_FILE) --parents $(DIST) $(DISTDIR)/
	$(COPY_FILE) --parents ../resources.qrc $(DISTDIR)/
	$(COPY_FILE) --parents /usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/data/dummy.cpp $(DISTDIR)/
	$(COPY_FILE) --parents ../src/core/mainwindow.h ../src/core/devicemanager.h ../src/core/config.h ../src/core/chatdatabase.h ../src/ui/stylemanager.h ../src/ui/iconmanager.h ../src/speech/speechmanager.h ../src/speech/advanced_speech_engine.h ../src/speech/baidu_speech_client.h ../src/speech/baidu_official_client.h ../src/speech/baidu_tts_client.h ../src/emotion/emotioncontroller.h ../src/sensor/sensormanager.h ../src/sensor/weathermanager.h ../src/hardware/networkcontroller.h ../src/hardware/cameramanager.h ../src/hardware/wifihotspotcontroller.h ../src/hardware/ledcontroller.h ../src/hardware/hi3861_client.h ../src/include/rk3588_gpio_pins.h ../src/include/hi3861_gpio_pins.h $(DISTDIR)/
	$(COPY_FILE) --parents ../src/core/main.cpp ../src/core/mainwindow.cpp ../src/core/devicemanager.cpp ../src/core/chatdatabase.cpp ../src/ui/stylemanager.cpp ../src/ui/iconmanager.cpp ../src/speech/speechmanager.cpp ../src/speech/advanced_speech_engine.cpp ../src/speech/baidu_speech_client.cpp ../src/speech/baidu_official_client.cpp ../src/speech/baidu_tts_client.cpp ../src/emotion/emotioncontroller.cpp ../src/sensor/sensormanager.cpp ../src/sensor/weathermanager.cpp ../src/hardware/networkcontroller.cpp ../src/hardware/cameramanager.cpp ../src/hardware/gpio_control.c ../src/hardware/wifihotspotcontroller.cpp ../src/hardware/ledcontroller.cpp ../src/hardware/hi3861_client.cpp $(DISTDIR)/
	$(COPY_FILE) --parents ../src/ui/mainwindow.ui $(DISTDIR)/


clean: compiler_clean 
	-$(DEL_FILE) $(OBJECTS)
	-$(DEL_FILE) *~ core *.core


distclean: clean 
	-$(DEL_FILE) $(TARGET) 
	-$(DEL_FILE) Makefile


####### Sub-libraries

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_rcc_make_all: build/qrc_resources.cpp
compiler_rcc_clean:
	-$(DEL_FILE) build/qrc_resources.cpp
build/qrc_resources.cpp: ../resources.qrc \
		/usr/lib/qt5/bin/rcc \
		../src/ui/styles/modern_style.qss
	/usr/lib/qt5/bin/rcc -name resources ../resources.qrc -o build/qrc_resources.cpp

compiler_moc_predefs_make_all: build/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) build/moc_predefs.h
build/moc_predefs.h: /usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/data/dummy.cpp
	g++ -pipe -std=c++17 -O2 -std=gnu++1z -Wall -Wextra -dM -E -o build/moc_predefs.h /usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/data/dummy.cpp

compiler_moc_header_make_all: build/moc_mainwindow.cpp build/moc_devicemanager.cpp build/moc_stylemanager.cpp build/moc_speechmanager.cpp build/moc_advanced_speech_engine.cpp build/moc_baidu_speech_client.cpp build/moc_baidu_official_client.cpp build/moc_baidu_tts_client.cpp build/moc_emotioncontroller.cpp build/moc_sensormanager.cpp build/moc_weathermanager.cpp build/moc_networkcontroller.cpp build/moc_cameramanager.cpp build/moc_wifihotspotcontroller.cpp build/moc_ledcontroller.cpp build/moc_hi3861_client.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) build/moc_mainwindow.cpp build/moc_devicemanager.cpp build/moc_stylemanager.cpp build/moc_speechmanager.cpp build/moc_advanced_speech_engine.cpp build/moc_baidu_speech_client.cpp build/moc_baidu_official_client.cpp build/moc_baidu_tts_client.cpp build/moc_emotioncontroller.cpp build/moc_sensormanager.cpp build/moc_weathermanager.cpp build/moc_networkcontroller.cpp build/moc_cameramanager.cpp build/moc_wifihotspotcontroller.cpp build/moc_ledcontroller.cpp build/moc_hi3861_client.cpp
build/moc_mainwindow.cpp: ../src/core/mainwindow.h \
		../src/hardware/wifihotspotcontroller.h \
		../src/hardware/ledcontroller.h \
		../src/hardware/hi3861_client.h \
		../src/core/chatdatabase.h \
		../src/ui/stylemanager.h \
		../src/ui/iconmanager.h \
		../src/sensor/weathermanager.h \
		build/moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/桌面/系统能力大赛/qt_cpp/build/build/moc_predefs.h -I/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/桌面/系统能力大赛/qt_cpp -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/core -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/speech -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/emotion -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/sensor -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/hardware -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/ui -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/include -I/home/<USER>/桌面/系统能力大赛/qt_cpp/build/src/ui -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7 -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7/base -I/usr/include/jsoncpp -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtMultimedia -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/14 -I/usr/include/x86_64-linux-gnu/c++/14 -I/usr/include/c++/14/backward -I/usr/lib/gcc/x86_64-linux-gnu/14/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../src/core/mainwindow.h -o build/moc_mainwindow.cpp

build/moc_devicemanager.cpp: ../src/core/devicemanager.h \
		build/moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/桌面/系统能力大赛/qt_cpp/build/build/moc_predefs.h -I/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/桌面/系统能力大赛/qt_cpp -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/core -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/speech -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/emotion -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/sensor -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/hardware -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/ui -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/include -I/home/<USER>/桌面/系统能力大赛/qt_cpp/build/src/ui -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7 -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7/base -I/usr/include/jsoncpp -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtMultimedia -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/14 -I/usr/include/x86_64-linux-gnu/c++/14 -I/usr/include/c++/14/backward -I/usr/lib/gcc/x86_64-linux-gnu/14/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../src/core/devicemanager.h -o build/moc_devicemanager.cpp

build/moc_stylemanager.cpp: ../src/ui/stylemanager.h \
		build/moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/桌面/系统能力大赛/qt_cpp/build/build/moc_predefs.h -I/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/桌面/系统能力大赛/qt_cpp -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/core -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/speech -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/emotion -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/sensor -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/hardware -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/ui -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/include -I/home/<USER>/桌面/系统能力大赛/qt_cpp/build/src/ui -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7 -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7/base -I/usr/include/jsoncpp -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtMultimedia -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/14 -I/usr/include/x86_64-linux-gnu/c++/14 -I/usr/include/c++/14/backward -I/usr/lib/gcc/x86_64-linux-gnu/14/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../src/ui/stylemanager.h -o build/moc_stylemanager.cpp

build/moc_speechmanager.cpp: ../src/speech/speechmanager.h \
		build/moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/桌面/系统能力大赛/qt_cpp/build/build/moc_predefs.h -I/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/桌面/系统能力大赛/qt_cpp -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/core -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/speech -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/emotion -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/sensor -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/hardware -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/ui -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/include -I/home/<USER>/桌面/系统能力大赛/qt_cpp/build/src/ui -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7 -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7/base -I/usr/include/jsoncpp -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtMultimedia -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/14 -I/usr/include/x86_64-linux-gnu/c++/14 -I/usr/include/c++/14/backward -I/usr/lib/gcc/x86_64-linux-gnu/14/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../src/speech/speechmanager.h -o build/moc_speechmanager.cpp

build/moc_advanced_speech_engine.cpp: ../src/speech/advanced_speech_engine.h \
		build/moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/桌面/系统能力大赛/qt_cpp/build/build/moc_predefs.h -I/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/桌面/系统能力大赛/qt_cpp -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/core -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/speech -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/emotion -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/sensor -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/hardware -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/ui -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/include -I/home/<USER>/桌面/系统能力大赛/qt_cpp/build/src/ui -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7 -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7/base -I/usr/include/jsoncpp -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtMultimedia -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/14 -I/usr/include/x86_64-linux-gnu/c++/14 -I/usr/include/c++/14/backward -I/usr/lib/gcc/x86_64-linux-gnu/14/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../src/speech/advanced_speech_engine.h -o build/moc_advanced_speech_engine.cpp

build/moc_baidu_speech_client.cpp: ../src/speech/baidu_speech_client.h \
		build/moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/桌面/系统能力大赛/qt_cpp/build/build/moc_predefs.h -I/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/桌面/系统能力大赛/qt_cpp -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/core -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/speech -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/emotion -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/sensor -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/hardware -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/ui -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/include -I/home/<USER>/桌面/系统能力大赛/qt_cpp/build/src/ui -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7 -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7/base -I/usr/include/jsoncpp -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtMultimedia -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/14 -I/usr/include/x86_64-linux-gnu/c++/14 -I/usr/include/c++/14/backward -I/usr/lib/gcc/x86_64-linux-gnu/14/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../src/speech/baidu_speech_client.h -o build/moc_baidu_speech_client.cpp

build/moc_baidu_official_client.cpp: ../src/speech/baidu_official_client.h \
		build/moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/桌面/系统能力大赛/qt_cpp/build/build/moc_predefs.h -I/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/桌面/系统能力大赛/qt_cpp -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/core -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/speech -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/emotion -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/sensor -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/hardware -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/ui -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/include -I/home/<USER>/桌面/系统能力大赛/qt_cpp/build/src/ui -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7 -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7/base -I/usr/include/jsoncpp -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtMultimedia -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/14 -I/usr/include/x86_64-linux-gnu/c++/14 -I/usr/include/c++/14/backward -I/usr/lib/gcc/x86_64-linux-gnu/14/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../src/speech/baidu_official_client.h -o build/moc_baidu_official_client.cpp

build/moc_baidu_tts_client.cpp: ../src/speech/baidu_tts_client.h \
		build/moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/桌面/系统能力大赛/qt_cpp/build/build/moc_predefs.h -I/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/桌面/系统能力大赛/qt_cpp -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/core -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/speech -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/emotion -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/sensor -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/hardware -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/ui -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/include -I/home/<USER>/桌面/系统能力大赛/qt_cpp/build/src/ui -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7 -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7/base -I/usr/include/jsoncpp -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtMultimedia -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/14 -I/usr/include/x86_64-linux-gnu/c++/14 -I/usr/include/c++/14/backward -I/usr/lib/gcc/x86_64-linux-gnu/14/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../src/speech/baidu_tts_client.h -o build/moc_baidu_tts_client.cpp

build/moc_emotioncontroller.cpp: ../src/emotion/emotioncontroller.h \
		build/moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/桌面/系统能力大赛/qt_cpp/build/build/moc_predefs.h -I/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/桌面/系统能力大赛/qt_cpp -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/core -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/speech -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/emotion -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/sensor -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/hardware -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/ui -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/include -I/home/<USER>/桌面/系统能力大赛/qt_cpp/build/src/ui -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7 -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7/base -I/usr/include/jsoncpp -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtMultimedia -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/14 -I/usr/include/x86_64-linux-gnu/c++/14 -I/usr/include/c++/14/backward -I/usr/lib/gcc/x86_64-linux-gnu/14/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../src/emotion/emotioncontroller.h -o build/moc_emotioncontroller.cpp

build/moc_sensormanager.cpp: ../src/sensor/sensormanager.h \
		build/moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/桌面/系统能力大赛/qt_cpp/build/build/moc_predefs.h -I/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/桌面/系统能力大赛/qt_cpp -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/core -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/speech -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/emotion -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/sensor -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/hardware -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/ui -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/include -I/home/<USER>/桌面/系统能力大赛/qt_cpp/build/src/ui -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7 -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7/base -I/usr/include/jsoncpp -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtMultimedia -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/14 -I/usr/include/x86_64-linux-gnu/c++/14 -I/usr/include/c++/14/backward -I/usr/lib/gcc/x86_64-linux-gnu/14/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../src/sensor/sensormanager.h -o build/moc_sensormanager.cpp

build/moc_weathermanager.cpp: ../src/sensor/weathermanager.h \
		build/moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/桌面/系统能力大赛/qt_cpp/build/build/moc_predefs.h -I/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/桌面/系统能力大赛/qt_cpp -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/core -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/speech -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/emotion -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/sensor -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/hardware -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/ui -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/include -I/home/<USER>/桌面/系统能力大赛/qt_cpp/build/src/ui -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7 -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7/base -I/usr/include/jsoncpp -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtMultimedia -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/14 -I/usr/include/x86_64-linux-gnu/c++/14 -I/usr/include/c++/14/backward -I/usr/lib/gcc/x86_64-linux-gnu/14/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../src/sensor/weathermanager.h -o build/moc_weathermanager.cpp

build/moc_networkcontroller.cpp: ../src/hardware/networkcontroller.h \
		build/moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/桌面/系统能力大赛/qt_cpp/build/build/moc_predefs.h -I/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/桌面/系统能力大赛/qt_cpp -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/core -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/speech -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/emotion -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/sensor -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/hardware -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/ui -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/include -I/home/<USER>/桌面/系统能力大赛/qt_cpp/build/src/ui -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7 -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7/base -I/usr/include/jsoncpp -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtMultimedia -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/14 -I/usr/include/x86_64-linux-gnu/c++/14 -I/usr/include/c++/14/backward -I/usr/lib/gcc/x86_64-linux-gnu/14/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../src/hardware/networkcontroller.h -o build/moc_networkcontroller.cpp

build/moc_cameramanager.cpp: ../src/hardware/cameramanager.h \
		build/moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/桌面/系统能力大赛/qt_cpp/build/build/moc_predefs.h -I/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/桌面/系统能力大赛/qt_cpp -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/core -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/speech -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/emotion -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/sensor -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/hardware -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/ui -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/include -I/home/<USER>/桌面/系统能力大赛/qt_cpp/build/src/ui -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7 -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7/base -I/usr/include/jsoncpp -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtMultimedia -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/14 -I/usr/include/x86_64-linux-gnu/c++/14 -I/usr/include/c++/14/backward -I/usr/lib/gcc/x86_64-linux-gnu/14/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../src/hardware/cameramanager.h -o build/moc_cameramanager.cpp

build/moc_wifihotspotcontroller.cpp: ../src/hardware/wifihotspotcontroller.h \
		build/moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/桌面/系统能力大赛/qt_cpp/build/build/moc_predefs.h -I/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/桌面/系统能力大赛/qt_cpp -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/core -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/speech -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/emotion -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/sensor -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/hardware -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/ui -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/include -I/home/<USER>/桌面/系统能力大赛/qt_cpp/build/src/ui -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7 -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7/base -I/usr/include/jsoncpp -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtMultimedia -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/14 -I/usr/include/x86_64-linux-gnu/c++/14 -I/usr/include/c++/14/backward -I/usr/lib/gcc/x86_64-linux-gnu/14/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../src/hardware/wifihotspotcontroller.h -o build/moc_wifihotspotcontroller.cpp

build/moc_ledcontroller.cpp: ../src/hardware/ledcontroller.h \
		build/moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/桌面/系统能力大赛/qt_cpp/build/build/moc_predefs.h -I/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/桌面/系统能力大赛/qt_cpp -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/core -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/speech -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/emotion -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/sensor -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/hardware -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/ui -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/include -I/home/<USER>/桌面/系统能力大赛/qt_cpp/build/src/ui -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7 -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7/base -I/usr/include/jsoncpp -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtMultimedia -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/14 -I/usr/include/x86_64-linux-gnu/c++/14 -I/usr/include/c++/14/backward -I/usr/lib/gcc/x86_64-linux-gnu/14/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../src/hardware/ledcontroller.h -o build/moc_ledcontroller.cpp

build/moc_hi3861_client.cpp: ../src/hardware/hi3861_client.h \
		build/moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/桌面/系统能力大赛/qt_cpp/build/build/moc_predefs.h -I/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/桌面/系统能力大赛/qt_cpp -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/core -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/speech -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/emotion -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/sensor -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/hardware -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/ui -I/home/<USER>/桌面/系统能力大赛/qt_cpp/src/include -I/home/<USER>/桌面/系统能力大赛/qt_cpp/build/src/ui -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7 -I/home/<USER>/桌面/系统能力大赛/aip-cpp-sdk-4.16.7/base -I/usr/include/jsoncpp -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtMultimediaWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtMultimedia -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/14 -I/usr/include/x86_64-linux-gnu/c++/14 -I/usr/include/c++/14/backward -I/usr/lib/gcc/x86_64-linux-gnu/14/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../src/hardware/hi3861_client.h -o build/moc_hi3861_client.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: build/src/ui/ui_mainwindow.h
compiler_uic_clean:
	-$(DEL_FILE) build/src/ui/ui_mainwindow.h
build/src/ui/ui_mainwindow.h: ../src/ui/mainwindow.ui \
		/usr/lib/qt5/bin/uic
	/usr/lib/qt5/bin/uic ../src/ui/mainwindow.ui -o build/src/ui/ui_mainwindow.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 

####### Compile

build/main.o: ../src/core/main.cpp ../src/core/mainwindow.h \
		../src/hardware/wifihotspotcontroller.h \
		../src/hardware/ledcontroller.h \
		../src/hardware/hi3861_client.h \
		../src/core/chatdatabase.h \
		../src/ui/stylemanager.h \
		../src/ui/iconmanager.h \
		../src/sensor/weathermanager.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/main.o ../src/core/main.cpp

build/mainwindow.o: ../src/core/mainwindow.cpp ../src/core/mainwindow.h \
		../src/hardware/wifihotspotcontroller.h \
		../src/hardware/ledcontroller.h \
		../src/hardware/hi3861_client.h \
		../src/core/chatdatabase.h \
		../src/ui/stylemanager.h \
		../src/ui/iconmanager.h \
		../src/sensor/weathermanager.h \
		build/src/ui/ui_mainwindow.h \
		../src/speech/speechmanager.h \
		../src/emotion/emotioncontroller.h \
		../src/sensor/sensormanager.h \
		../src/hardware/networkcontroller.h \
		../src/core/devicemanager.h \
		../src/hardware/cameramanager.h \
		../src/core/config.h \
		../src/include/display_config.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/mainwindow.o ../src/core/mainwindow.cpp

build/devicemanager.o: ../src/core/devicemanager.cpp ../src/core/devicemanager.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/devicemanager.o ../src/core/devicemanager.cpp

build/chatdatabase.o: ../src/core/chatdatabase.cpp ../src/core/chatdatabase.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/chatdatabase.o ../src/core/chatdatabase.cpp

build/stylemanager.o: ../src/ui/stylemanager.cpp ../src/ui/stylemanager.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/stylemanager.o ../src/ui/stylemanager.cpp

build/iconmanager.o: ../src/ui/iconmanager.cpp ../src/ui/iconmanager.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/iconmanager.o ../src/ui/iconmanager.cpp

build/speechmanager.o: ../src/speech/speechmanager.cpp ../src/speech/speechmanager.h \
		../src/speech/advanced_speech_engine.h \
		../src/speech/baidu_speech_client.h \
		../src/speech/baidu_official_client.h \
		../src/speech/baidu_tts_client.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/speechmanager.o ../src/speech/speechmanager.cpp

build/advanced_speech_engine.o: ../src/speech/advanced_speech_engine.cpp ../src/speech/advanced_speech_engine.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/advanced_speech_engine.o ../src/speech/advanced_speech_engine.cpp

build/baidu_speech_client.o: ../src/speech/baidu_speech_client.cpp ../src/speech/baidu_speech_client.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/baidu_speech_client.o ../src/speech/baidu_speech_client.cpp

build/baidu_official_client.o: ../src/speech/baidu_official_client.cpp ../src/speech/baidu_official_client.h \
		../../aip-cpp-sdk-4.16.7/speech.h \
		../../aip-cpp-sdk-4.16.7/base/base.h \
		../../aip-cpp-sdk-4.16.7/base/http.h \
		../../aip-cpp-sdk-4.16.7/base/base64.h \
		../../aip-cpp-sdk-4.16.7/base/utils.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/baidu_official_client.o ../src/speech/baidu_official_client.cpp

build/baidu_tts_client.o: ../src/speech/baidu_tts_client.cpp ../src/speech/baidu_tts_client.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/baidu_tts_client.o ../src/speech/baidu_tts_client.cpp

build/emotioncontroller.o: ../src/emotion/emotioncontroller.cpp ../src/emotion/emotioncontroller.h \
		../src/hardware/networkcontroller.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/emotioncontroller.o ../src/emotion/emotioncontroller.cpp

build/sensormanager.o: ../src/sensor/sensormanager.cpp ../src/sensor/sensormanager.h \
		../src/core/config.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/sensormanager.o ../src/sensor/sensormanager.cpp

build/weathermanager.o: ../src/sensor/weathermanager.cpp ../src/sensor/weathermanager.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/weathermanager.o ../src/sensor/weathermanager.cpp

build/networkcontroller.o: ../src/hardware/networkcontroller.cpp ../src/hardware/networkcontroller.h \
		../src/core/config.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/networkcontroller.o ../src/hardware/networkcontroller.cpp

build/cameramanager.o: ../src/hardware/cameramanager.cpp ../src/hardware/cameramanager.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/cameramanager.o ../src/hardware/cameramanager.cpp

build/gpio_control.o: ../src/hardware/gpio_control.c ../src/include/rk3588_gpio_pins.h
	$(CC) -c $(CFLAGS) $(INCPATH) -o build/gpio_control.o ../src/hardware/gpio_control.c

build/wifihotspotcontroller.o: ../src/hardware/wifihotspotcontroller.cpp ../src/hardware/wifihotspotcontroller.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/wifihotspotcontroller.o ../src/hardware/wifihotspotcontroller.cpp

build/ledcontroller.o: ../src/hardware/ledcontroller.cpp ../src/hardware/ledcontroller.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/ledcontroller.o ../src/hardware/ledcontroller.cpp

build/hi3861_client.o: ../src/hardware/hi3861_client.cpp ../src/hardware/hi3861_client.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/hi3861_client.o ../src/hardware/hi3861_client.cpp

build/qrc_resources.o: build/qrc_resources.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/qrc_resources.o build/qrc_resources.cpp

build/moc_mainwindow.o: build/moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/moc_mainwindow.o build/moc_mainwindow.cpp

build/moc_devicemanager.o: build/moc_devicemanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/moc_devicemanager.o build/moc_devicemanager.cpp

build/moc_stylemanager.o: build/moc_stylemanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/moc_stylemanager.o build/moc_stylemanager.cpp

build/moc_speechmanager.o: build/moc_speechmanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/moc_speechmanager.o build/moc_speechmanager.cpp

build/moc_advanced_speech_engine.o: build/moc_advanced_speech_engine.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/moc_advanced_speech_engine.o build/moc_advanced_speech_engine.cpp

build/moc_baidu_speech_client.o: build/moc_baidu_speech_client.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/moc_baidu_speech_client.o build/moc_baidu_speech_client.cpp

build/moc_baidu_official_client.o: build/moc_baidu_official_client.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/moc_baidu_official_client.o build/moc_baidu_official_client.cpp

build/moc_baidu_tts_client.o: build/moc_baidu_tts_client.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/moc_baidu_tts_client.o build/moc_baidu_tts_client.cpp

build/moc_emotioncontroller.o: build/moc_emotioncontroller.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/moc_emotioncontroller.o build/moc_emotioncontroller.cpp

build/moc_sensormanager.o: build/moc_sensormanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/moc_sensormanager.o build/moc_sensormanager.cpp

build/moc_weathermanager.o: build/moc_weathermanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/moc_weathermanager.o build/moc_weathermanager.cpp

build/moc_networkcontroller.o: build/moc_networkcontroller.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/moc_networkcontroller.o build/moc_networkcontroller.cpp

build/moc_cameramanager.o: build/moc_cameramanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/moc_cameramanager.o build/moc_cameramanager.cpp

build/moc_wifihotspotcontroller.o: build/moc_wifihotspotcontroller.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/moc_wifihotspotcontroller.o build/moc_wifihotspotcontroller.cpp

build/moc_ledcontroller.o: build/moc_ledcontroller.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/moc_ledcontroller.o build/moc_ledcontroller.cpp

build/moc_hi3861_client.o: build/moc_hi3861_client.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/moc_hi3861_client.o build/moc_hi3861_client.cpp

####### Install

install_target: first FORCE
	@test -d $(INSTALL_ROOT)/opt/qt_cpp || mkdir -p $(INSTALL_ROOT)/opt/qt_cpp
	$(QINSTALL_PROGRAM) $(QMAKE_TARGET) $(INSTALL_ROOT)/opt/qt_cpp/$(QMAKE_TARGET)
	-$(STRIP) $(INSTALL_ROOT)/opt/qt_cpp/$(QMAKE_TARGET)

uninstall_target: FORCE
	-$(DEL_FILE) $(INSTALL_ROOT)/opt/qt_cpp/$(QMAKE_TARGET)
	-$(DEL_DIR) $(INSTALL_ROOT)/opt/qt_cpp/ 


install: install_target  FORCE

uninstall: uninstall_target  FORCE

FORCE:

