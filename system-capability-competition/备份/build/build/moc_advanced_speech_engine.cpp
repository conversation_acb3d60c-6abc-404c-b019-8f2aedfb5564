/****************************************************************************
** Meta object code from reading C++ file 'advanced_speech_engine.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.15)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../src/speech/advanced_speech_engine.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'advanced_speech_engine.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.15. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_AdvancedSpeechEngine_t {
    QByteArrayData data[13];
    char stringdata0[184];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_AdvancedSpeechEngine_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_AdvancedSpeechEngine_t qt_meta_stringdata_AdvancedSpeechEngine = {
    {
QT_MOC_LITERAL(0, 0, 20), // "AdvancedSpeechEngine"
QT_MOC_LITERAL(1, 21, 16), // "speechRecognized"
QT_MOC_LITERAL(2, 38, 0), // ""
QT_MOC_LITERAL(3, 39, 4), // "text"
QT_MOC_LITERAL(4, 44, 11), // "speechError"
QT_MOC_LITERAL(5, 56, 5), // "error"
QT_MOC_LITERAL(6, 62, 17), // "synthesisFinished"
QT_MOC_LITERAL(7, 80, 21), // "onRecognitionFinished"
QT_MOC_LITERAL(8, 102, 8), // "exitCode"
QT_MOC_LITERAL(9, 111, 20), // "QProcess::ExitStatus"
QT_MOC_LITERAL(10, 132, 10), // "exitStatus"
QT_MOC_LITERAL(11, 143, 19), // "onSynthesisFinished"
QT_MOC_LITERAL(12, 163, 20) // "onRecognitionTimeout"

    },
    "AdvancedSpeechEngine\0speechRecognized\0"
    "\0text\0speechError\0error\0synthesisFinished\0"
    "onRecognitionFinished\0exitCode\0"
    "QProcess::ExitStatus\0exitStatus\0"
    "onSynthesisFinished\0onRecognitionTimeout"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_AdvancedSpeechEngine[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       6,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   44,    2, 0x06 /* Public */,
       4,    1,   47,    2, 0x06 /* Public */,
       6,    0,   50,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       7,    2,   51,    2, 0x08 /* Private */,
      11,    2,   56,    2, 0x08 /* Private */,
      12,    0,   61,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void, QMetaType::Int, 0x80000000 | 9,    8,   10,
    QMetaType::Void, QMetaType::Int, 0x80000000 | 9,    8,   10,
    QMetaType::Void,

       0        // eod
};

void AdvancedSpeechEngine::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<AdvancedSpeechEngine *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->speechRecognized((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 1: _t->speechError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->synthesisFinished(); break;
        case 3: _t->onRecognitionFinished((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< QProcess::ExitStatus(*)>(_a[2]))); break;
        case 4: _t->onSynthesisFinished((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< QProcess::ExitStatus(*)>(_a[2]))); break;
        case 5: _t->onRecognitionTimeout(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (AdvancedSpeechEngine::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&AdvancedSpeechEngine::speechRecognized)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (AdvancedSpeechEngine::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&AdvancedSpeechEngine::speechError)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (AdvancedSpeechEngine::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&AdvancedSpeechEngine::synthesisFinished)) {
                *result = 2;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject AdvancedSpeechEngine::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_AdvancedSpeechEngine.data,
    qt_meta_data_AdvancedSpeechEngine,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *AdvancedSpeechEngine::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AdvancedSpeechEngine::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_AdvancedSpeechEngine.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int AdvancedSpeechEngine::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 6;
    }
    return _id;
}

// SIGNAL 0
void AdvancedSpeechEngine::speechRecognized(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void AdvancedSpeechEngine::speechError(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void AdvancedSpeechEngine::synthesisFinished()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
