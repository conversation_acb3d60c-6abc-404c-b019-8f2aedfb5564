/****************************************************************************
** Meta object code from reading C++ file 'wifihotspotcontroller.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.15)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../src/hardware/wifihotspotcontroller.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'wifihotspotcontroller.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.15. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_WiFiHotspotController_t {
    QByteArrayData data[17];
    char stringdata0[266];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_WiFiHotspotController_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_WiFiHotspotController_t qt_meta_stringdata_WiFiHotspotController = {
    {
QT_MOC_LITERAL(0, 0, 21), // "WiFiHotspotController"
QT_MOC_LITERAL(1, 22, 19), // "hotspotStateChanged"
QT_MOC_LITERAL(2, 42, 0), // ""
QT_MOC_LITERAL(3, 43, 8), // "isActive"
QT_MOC_LITERAL(4, 52, 7), // "message"
QT_MOC_LITERAL(5, 60, 10), // "logMessage"
QT_MOC_LITERAL(6, 71, 12), // "startHotspot"
QT_MOC_LITERAL(7, 84, 11), // "stopHotspot"
QT_MOC_LITERAL(8, 96, 17), // "onProcessFinished"
QT_MOC_LITERAL(9, 114, 8), // "exitCode"
QT_MOC_LITERAL(10, 123, 20), // "QProcess::ExitStatus"
QT_MOC_LITERAL(11, 144, 10), // "exitStatus"
QT_MOC_LITERAL(12, 155, 22), // "onProcessErrorOccurred"
QT_MOC_LITERAL(13, 178, 22), // "QProcess::ProcessError"
QT_MOC_LITERAL(14, 201, 5), // "error"
QT_MOC_LITERAL(15, 207, 29), // "handleReadyReadStandardOutput"
QT_MOC_LITERAL(16, 237, 28) // "handleReadyReadStandardError"

    },
    "WiFiHotspotController\0hotspotStateChanged\0"
    "\0isActive\0message\0logMessage\0startHotspot\0"
    "stopHotspot\0onProcessFinished\0exitCode\0"
    "QProcess::ExitStatus\0exitStatus\0"
    "onProcessErrorOccurred\0QProcess::ProcessError\0"
    "error\0handleReadyReadStandardOutput\0"
    "handleReadyReadStandardError"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_WiFiHotspotController[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       8,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   54,    2, 0x06 /* Public */,
       5,    1,   59,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       6,    0,   62,    2, 0x0a /* Public */,
       7,    0,   63,    2, 0x0a /* Public */,
       8,    2,   64,    2, 0x08 /* Private */,
      12,    1,   69,    2, 0x08 /* Private */,
      15,    0,   72,    2, 0x08 /* Private */,
      16,    0,   73,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::Bool, QMetaType::QString,    3,    4,
    QMetaType::Void, QMetaType::QString,    4,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, 0x80000000 | 10,    9,   11,
    QMetaType::Void, 0x80000000 | 13,   14,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void WiFiHotspotController::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<WiFiHotspotController *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->hotspotStateChanged((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 1: _t->logMessage((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->startHotspot(); break;
        case 3: _t->stopHotspot(); break;
        case 4: _t->onProcessFinished((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< QProcess::ExitStatus(*)>(_a[2]))); break;
        case 5: _t->onProcessErrorOccurred((*reinterpret_cast< QProcess::ProcessError(*)>(_a[1]))); break;
        case 6: _t->handleReadyReadStandardOutput(); break;
        case 7: _t->handleReadyReadStandardError(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (WiFiHotspotController::*)(bool , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&WiFiHotspotController::hotspotStateChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (WiFiHotspotController::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&WiFiHotspotController::logMessage)) {
                *result = 1;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject WiFiHotspotController::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_WiFiHotspotController.data,
    qt_meta_data_WiFiHotspotController,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *WiFiHotspotController::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *WiFiHotspotController::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_WiFiHotspotController.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int WiFiHotspotController::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 8;
    }
    return _id;
}

// SIGNAL 0
void WiFiHotspotController::hotspotStateChanged(bool _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void WiFiHotspotController::logMessage(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
