/****************************************************************************
** Meta object code from reading C++ file 'baidu_tts_client.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.15)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../src/speech/baidu_tts_client.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'baidu_tts_client.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.15. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_BaiduTTSClient_t {
    QByteArrayData data[14];
    char stringdata0[203];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_BaiduTTSClient_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_BaiduTTSClient_t qt_meta_stringdata_BaiduTTSClient = {
    {
QT_MOC_LITERAL(0, 0, 14), // "BaiduTTSClient"
QT_MOC_LITERAL(1, 15, 16), // "synthesisStarted"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 17), // "synthesisFinished"
QT_MOC_LITERAL(4, 51, 14), // "synthesisError"
QT_MOC_LITERAL(5, 66, 5), // "error"
QT_MOC_LITERAL(6, 72, 14), // "audioDataReady"
QT_MOC_LITERAL(7, 87, 9), // "audioData"
QT_MOC_LITERAL(8, 97, 19), // "ttsPlaybackFinished"
QT_MOC_LITERAL(9, 117, 20), // "onTokenReplyFinished"
QT_MOC_LITERAL(10, 138, 18), // "onTTSReplyFinished"
QT_MOC_LITERAL(11, 157, 25), // "onAudioOutputStateChanged"
QT_MOC_LITERAL(12, 183, 13), // "QAudio::State"
QT_MOC_LITERAL(13, 197, 5) // "state"

    },
    "BaiduTTSClient\0synthesisStarted\0\0"
    "synthesisFinished\0synthesisError\0error\0"
    "audioDataReady\0audioData\0ttsPlaybackFinished\0"
    "onTokenReplyFinished\0onTTSReplyFinished\0"
    "onAudioOutputStateChanged\0QAudio::State\0"
    "state"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_BaiduTTSClient[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       8,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   54,    2, 0x06 /* Public */,
       3,    0,   55,    2, 0x06 /* Public */,
       4,    1,   56,    2, 0x06 /* Public */,
       6,    1,   59,    2, 0x06 /* Public */,
       8,    0,   62,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       9,    0,   63,    2, 0x08 /* Private */,
      10,    0,   64,    2, 0x08 /* Private */,
      11,    1,   65,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void, QMetaType::QByteArray,    7,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 12,   13,

       0        // eod
};

void BaiduTTSClient::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<BaiduTTSClient *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->synthesisStarted(); break;
        case 1: _t->synthesisFinished(); break;
        case 2: _t->synthesisError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->audioDataReady((*reinterpret_cast< const QByteArray(*)>(_a[1]))); break;
        case 4: _t->ttsPlaybackFinished(); break;
        case 5: _t->onTokenReplyFinished(); break;
        case 6: _t->onTTSReplyFinished(); break;
        case 7: _t->onAudioOutputStateChanged((*reinterpret_cast< QAudio::State(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 7:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QAudio::State >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (BaiduTTSClient::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&BaiduTTSClient::synthesisStarted)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (BaiduTTSClient::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&BaiduTTSClient::synthesisFinished)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (BaiduTTSClient::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&BaiduTTSClient::synthesisError)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (BaiduTTSClient::*)(const QByteArray & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&BaiduTTSClient::audioDataReady)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (BaiduTTSClient::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&BaiduTTSClient::ttsPlaybackFinished)) {
                *result = 4;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject BaiduTTSClient::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_BaiduTTSClient.data,
    qt_meta_data_BaiduTTSClient,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *BaiduTTSClient::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *BaiduTTSClient::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_BaiduTTSClient.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int BaiduTTSClient::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    }
    return _id;
}

// SIGNAL 0
void BaiduTTSClient::synthesisStarted()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void BaiduTTSClient::synthesisFinished()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void BaiduTTSClient::synthesisError(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void BaiduTTSClient::audioDataReady(const QByteArray & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void BaiduTTSClient::ttsPlaybackFinished()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
