/****************************************************************************
** Meta object code from reading C++ file 'emotioncontroller.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.15)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../src/emotion/emotioncontroller.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'emotioncontroller.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.15. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_EmotionController_t {
    QByteArrayData data[24];
    char stringdata0[336];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_EmotionController_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_EmotionController_t qt_meta_stringdata_EmotionController = {
    {
QT_MOC_LITERAL(0, 0, 17), // "EmotionController"
QT_MOC_LITERAL(1, 18, 14), // "emotionChanged"
QT_MOC_LITERAL(2, 33, 0), // ""
QT_MOC_LITERAL(3, 34, 7), // "emotion"
QT_MOC_LITERAL(4, 42, 9), // "intensity"
QT_MOC_LITERAL(5, 52, 23), // "connectionStatusChanged"
QT_MOC_LITERAL(6, 76, 7), // "leftEye"
QT_MOC_LITERAL(7, 84, 8), // "rightEye"
QT_MOC_LITERAL(8, 93, 16), // "deviceTestResult"
QT_MOC_LITERAL(9, 110, 11), // "deviceIndex"
QT_MOC_LITERAL(10, 122, 7), // "success"
QT_MOC_LITERAL(11, 130, 10), // "setEmotion"
QT_MOC_LITERAL(12, 141, 11), // "EmotionType"
QT_MOC_LITERAL(13, 153, 12), // "setSleepMode"
QT_MOC_LITERAL(14, 166, 20), // "testDeviceConnection"
QT_MOC_LITERAL(15, 187, 12), // "setIntensity"
QT_MOC_LITERAL(16, 200, 13), // "setBrightness"
QT_MOC_LITERAL(17, 214, 10), // "brightness"
QT_MOC_LITERAL(18, 225, 18), // "isLeftEyeConnected"
QT_MOC_LITERAL(19, 244, 19), // "isRightEyeConnected"
QT_MOC_LITERAL(20, 264, 19), // "getConnectionStatus"
QT_MOC_LITERAL(21, 284, 21), // "onDeviceStatusChanged"
QT_MOC_LITERAL(22, 306, 6), // "online"
QT_MOC_LITERAL(23, 313, 22) // "onConnectionTestResult"

    },
    "EmotionController\0emotionChanged\0\0"
    "emotion\0intensity\0connectionStatusChanged\0"
    "leftEye\0rightEye\0deviceTestResult\0"
    "deviceIndex\0success\0setEmotion\0"
    "EmotionType\0setSleepMode\0testDeviceConnection\0"
    "setIntensity\0setBrightness\0brightness\0"
    "isLeftEyeConnected\0isRightEyeConnected\0"
    "getConnectionStatus\0onDeviceStatusChanged\0"
    "online\0onConnectionTestResult"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_EmotionController[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      16,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   94,    2, 0x06 /* Public */,
       5,    2,   99,    2, 0x06 /* Public */,
       8,    2,  104,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      11,    2,  109,    2, 0x0a /* Public */,
      11,    1,  114,    2, 0x2a /* Public | MethodCloned */,
      11,    2,  117,    2, 0x0a /* Public */,
      11,    1,  122,    2, 0x2a /* Public | MethodCloned */,
      13,    0,  125,    2, 0x0a /* Public */,
      14,    0,  126,    2, 0x0a /* Public */,
      15,    1,  127,    2, 0x0a /* Public */,
      16,    1,  130,    2, 0x0a /* Public */,
      18,    0,  133,    2, 0x0a /* Public */,
      19,    0,  134,    2, 0x0a /* Public */,
      20,    0,  135,    2, 0x0a /* Public */,
      21,    2,  136,    2, 0x08 /* Private */,
      23,    0,  141,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::Int,    3,    4,
    QMetaType::Void, QMetaType::Bool, QMetaType::Bool,    6,    7,
    QMetaType::Void, QMetaType::Int, QMetaType::Bool,    9,   10,

 // slots: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::Int,    3,    4,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, 0x80000000 | 12, QMetaType::Int,    3,    4,
    QMetaType::Void, 0x80000000 | 12,    3,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,    4,
    QMetaType::Void, QMetaType::Int,   17,
    QMetaType::Bool,
    QMetaType::Bool,
    QMetaType::QString,
    QMetaType::Void, QMetaType::Int, QMetaType::Bool,    9,   22,
    QMetaType::Void,

       0        // eod
};

void EmotionController::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<EmotionController *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->emotionChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 1: _t->connectionStatusChanged((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 2: _t->deviceTestResult((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 3: _t->setEmotion((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 4: _t->setEmotion((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 5: _t->setEmotion((*reinterpret_cast< EmotionType(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 6: _t->setEmotion((*reinterpret_cast< EmotionType(*)>(_a[1]))); break;
        case 7: _t->setSleepMode(); break;
        case 8: _t->testDeviceConnection(); break;
        case 9: _t->setIntensity((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 10: _t->setBrightness((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 11: { bool _r = _t->isLeftEyeConnected();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 12: { bool _r = _t->isRightEyeConnected();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 13: { QString _r = _t->getConnectionStatus();
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 14: _t->onDeviceStatusChanged((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 15: _t->onConnectionTestResult(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (EmotionController::*)(const QString & , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&EmotionController::emotionChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (EmotionController::*)(bool , bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&EmotionController::connectionStatusChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (EmotionController::*)(int , bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&EmotionController::deviceTestResult)) {
                *result = 2;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject EmotionController::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_EmotionController.data,
    qt_meta_data_EmotionController,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *EmotionController::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *EmotionController::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_EmotionController.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int EmotionController::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 16)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 16;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 16)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 16;
    }
    return _id;
}

// SIGNAL 0
void EmotionController::emotionChanged(const QString & _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void EmotionController::connectionStatusChanged(bool _t1, bool _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void EmotionController::deviceTestResult(int _t1, bool _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
