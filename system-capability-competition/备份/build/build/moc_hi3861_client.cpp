/****************************************************************************
** Meta object code from reading C++ file 'hi3861_client.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.15)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../src/hardware/hi3861_client.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'hi3861_client.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.15. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_Hi3861Client_t {
    QByteArrayData data[20];
    char stringdata0[251];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_Hi3861Client_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_Hi3861Client_t qt_meta_stringdata_Hi3861Client = {
    {
QT_MOC_LITERAL(0, 0, 12), // "Hi3861Client"
QT_MOC_LITERAL(1, 13, 9), // "connected"
QT_MOC_LITERAL(2, 23, 0), // ""
QT_MOC_LITERAL(3, 24, 12), // "disconnected"
QT_MOC_LITERAL(4, 37, 15), // "connectionError"
QT_MOC_LITERAL(5, 53, 5), // "error"
QT_MOC_LITERAL(6, 59, 11), // "emotionSent"
QT_MOC_LITERAL(7, 71, 11), // "EmotionType"
QT_MOC_LITERAL(8, 83, 7), // "emotion"
QT_MOC_LITERAL(9, 91, 9), // "intensity"
QT_MOC_LITERAL(10, 101, 17), // "heartbeatResponse"
QT_MOC_LITERAL(11, 119, 18), // "deviceInfoReceived"
QT_MOC_LITERAL(12, 138, 10), // "deviceType"
QT_MOC_LITERAL(13, 149, 6), // "status"
QT_MOC_LITERAL(14, 156, 11), // "onConnected"
QT_MOC_LITERAL(15, 168, 14), // "onDisconnected"
QT_MOC_LITERAL(16, 183, 11), // "onReadyRead"
QT_MOC_LITERAL(17, 195, 7), // "onError"
QT_MOC_LITERAL(18, 203, 28), // "QAbstractSocket::SocketError"
QT_MOC_LITERAL(19, 232, 18) // "sendHeartbeatTimer"

    },
    "Hi3861Client\0connected\0\0disconnected\0"
    "connectionError\0error\0emotionSent\0"
    "EmotionType\0emotion\0intensity\0"
    "heartbeatResponse\0deviceInfoReceived\0"
    "deviceType\0status\0onConnected\0"
    "onDisconnected\0onReadyRead\0onError\0"
    "QAbstractSocket::SocketError\0"
    "sendHeartbeatTimer"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_Hi3861Client[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      11,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       6,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   69,    2, 0x06 /* Public */,
       3,    0,   70,    2, 0x06 /* Public */,
       4,    1,   71,    2, 0x06 /* Public */,
       6,    2,   74,    2, 0x06 /* Public */,
      10,    0,   79,    2, 0x06 /* Public */,
      11,    2,   80,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      14,    0,   85,    2, 0x08 /* Private */,
      15,    0,   86,    2, 0x08 /* Private */,
      16,    0,   87,    2, 0x08 /* Private */,
      17,    1,   88,    2, 0x08 /* Private */,
      19,    0,   91,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void, 0x80000000 | 7, QMetaType::Int,    8,    9,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   12,   13,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 18,    5,
    QMetaType::Void,

       0        // eod
};

void Hi3861Client::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<Hi3861Client *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->connected(); break;
        case 1: _t->disconnected(); break;
        case 2: _t->connectionError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->emotionSent((*reinterpret_cast< EmotionType(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 4: _t->heartbeatResponse(); break;
        case 5: _t->deviceInfoReceived((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 6: _t->onConnected(); break;
        case 7: _t->onDisconnected(); break;
        case 8: _t->onReadyRead(); break;
        case 9: _t->onError((*reinterpret_cast< QAbstractSocket::SocketError(*)>(_a[1]))); break;
        case 10: _t->sendHeartbeatTimer(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 9:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QAbstractSocket::SocketError >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (Hi3861Client::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Hi3861Client::connected)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (Hi3861Client::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Hi3861Client::disconnected)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (Hi3861Client::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Hi3861Client::connectionError)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (Hi3861Client::*)(EmotionType , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Hi3861Client::emotionSent)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (Hi3861Client::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Hi3861Client::heartbeatResponse)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (Hi3861Client::*)(const QString & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&Hi3861Client::deviceInfoReceived)) {
                *result = 5;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject Hi3861Client::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_Hi3861Client.data,
    qt_meta_data_Hi3861Client,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *Hi3861Client::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *Hi3861Client::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_Hi3861Client.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int Hi3861Client::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    }
    return _id;
}

// SIGNAL 0
void Hi3861Client::connected()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void Hi3861Client::disconnected()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void Hi3861Client::connectionError(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void Hi3861Client::emotionSent(EmotionType _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void Hi3861Client::heartbeatResponse()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void Hi3861Client::deviceInfoReceived(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
