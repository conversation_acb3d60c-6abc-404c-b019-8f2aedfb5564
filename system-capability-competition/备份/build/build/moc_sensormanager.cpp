/****************************************************************************
** Meta object code from reading C++ file 'sensormanager.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.15)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../src/sensor/sensormanager.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'sensormanager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.15. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_SensorManager_t {
    QByteArrayData data[20];
    char stringdata0[253];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_SensorManager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_SensorManager_t qt_meta_stringdata_SensorManager = {
    {
QT_MOC_LITERAL(0, 0, 13), // "SensorManager"
QT_MOC_LITERAL(1, 14, 18), // "temperatureChanged"
QT_MOC_LITERAL(2, 33, 0), // ""
QT_MOC_LITERAL(3, 34, 11), // "temperature"
QT_MOC_LITERAL(4, 46, 15), // "humidityChanged"
QT_MOC_LITERAL(5, 62, 8), // "humidity"
QT_MOC_LITERAL(6, 71, 17), // "lightLevelChanged"
QT_MOC_LITERAL(7, 89, 5), // "level"
QT_MOC_LITERAL(8, 95, 12), // "lightChanged"
QT_MOC_LITERAL(9, 108, 17), // "sensorDataUpdated"
QT_MOC_LITERAL(10, 126, 10), // "SensorData"
QT_MOC_LITERAL(11, 137, 4), // "data"
QT_MOC_LITERAL(12, 142, 11), // "sensorError"
QT_MOC_LITERAL(13, 154, 5), // "error"
QT_MOC_LITERAL(14, 160, 15), // "startMonitoring"
QT_MOC_LITERAL(15, 176, 14), // "stopMonitoring"
QT_MOC_LITERAL(16, 191, 16), // "calibrateSensors"
QT_MOC_LITERAL(17, 208, 19), // "setSamplingInterval"
QT_MOC_LITERAL(18, 228, 12), // "milliseconds"
QT_MOC_LITERAL(19, 241, 11) // "readSensors"

    },
    "SensorManager\0temperatureChanged\0\0"
    "temperature\0humidityChanged\0humidity\0"
    "lightLevelChanged\0level\0lightChanged\0"
    "sensorDataUpdated\0SensorData\0data\0"
    "sensorError\0error\0startMonitoring\0"
    "stopMonitoring\0calibrateSensors\0"
    "setSamplingInterval\0milliseconds\0"
    "readSensors"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_SensorManager[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      11,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       6,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   69,    2, 0x06 /* Public */,
       4,    1,   72,    2, 0x06 /* Public */,
       6,    1,   75,    2, 0x06 /* Public */,
       8,    1,   78,    2, 0x06 /* Public */,
       9,    1,   81,    2, 0x06 /* Public */,
      12,    1,   84,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      14,    0,   87,    2, 0x0a /* Public */,
      15,    0,   88,    2, 0x0a /* Public */,
      16,    0,   89,    2, 0x0a /* Public */,
      17,    1,   90,    2, 0x0a /* Public */,
      19,    0,   93,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::Double,    3,
    QMetaType::Void, QMetaType::Double,    5,
    QMetaType::Void, QMetaType::Int,    7,
    QMetaType::Void, QMetaType::Int,    7,
    QMetaType::Void, 0x80000000 | 10,   11,
    QMetaType::Void, QMetaType::QString,   13,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   18,
    QMetaType::Void,

       0        // eod
};

void SensorManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<SensorManager *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->temperatureChanged((*reinterpret_cast< double(*)>(_a[1]))); break;
        case 1: _t->humidityChanged((*reinterpret_cast< double(*)>(_a[1]))); break;
        case 2: _t->lightLevelChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 3: _t->lightChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 4: _t->sensorDataUpdated((*reinterpret_cast< const SensorData(*)>(_a[1]))); break;
        case 5: _t->sensorError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 6: _t->startMonitoring(); break;
        case 7: _t->stopMonitoring(); break;
        case 8: _t->calibrateSensors(); break;
        case 9: _t->setSamplingInterval((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 10: _t->readSensors(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (SensorManager::*)(double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SensorManager::temperatureChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (SensorManager::*)(double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SensorManager::humidityChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (SensorManager::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SensorManager::lightLevelChanged)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (SensorManager::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SensorManager::lightChanged)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (SensorManager::*)(const SensorData & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SensorManager::sensorDataUpdated)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (SensorManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SensorManager::sensorError)) {
                *result = 5;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject SensorManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_SensorManager.data,
    qt_meta_data_SensorManager,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *SensorManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SensorManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_SensorManager.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int SensorManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 11)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 11;
    }
    return _id;
}

// SIGNAL 0
void SensorManager::temperatureChanged(double _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void SensorManager::humidityChanged(double _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void SensorManager::lightLevelChanged(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void SensorManager::lightChanged(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void SensorManager::sensorDataUpdated(const SensorData & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void SensorManager::sensorError(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
