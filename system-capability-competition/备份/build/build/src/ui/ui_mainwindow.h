/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 5.15.15
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSlider>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QStackedWidget>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralwidget;
    QVBoxLayout *verticalLayout_main;
    QLabel *label_header_title;
    QStackedWidget *stackedWidget_content;
    QWidget *page_speech;
    QVBoxLayout *verticalLayout_speech_page;
    QGroupBox *groupBox_music_player;
    QVBoxLayout *verticalLayout_music_player;
    QLabel *label_current_song;
    QHBoxLayout *horizontalLayout_music_controls;
    QPushButton *pushButton_pause_music;
    QPushButton *pushButton_stop_music;
    QTextEdit *textEdit_conversation;
    QLabel *label_conversation_status;
    QSpacerItem *verticalSpacer_speech;
    QHBoxLayout *horizontalLayout_voice_controls;
    QSpacerItem *horizontalSpacer_left;
    QPushButton *pushButton_start_conversation;
    QSpacerItem *horizontalSpacer_right;
    QPushButton *pushButton_stop_reply;
    QWidget *page_emotion;
    QVBoxLayout *verticalLayout_emotion_page;
    QLabel *label_emotion_title;
    QGridLayout *gridLayout_emotions;
    QPushButton *pushButton_happy;
    QPushButton *pushButton_sad;
    QPushButton *pushButton_angry;
    QPushButton *pushButton_surprised;
    QPushButton *pushButton_fear;
    QPushButton *pushButton_neutral;
    QLabel *label_current_emotion;
    QLabel *label_device_status;
    QHBoxLayout *horizontalLayout_display_status;
    QFrame *frame_left_eye;
    QVBoxLayout *verticalLayout_left_eye;
    QLabel *label_left_eye_title;
    QLabel *label_left_eye_ip;
    QLabel *label_left_eye_status;
    QFrame *frame_right_eye;
    QVBoxLayout *verticalLayout_right_eye;
    QLabel *label_right_eye_title;
    QLabel *label_right_eye_ip;
    QLabel *label_right_eye_status;
    QGridLayout *gridLayout_hi3861_config;
    QLabel *label_left_eye_ip1;
    QHBoxLayout *horizontalLayout_left_eye_ip;
    QLineEdit *lineEdit_left_eye_ip;
    QPushButton *pushButton_keyboard_left;
    QPushButton *pushButton_connect_left_eye;
    QLabel *label_right_eye_ip1;
    QHBoxLayout *horizontalLayout_right_eye_ip;
    QLineEdit *lineEdit_right_eye_ip;
    QPushButton *pushButton_keyboard_right;
    QPushButton *pushButton_connect_right_eye;
    QHBoxLayout *horizontalLayout_hi3861_status;
    QLabel *label_left_eye_status1;
    QLabel *label_right_eye_status1;
    QSpacerItem *verticalSpacer_emotion;
    QWidget *page_sensor;
    QVBoxLayout *verticalLayout_sensor_page;
    QLabel *label_sensor_title;
    QHBoxLayout *horizontalLayout_city_select;
    QLabel *label_city;
    QComboBox *comboBox_city;
    QVBoxLayout *verticalLayout_city_slider;
    QLabel *label_city_slider;
    QSlider *horizontalSlider_city;
    QLabel *label_city_display;
    QGridLayout *gridLayout_sensor_data;
    QLabel *label_weather_title;
    QLabel *label_weather;
    QLabel *label_temp_title;
    QLabel *label_temperature;
    QLabel *label_humidity_title;
    QLabel *label_humidity;
    QLabel *label_wind_title;
    QLabel *label_wind;
    QHBoxLayout *horizontalLayout_sensor_buttons;
    QPushButton *pushButton_refresh_weather;
    QGroupBox *hardwareTestGroupBox;
    QHBoxLayout *horizontalLayout_hardware_test;
    QPushButton *testSpeakerButton;
    QPushButton *testMicrophoneButton;
    QPushButton *testCameraButton;
    QSpacerItem *verticalSpacer_sensor;
    QWidget *page_camera;
    QVBoxLayout *verticalLayout_camera_page;
    QLabel *label_camera_title;
    QLabel *label_camera_display;
    QHBoxLayout *horizontalLayout_camera_info;
    QLabel *label_camera_status;
    QLabel *label_camera_resolution;
    QLabel *label_camera_fps;
    QHBoxLayout *horizontalLayout_camera_buttons;
    QPushButton *pushButton_camera_start;
    QPushButton *pushButton_camera_stop;
    QPushButton *pushButton_camera_snapshot;
    QSpacerItem *verticalSpacer_camera;
    QWidget *page_system;
    QVBoxLayout *verticalLayout_system_page;
    QLabel *label_system_title;
    QLabel *label_system_status;
    QHBoxLayout *horizontalLayout_debug;
    QLabel *label_debug_mode;
    QComboBox *comboBox_debug_level;
    QTextEdit *textEdit_logs;
    QHBoxLayout *horizontalLayout_system_buttons;
    QPushButton *pushButton_reconnect;
    QPushButton *pushButton_settings;
    QSpacerItem *verticalSpacer_system;
    QWidget *navWidget;
    QHBoxLayout *horizontalLayout_nav;
    QPushButton *pushButton_nav_speech;
    QPushButton *pushButton_nav_emotion;
    QPushButton *pushButton_nav_sensor;
    QPushButton *pushButton_nav_camera;
    QPushButton *pushButton_nav_system;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName(QString::fromUtf8("MainWindow"));
        MainWindow->resize(1080, 1850);
        MainWindow->setMinimumSize(QSize(1080, 1850));
        MainWindow->setMaximumSize(QSize(1080, 1850));
        MainWindow->setStyleSheet(QString::fromUtf8("QMainWindow {\n"
"    background-color: #f5f5f5;\n"
"    color: #333;\n"
"}\n"
"\n"
"/* \344\270\273\350\246\201\345\206\205\345\256\271\345\214\272\345\237\237 */\n"
"QWidget#contentWidget {\n"
"    background-color: white;\n"
"}\n"
"\n"
"/* \346\240\207\351\242\230\346\240\207\347\255\276 */\n"
"QLabel.title-label {\n"
"    font-size: 42px;\n"
"    font-weight: bold;\n"
"    color: #2c3e50;\n"
"    padding: 10px;\n"
"    margin-bottom: 15px;\n"
"}\n"
"\n"
"/* \351\200\232\347\224\250\346\240\207\347\255\276 */\n"
"QLabel {\n"
"    color: #333;\n"
"    font-size: 36px;\n"
"    background-color: transparent;\n"
"    padding: 5px;\n"
"}\n"
"\n"
"/* \351\200\232\347\224\250\346\214\211\351\222\256 */\n"
"QPushButton {\n"
"    background-color: #3498db;\n"
"    border: none;\n"
"    border-radius: 12px;\n"
"    padding: 20px 30px;\n"
"    color: white;\n"
"    font-weight: bold;\n"
"    font-size: 38px;\n"
"    min-height: 100px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #2980b9;\n"
"}\n"
"\n"
""
                        "QPushButton:pressed {\n"
"    background-color: #1f639a;\n"
"}\n"
"\n"
"QPushButton:disabled {\n"
"    background-color: #bdc3c7;\n"
"    color: #7f8c8d;\n"
"}\n"
"\n"
"/* \345\272\225\351\203\250\345\257\274\350\210\252\346\240\217 */\n"
"QWidget#navWidget {\n"
"    background-color: #2c3e50;\n"
"    border-top: 2px solid #34495e;\n"
"    min-height: 150px;\n"
"    max-height: 150px;\n"
"}\n"
"\n"
"/* \345\272\225\351\203\250\345\257\274\350\210\252\346\214\211\351\222\256 */\n"
"#navWidget QPushButton {\n"
"    background-color: transparent;\n"
"    border: none;\n"
"    color: #ecf0f1;\n"
"    text-align: center;\n"
"    padding: 15px 5px;\n"
"    font-size: 36px;\n"
"    font-weight: bold;\n"
"    border-bottom: 6px solid transparent; /* \346\234\252\351\200\211\344\270\255\346\227\266\351\200\217\346\230\216\350\276\271\346\241\206 */\n"
"}\n"
"\n"
"#navWidget QPushButton:hover {\n"
"    background-color: #34495e;\n"
"}\n"
"\n"
"#navWidget QPushButton:pressed {\n"
"    background-color: #3498db;\n"
"}\n"
""
                        "\n"
"#navWidget QPushButton:checked {\n"
"    color: white;\n"
"    background-color: #3498db;\n"
"    border-bottom: 6px solid #e74c3c; /* \351\200\211\344\270\255\346\227\266\351\253\230\344\272\256\350\276\271\346\241\206 */\n"
"}\n"
""));
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
        verticalLayout_main = new QVBoxLayout(centralwidget);
        verticalLayout_main->setSpacing(0);
        verticalLayout_main->setObjectName(QString::fromUtf8("verticalLayout_main"));
        verticalLayout_main->setContentsMargins(0, 0, 0, 0);
        label_header_title = new QLabel(centralwidget);
        label_header_title->setObjectName(QString::fromUtf8("label_header_title"));
        label_header_title->setAlignment(Qt::AlignCenter);
        label_header_title->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    font-size: 76px;\n"
"    font-weight: 900;\n"
"    color: white;\n"
"    padding: 35px 15px;\n"
"    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,\n"
"        stop:0 #667eea, stop:0.3 #764ba2, stop:0.7 #f093fb, stop:1 #f5576c);\n"
"    border-bottom: 6px solid #e74c3c;\n"
"    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\n"
"}"));

        verticalLayout_main->addWidget(label_header_title);

        stackedWidget_content = new QStackedWidget(centralwidget);
        stackedWidget_content->setObjectName(QString::fromUtf8("stackedWidget_content"));
        page_speech = new QWidget();
        page_speech->setObjectName(QString::fromUtf8("page_speech"));
        verticalLayout_speech_page = new QVBoxLayout(page_speech);
        verticalLayout_speech_page->setSpacing(3);
        verticalLayout_speech_page->setObjectName(QString::fromUtf8("verticalLayout_speech_page"));
        verticalLayout_speech_page->setContentsMargins(5, 15, 5, 5);
        groupBox_music_player = new QGroupBox(page_speech);
        groupBox_music_player->setObjectName(QString::fromUtf8("groupBox_music_player"));
        groupBox_music_player->setVisible(false);
        groupBox_music_player->setStyleSheet(QString::fromUtf8("QGroupBox {\n"
"    font-size: 24px;\n"
"    font-weight: bold;\n"
"    color: #2c3e50;\n"
"    border: 3px solid #74b9ff;\n"
"    border-radius: 15px;\n"
"    margin-top: 10px;\n"
"    padding-top: 10px;\n"
"}\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 5px 10px;\n"
"    background: white;\n"
"    border-radius: 5px;\n"
"}"));
        verticalLayout_music_player = new QVBoxLayout(groupBox_music_player);
        verticalLayout_music_player->setObjectName(QString::fromUtf8("verticalLayout_music_player"));
        label_current_song = new QLabel(groupBox_music_player);
        label_current_song->setObjectName(QString::fromUtf8("label_current_song"));
        label_current_song->setAlignment(Qt::AlignCenter);
        label_current_song->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    font-size: 18px;\n"
"    color: #2c3e50;\n"
"    padding: 10px;\n"
"    background: rgba(116, 185, 255, 0.1);\n"
"    border-radius: 10px;\n"
"}"));

        verticalLayout_music_player->addWidget(label_current_song);

        horizontalLayout_music_controls = new QHBoxLayout();
        horizontalLayout_music_controls->setObjectName(QString::fromUtf8("horizontalLayout_music_controls"));
        pushButton_pause_music = new QPushButton(groupBox_music_player);
        pushButton_pause_music->setObjectName(QString::fromUtf8("pushButton_pause_music"));
        pushButton_pause_music->setMinimumSize(QSize(100, 50));
        pushButton_pause_music->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"        stop:0 #fdcb6e, stop:1 #e17055);\n"
"    border: none;\n"
"    border-radius: 25px;\n"
"    color: white;\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"        stop:0 #e17055, stop:1 #d63031);\n"
"}"));

        horizontalLayout_music_controls->addWidget(pushButton_pause_music);

        pushButton_stop_music = new QPushButton(groupBox_music_player);
        pushButton_stop_music->setObjectName(QString::fromUtf8("pushButton_stop_music"));
        pushButton_stop_music->setMinimumSize(QSize(100, 50));
        pushButton_stop_music->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"        stop:0 #e17055, stop:1 #d63031);\n"
"    border: none;\n"
"    border-radius: 25px;\n"
"    color: white;\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"        stop:0 #d63031, stop:1 #c92a2a);\n"
"}"));

        horizontalLayout_music_controls->addWidget(pushButton_stop_music);


        verticalLayout_music_player->addLayout(horizontalLayout_music_controls);


        verticalLayout_speech_page->addWidget(groupBox_music_player);

        textEdit_conversation = new QTextEdit(page_speech);
        textEdit_conversation->setObjectName(QString::fromUtf8("textEdit_conversation"));
        textEdit_conversation->setMinimumSize(QSize(0, 900));
        textEdit_conversation->setMaximumSize(QSize(16777215, 1350));
        textEdit_conversation->setReadOnly(true);
        textEdit_conversation->setStyleSheet(QString::fromUtf8("QTextEdit {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"        stop:0 #1e3c72, stop:1 #2a5298);\n"
"    border: 3px solid #3498db;\n"
"    border-radius: 20px;\n"
"    color: #ffffff;\n"
"    font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;\n"
"    font-size: 24px;\n"
"    padding: 20px;\n"
"    selection-background-color: #e74c3c;\n"
"    selection-color: white;\n"
"    line-height: 1.6;\n"
"}\n"
"\n"
"QTextEdit:focus {\n"
"    border-color: #e74c3c;\n"
"    box-shadow: 0 0 20px rgba(231, 76, 60, 0.5);\n"
"    outline: none;\n"
"}\n"
"\n"
"QScrollBar:vertical {\n"
"    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n"
"        stop:0 #34495e, stop:1 #2c3e50);\n"
"    width: 16px;\n"
"    border-radius: 8px;\n"
"    margin: 0;\n"
"}\n"
"\n"
"QScrollBar::handle:vertical {\n"
"    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n"
"        stop:0 #3498db, stop:1 #2980b9);\n"
"    border-radius: 8px;\n"
"    min-height: 30px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QScrollBar::"
                        "handle:vertical:hover {\n"
"    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n"
"        stop:0 #e74c3c, stop:1 #c0392b);\n"
"}\n"
"\n"
"QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {\n"
"    border: none;\n"
"    background: none;\n"
"}\n"
"\n"
"QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {\n"
"    background: none;\n"
"}"));

        verticalLayout_speech_page->addWidget(textEdit_conversation);

        label_conversation_status = new QLabel(page_speech);
        label_conversation_status->setObjectName(QString::fromUtf8("label_conversation_status"));
        label_conversation_status->setAlignment(Qt::AlignCenter);
        label_conversation_status->setMinimumSize(QSize(0, 50));
        label_conversation_status->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,\n"
"        stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);\n"
"    border: 3px solid #9b59b6;\n"
"    border-radius: 25px;\n"
"    padding: 15px 30px;\n"
"    font-size: 20px;\n"
"    font-weight: bold;\n"
"    color: white;\n"
"    box-shadow: 0 6px 20px rgba(155, 89, 182, 0.3);\n"
"}"));

        verticalLayout_speech_page->addWidget(label_conversation_status);

        verticalSpacer_speech = new QSpacerItem(20, 5, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_speech_page->addItem(verticalSpacer_speech);

        horizontalLayout_voice_controls = new QHBoxLayout();
        horizontalLayout_voice_controls->setSpacing(12);
        horizontalLayout_voice_controls->setObjectName(QString::fromUtf8("horizontalLayout_voice_controls"));
        horizontalSpacer_left = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_voice_controls->addItem(horizontalSpacer_left);

        pushButton_start_conversation = new QPushButton(page_speech);
        pushButton_start_conversation->setObjectName(QString::fromUtf8("pushButton_start_conversation"));
        pushButton_start_conversation->setMinimumSize(QSize(360, 75));
        pushButton_start_conversation->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"        stop:0 #667eea, stop:1 #764ba2);\n"
"    border: 3px solid #9b59b6;\n"
"    border-radius: 37px;\n"
"    color: white;\n"
"    font-size: 40px;\n"
"    font-weight: bold;\n"
"    padding: 45px 60px;\n"
"    box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4);\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"        stop:0 #f093fb, stop:1 #f5576c);\n"
"    border-color: #e74c3c;\n"
"    transform: scale(1.05);\n"
"    box-shadow: 0 12px 35px rgba(231, 76, 60, 0.6);\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"        stop:0 #4facfe, stop:1 #00f2fe);\n"
"    border-color: #3498db;\n"
"    transform: scale(0.95);\n"
"    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.8);\n"
"}\n"
"\n"
"QPushButton:disabled {\n"
"    background: linear-gradient(135deg, #bdc3c7, #95a5a6);\n"
"    border-color: #7f8c8d;\n"
"    color: #ecf0f1;\n"
"    "
                        "box-shadow: none;\n"
"}"));

        horizontalLayout_voice_controls->addWidget(pushButton_start_conversation);

        horizontalSpacer_right = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_voice_controls->addItem(horizontalSpacer_right);


        verticalLayout_speech_page->addLayout(horizontalLayout_voice_controls);

        pushButton_stop_reply = new QPushButton(page_speech);
        pushButton_stop_reply->setObjectName(QString::fromUtf8("pushButton_stop_reply"));
        pushButton_stop_reply->setVisible(false);
        pushButton_stop_reply->setMinimumSize(QSize(0, 135));
        pushButton_stop_reply->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"        stop:0 #e17055, stop:1 #d63031);\n"
"    border: none;\n"
"    border-radius: 66px;\n"
"    color: white;\n"
"    font-size: 33px;\n"
"    font-weight: bold;\n"
"    padding: 36px 48px;\n"
"    margin: 0px 60px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"        stop:0 #d63031, stop:1 #c92a2a);\n"
"    transform: scale(1.02);\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"        stop:0 #c92a2a, stop:1 #b71c1c);\n"
"    transform: scale(0.98);\n"
"}"));

        verticalLayout_speech_page->addWidget(pushButton_stop_reply);

        stackedWidget_content->addWidget(page_speech);
        page_emotion = new QWidget();
        page_emotion->setObjectName(QString::fromUtf8("page_emotion"));
        verticalLayout_emotion_page = new QVBoxLayout(page_emotion);
        verticalLayout_emotion_page->setSpacing(3);
        verticalLayout_emotion_page->setObjectName(QString::fromUtf8("verticalLayout_emotion_page"));
        verticalLayout_emotion_page->setContentsMargins(5, 5, 5, 5);
        label_emotion_title = new QLabel(page_emotion);
        label_emotion_title->setObjectName(QString::fromUtf8("label_emotion_title"));
        label_emotion_title->setAlignment(Qt::AlignCenter);
        label_emotion_title->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    font-size: 42px;\n"
"    font-weight: bold;\n"
"    color: white;\n"
"    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n"
"        stop:0 #667eea, stop:1 #764ba2);\n"
"    padding: 36px;\n"
"    border-radius: 24px;\n"
"    margin-bottom: 24px;\n"
"}"));

        verticalLayout_emotion_page->addWidget(label_emotion_title);

        gridLayout_emotions = new QGridLayout();
        gridLayout_emotions->setSpacing(20);
        gridLayout_emotions->setObjectName(QString::fromUtf8("gridLayout_emotions"));
        pushButton_happy = new QPushButton(page_emotion);
        pushButton_happy->setObjectName(QString::fromUtf8("pushButton_happy"));
        pushButton_happy->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    background-color: #f39c12;\n"
"    border-color: #e67e22;\n"
"    font-size: 38px;\n"
"    min-height: 120px;\n"
"}\n"
"QPushButton:hover { background-color: #e67e22; }\n"
"QPushButton:pressed { background-color: #d35400; }"));

        gridLayout_emotions->addWidget(pushButton_happy, 0, 0, 1, 1);

        pushButton_sad = new QPushButton(page_emotion);
        pushButton_sad->setObjectName(QString::fromUtf8("pushButton_sad"));
        pushButton_sad->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    background-color: #3498db;\n"
"    border-color: #2980b9;\n"
"    font-size: 38px;\n"
"    min-height: 120px;\n"
"}\n"
"QPushButton:hover { background-color: #2980b9; }\n"
"QPushButton:pressed { background-color: #1f639a; }"));

        gridLayout_emotions->addWidget(pushButton_sad, 0, 1, 1, 1);

        pushButton_angry = new QPushButton(page_emotion);
        pushButton_angry->setObjectName(QString::fromUtf8("pushButton_angry"));
        pushButton_angry->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    background-color: #e74c3c;\n"
"    border-color: #c0392b;\n"
"    font-size: 38px;\n"
"    min-height: 120px;\n"
"}\n"
"QPushButton:hover { background-color: #c0392b; }\n"
"QPushButton:pressed { background-color: #a93226; }"));

        gridLayout_emotions->addWidget(pushButton_angry, 0, 2, 1, 1);

        pushButton_surprised = new QPushButton(page_emotion);
        pushButton_surprised->setObjectName(QString::fromUtf8("pushButton_surprised"));
        pushButton_surprised->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    background-color: #9b59b6;\n"
"    border-color: #8e44ad;\n"
"    font-size: 38px;\n"
"    min-height: 120px;\n"
"}\n"
"QPushButton:hover { background-color: #8e44ad; }\n"
"QPushButton:pressed { background-color: #7d3c98; }"));

        gridLayout_emotions->addWidget(pushButton_surprised, 1, 0, 1, 1);

        pushButton_fear = new QPushButton(page_emotion);
        pushButton_fear->setObjectName(QString::fromUtf8("pushButton_fear"));
        pushButton_fear->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    background-color: #663399;\n"
"    border-color: #552288;\n"
"    font-size: 38px;\n"
"    min-height: 120px;\n"
"}\n"
"QPushButton:hover { background-color: #552288; }\n"
"QPushButton:pressed { background-color: #441177; }"));

        gridLayout_emotions->addWidget(pushButton_fear, 1, 1, 1, 1);

        pushButton_neutral = new QPushButton(page_emotion);
        pushButton_neutral->setObjectName(QString::fromUtf8("pushButton_neutral"));
        pushButton_neutral->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    background-color: #95a5a6;\n"
"    border-color: #7f8c8d;\n"
"    font-size: 38px;\n"
"    min-height: 120px;\n"
"}\n"
"QPushButton:hover { background-color: #7f8c8d; }\n"
"QPushButton:pressed { background-color: #6c7b7d; }"));

        gridLayout_emotions->addWidget(pushButton_neutral, 1, 2, 1, 1);


        verticalLayout_emotion_page->addLayout(gridLayout_emotions);

        label_current_emotion = new QLabel(page_emotion);
        label_current_emotion->setObjectName(QString::fromUtf8("label_current_emotion"));
        label_current_emotion->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #2c3e50;\n"
"    font-weight: bold;\n"
"    font-size: 38px;\n"
"    padding: 20px;\n"
"    margin-top: 20px;\n"
"    background-color: #e9ecef;\n"
"    border-radius: 12px;\n"
"}"));

        verticalLayout_emotion_page->addWidget(label_current_emotion);

        label_device_status = new QLabel(page_emotion);
        label_device_status->setObjectName(QString::fromUtf8("label_device_status"));
        label_device_status->setAlignment(Qt::AlignCenter);
        label_device_status->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #f39c12;\n"
"    font-weight: bold;\n"
"    font-size: 32px;\n"
"    padding: 15px;\n"
"    margin-top: 10px;\n"
"    background-color: #fef9e7;\n"
"    border: 2px solid #f39c12;\n"
"    border-radius: 10px;\n"
"}"));

        verticalLayout_emotion_page->addWidget(label_device_status);

        horizontalLayout_display_status = new QHBoxLayout();
        horizontalLayout_display_status->setSpacing(20);
        horizontalLayout_display_status->setObjectName(QString::fromUtf8("horizontalLayout_display_status"));
        frame_left_eye = new QFrame(page_emotion);
        frame_left_eye->setObjectName(QString::fromUtf8("frame_left_eye"));
        frame_left_eye->setMinimumSize(QSize(200, 150));
        frame_left_eye->setMaximumSize(QSize(250, 180));
        frame_left_eye->setStyleSheet(QString::fromUtf8("QFrame {\n"
"    background-color: #95a5a6;\n"
"    border: 3px solid #7f8c8d;\n"
"    border-radius: 15px;\n"
"    margin: 5px;\n"
"}"));
        frame_left_eye->setFrameShape(QFrame::Box);
        verticalLayout_left_eye = new QVBoxLayout(frame_left_eye);
        verticalLayout_left_eye->setSpacing(8);
        verticalLayout_left_eye->setObjectName(QString::fromUtf8("verticalLayout_left_eye"));
        verticalLayout_left_eye->setContentsMargins(10, 10, 10, 10);
        label_left_eye_title = new QLabel(frame_left_eye);
        label_left_eye_title->setObjectName(QString::fromUtf8("label_left_eye_title"));
        label_left_eye_title->setAlignment(Qt::AlignCenter);
        label_left_eye_title->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #2c3e50;\n"
"    font-weight: bold;\n"
"    font-size: 28px;\n"
"    background-color: transparent;\n"
"    border: none;\n"
"    margin: 0px;\n"
"    padding: 5px;\n"
"}"));

        verticalLayout_left_eye->addWidget(label_left_eye_title);

        label_left_eye_ip = new QLabel(frame_left_eye);
        label_left_eye_ip->setObjectName(QString::fromUtf8("label_left_eye_ip"));
        label_left_eye_ip->setAlignment(Qt::AlignCenter);
        label_left_eye_ip->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #34495e;\n"
"    font-size: 22px;\n"
"    background-color: transparent;\n"
"    border: none;\n"
"    margin: 0px;\n"
"    padding: 3px;\n"
"}"));

        verticalLayout_left_eye->addWidget(label_left_eye_ip);

        label_left_eye_status = new QLabel(frame_left_eye);
        label_left_eye_status->setObjectName(QString::fromUtf8("label_left_eye_status"));
        label_left_eye_status->setAlignment(Qt::AlignCenter);
        label_left_eye_status->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #e74c3c;\n"
"    font-weight: bold;\n"
"    font-size: 24px;\n"
"    background-color: transparent;\n"
"    border: none;\n"
"    margin: 0px;\n"
"    padding: 5px;\n"
"}"));

        verticalLayout_left_eye->addWidget(label_left_eye_status);


        horizontalLayout_display_status->addWidget(frame_left_eye);

        frame_right_eye = new QFrame(page_emotion);
        frame_right_eye->setObjectName(QString::fromUtf8("frame_right_eye"));
        frame_right_eye->setMinimumSize(QSize(200, 150));
        frame_right_eye->setMaximumSize(QSize(250, 180));
        frame_right_eye->setStyleSheet(QString::fromUtf8("QFrame {\n"
"    background-color: #95a5a6;\n"
"    border: 3px solid #7f8c8d;\n"
"    border-radius: 15px;\n"
"    margin: 5px;\n"
"}"));
        frame_right_eye->setFrameShape(QFrame::Box);
        verticalLayout_right_eye = new QVBoxLayout(frame_right_eye);
        verticalLayout_right_eye->setSpacing(8);
        verticalLayout_right_eye->setObjectName(QString::fromUtf8("verticalLayout_right_eye"));
        verticalLayout_right_eye->setContentsMargins(10, 10, 10, 10);
        label_right_eye_title = new QLabel(frame_right_eye);
        label_right_eye_title->setObjectName(QString::fromUtf8("label_right_eye_title"));
        label_right_eye_title->setAlignment(Qt::AlignCenter);
        label_right_eye_title->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #2c3e50;\n"
"    font-weight: bold;\n"
"    font-size: 28px;\n"
"    background-color: transparent;\n"
"    border: none;\n"
"    margin: 0px;\n"
"    padding: 5px;\n"
"}"));

        verticalLayout_right_eye->addWidget(label_right_eye_title);

        label_right_eye_ip = new QLabel(frame_right_eye);
        label_right_eye_ip->setObjectName(QString::fromUtf8("label_right_eye_ip"));
        label_right_eye_ip->setAlignment(Qt::AlignCenter);
        label_right_eye_ip->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #34495e;\n"
"    font-size: 22px;\n"
"    background-color: transparent;\n"
"    border: none;\n"
"    margin: 0px;\n"
"    padding: 3px;\n"
"}"));

        verticalLayout_right_eye->addWidget(label_right_eye_ip);

        label_right_eye_status = new QLabel(frame_right_eye);
        label_right_eye_status->setObjectName(QString::fromUtf8("label_right_eye_status"));
        label_right_eye_status->setAlignment(Qt::AlignCenter);
        label_right_eye_status->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #e74c3c;\n"
"    font-weight: bold;\n"
"    font-size: 24px;\n"
"    background-color: transparent;\n"
"    border: none;\n"
"    margin: 0px;\n"
"    padding: 5px;\n"
"}"));

        verticalLayout_right_eye->addWidget(label_right_eye_status);


        horizontalLayout_display_status->addWidget(frame_right_eye);


        verticalLayout_emotion_page->addLayout(horizontalLayout_display_status);

        gridLayout_hi3861_config = new QGridLayout();
        gridLayout_hi3861_config->setSpacing(10);
        gridLayout_hi3861_config->setObjectName(QString::fromUtf8("gridLayout_hi3861_config"));
        label_left_eye_ip1 = new QLabel(page_emotion);
        label_left_eye_ip1->setObjectName(QString::fromUtf8("label_left_eye_ip1"));
        label_left_eye_ip1->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    font-size: 18px;\n"
"    font-weight: bold;\n"
"    color: #2c3e50;\n"
"}"));

        gridLayout_hi3861_config->addWidget(label_left_eye_ip1, 0, 0, 1, 1);

        horizontalLayout_left_eye_ip = new QHBoxLayout();
        horizontalLayout_left_eye_ip->setSpacing(5);
        horizontalLayout_left_eye_ip->setObjectName(QString::fromUtf8("horizontalLayout_left_eye_ip"));
        lineEdit_left_eye_ip = new QLineEdit(page_emotion);
        lineEdit_left_eye_ip->setObjectName(QString::fromUtf8("lineEdit_left_eye_ip"));
        lineEdit_left_eye_ip->setStyleSheet(QString::fromUtf8("QLineEdit {\n"
"    border: 2px solid #bdc3c7;\n"
"    border-radius: 8px;\n"
"    padding: 8px 12px;\n"
"    background-color: white;\n"
"    font-size: 18px;\n"
"    color: #2c3e50;\n"
"    min-height: 40px;\n"
"}\n"
"QLineEdit:focus {\n"
"    border-color: #3498db;\n"
"}"));

        horizontalLayout_left_eye_ip->addWidget(lineEdit_left_eye_ip);

        pushButton_keyboard_left = new QPushButton(page_emotion);
        pushButton_keyboard_left->setObjectName(QString::fromUtf8("pushButton_keyboard_left"));
        pushButton_keyboard_left->setMinimumSize(QSize(50, 40));
        pushButton_keyboard_left->setMaximumSize(QSize(60, 50));
        pushButton_keyboard_left->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    background-color: #95a5a6;\n"
"    border: none;\n"
"    border-radius: 8px;\n"
"    color: white;\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"    padding: 5px;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #7f8c8d;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #5d6d7e;\n"
"}"));

        horizontalLayout_left_eye_ip->addWidget(pushButton_keyboard_left);


        gridLayout_hi3861_config->addLayout(horizontalLayout_left_eye_ip, 0, 1, 1, 1);

        pushButton_connect_left_eye = new QPushButton(page_emotion);
        pushButton_connect_left_eye->setObjectName(QString::fromUtf8("pushButton_connect_left_eye"));
        pushButton_connect_left_eye->setMinimumSize(QSize(150, 60));
        pushButton_connect_left_eye->setMaximumSize(QSize(200, 80));
        pushButton_connect_left_eye->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    background-color: #3498db;\n"
"    border: none;\n"
"    border-radius: 12px;\n"
"    color: white;\n"
"    font-size: 20px;\n"
"    font-weight: bold;\n"
"    padding: 12px 20px;\n"
"    min-height: 60px;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #2980b9;\n"
"    transform: scale(1.05);\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #21618c;\n"
"    transform: scale(0.95);\n"
"}"));

        gridLayout_hi3861_config->addWidget(pushButton_connect_left_eye, 0, 2, 1, 1);

        label_right_eye_ip1 = new QLabel(page_emotion);
        label_right_eye_ip1->setObjectName(QString::fromUtf8("label_right_eye_ip1"));
        label_right_eye_ip1->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    font-size: 18px;\n"
"    font-weight: bold;\n"
"    color: #2c3e50;\n"
"}"));

        gridLayout_hi3861_config->addWidget(label_right_eye_ip1, 1, 0, 1, 1);

        horizontalLayout_right_eye_ip = new QHBoxLayout();
        horizontalLayout_right_eye_ip->setSpacing(5);
        horizontalLayout_right_eye_ip->setObjectName(QString::fromUtf8("horizontalLayout_right_eye_ip"));
        lineEdit_right_eye_ip = new QLineEdit(page_emotion);
        lineEdit_right_eye_ip->setObjectName(QString::fromUtf8("lineEdit_right_eye_ip"));
        lineEdit_right_eye_ip->setStyleSheet(QString::fromUtf8("QLineEdit {\n"
"    border: 2px solid #bdc3c7;\n"
"    border-radius: 8px;\n"
"    padding: 8px 12px;\n"
"    background-color: white;\n"
"    font-size: 18px;\n"
"    color: #2c3e50;\n"
"    min-height: 40px;\n"
"}\n"
"QLineEdit:focus {\n"
"    border-color: #3498db;\n"
"}"));

        horizontalLayout_right_eye_ip->addWidget(lineEdit_right_eye_ip);

        pushButton_keyboard_right = new QPushButton(page_emotion);
        pushButton_keyboard_right->setObjectName(QString::fromUtf8("pushButton_keyboard_right"));
        pushButton_keyboard_right->setMinimumSize(QSize(50, 40));
        pushButton_keyboard_right->setMaximumSize(QSize(60, 50));
        pushButton_keyboard_right->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    background-color: #95a5a6;\n"
"    border: none;\n"
"    border-radius: 8px;\n"
"    color: white;\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"    padding: 5px;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #7f8c8d;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #5d6d7e;\n"
"}"));

        horizontalLayout_right_eye_ip->addWidget(pushButton_keyboard_right);


        gridLayout_hi3861_config->addLayout(horizontalLayout_right_eye_ip, 1, 1, 1, 1);

        pushButton_connect_right_eye = new QPushButton(page_emotion);
        pushButton_connect_right_eye->setObjectName(QString::fromUtf8("pushButton_connect_right_eye"));
        pushButton_connect_right_eye->setMinimumSize(QSize(150, 60));
        pushButton_connect_right_eye->setMaximumSize(QSize(200, 80));
        pushButton_connect_right_eye->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    background-color: #e74c3c;\n"
"    border: none;\n"
"    border-radius: 12px;\n"
"    color: white;\n"
"    font-size: 20px;\n"
"    font-weight: bold;\n"
"    padding: 12px 20px;\n"
"    min-height: 60px;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #c0392b;\n"
"    transform: scale(1.05);\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #a93226;\n"
"    transform: scale(0.95);\n"
"}"));

        gridLayout_hi3861_config->addWidget(pushButton_connect_right_eye, 1, 2, 1, 1);


        verticalLayout_emotion_page->addLayout(gridLayout_hi3861_config);

        horizontalLayout_hi3861_status = new QHBoxLayout();
        horizontalLayout_hi3861_status->setObjectName(QString::fromUtf8("horizontalLayout_hi3861_status"));
        label_left_eye_status1 = new QLabel(page_emotion);
        label_left_eye_status1->setObjectName(QString::fromUtf8("label_left_eye_status1"));
        label_left_eye_status1->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #7f8c8d;\n"
"    font-size: 16px;\n"
"    padding: 5px;\n"
"}"));

        horizontalLayout_hi3861_status->addWidget(label_left_eye_status1);

        label_right_eye_status1 = new QLabel(page_emotion);
        label_right_eye_status1->setObjectName(QString::fromUtf8("label_right_eye_status1"));
        label_right_eye_status1->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #7f8c8d;\n"
"    font-size: 16px;\n"
"    padding: 5px;\n"
"}"));

        horizontalLayout_hi3861_status->addWidget(label_right_eye_status1);


        verticalLayout_emotion_page->addLayout(horizontalLayout_hi3861_status);

        verticalSpacer_emotion = new QSpacerItem(20, 20, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_emotion_page->addItem(verticalSpacer_emotion);

        stackedWidget_content->addWidget(page_emotion);
        page_sensor = new QWidget();
        page_sensor->setObjectName(QString::fromUtf8("page_sensor"));
        verticalLayout_sensor_page = new QVBoxLayout(page_sensor);
        verticalLayout_sensor_page->setSpacing(2);
        verticalLayout_sensor_page->setObjectName(QString::fromUtf8("verticalLayout_sensor_page"));
        verticalLayout_sensor_page->setContentsMargins(5, 3, 5, 3);
        label_sensor_title = new QLabel(page_sensor);
        label_sensor_title->setObjectName(QString::fromUtf8("label_sensor_title"));
        label_sensor_title->setMinimumSize(QSize(0, 0));
        label_sensor_title->setMaximumSize(QSize(16777215, 16777215));
        label_sensor_title->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    font-size: 42px;\n"
"    font-weight: bold;\n"
"    color: #2c3e50;\n"
"    margin-bottom: 20px;\n"
"    padding: 15px;\n"
"    background-color: #f0f0f0;\n"
"    border-radius: 15px;\n"
"}"));

        verticalLayout_sensor_page->addWidget(label_sensor_title);

        horizontalLayout_city_select = new QHBoxLayout();
        horizontalLayout_city_select->setSpacing(15);
        horizontalLayout_city_select->setObjectName(QString::fromUtf8("horizontalLayout_city_select"));
        horizontalLayout_city_select->setContentsMargins(0, 0, 0, 0);
        label_city = new QLabel(page_sensor);
        label_city->setObjectName(QString::fromUtf8("label_city"));
        label_city->setMinimumSize(QSize(120, 0));

        horizontalLayout_city_select->addWidget(label_city);

        comboBox_city = new QComboBox(page_sensor);
        comboBox_city->setObjectName(QString::fromUtf8("comboBox_city"));
        comboBox_city->setMaxVisibleItems(5);
        comboBox_city->setMinimumSize(QSize(0, 80));
        comboBox_city->setMaximumSize(QSize(16777215, 80));
        comboBox_city->setStyleSheet(QString::fromUtf8("QComboBox {\n"
"    border: 3px solid #bdc3c7;\n"
"    border-radius: 10px;\n"
"    padding: 12px 20px;\n"
"    background-color: white;\n"
"    font-size: 36px;\n"
"    color: #000000;\n"
"}\n"
"QComboBox:hover { border-color: #3498db; }\n"
"QComboBox::drop-down { border: none; }\n"
"QComboBox::down-arrow { image: none; }\n"
"QComboBox QAbstractItemView {\n"
"    font-size: 36px;\n"
"    color: #000000;\n"
"    background-color: white;\n"
"}"));

        horizontalLayout_city_select->addWidget(comboBox_city);


        verticalLayout_sensor_page->addLayout(horizontalLayout_city_select);

        verticalLayout_city_slider = new QVBoxLayout();
        verticalLayout_city_slider->setSpacing(10);
        verticalLayout_city_slider->setObjectName(QString::fromUtf8("verticalLayout_city_slider"));
        verticalLayout_city_slider->setContentsMargins(0, 0, 0, 0);
        label_city_slider = new QLabel(page_sensor);
        label_city_slider->setObjectName(QString::fromUtf8("label_city_slider"));
        label_city_slider->setMinimumSize(QSize(0, 0));
        label_city_slider->setMaximumSize(QSize(16777215, 16777215));
        label_city_slider->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #7f8c8d;\n"
"    font-size: 32px;\n"
"}"));

        verticalLayout_city_slider->addWidget(label_city_slider);

        horizontalSlider_city = new QSlider(page_sensor);
        horizontalSlider_city->setObjectName(QString::fromUtf8("horizontalSlider_city"));
        horizontalSlider_city->setMinimum(0);
        horizontalSlider_city->setMaximum(14);
        horizontalSlider_city->setValue(12);
        horizontalSlider_city->setOrientation(Qt::Horizontal);
        horizontalSlider_city->setMinimumSize(QSize(0, 60));
        horizontalSlider_city->setMaximumSize(QSize(16777215, 60));
        horizontalSlider_city->setStyleSheet(QString::fromUtf8("QSlider::groove:horizontal {\n"
"    border: 1px solid #bdc3c7;\n"
"    height: 30px;\n"
"    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, \n"
"        stop:0 #3498db, stop:0.2 #2ecc71, stop:0.4 #f39c12, \n"
"        stop:0.6 #e74c3c, stop:0.8 #9b59b6, stop:1 #34495e);\n"
"    margin: 2px 0;\n"
"    border-radius: 15px;\n"
"}\n"
"QSlider::handle:horizontal {\n"
"    background: white;\n"
"    border: 4px solid #2980b9;\n"
"    width: 50px;\n"
"    height: 50px;\n"
"    margin: -15px 0;\n"
"    border-radius: 25px;\n"
"}"));

        verticalLayout_city_slider->addWidget(horizontalSlider_city);

        label_city_display = new QLabel(page_sensor);
        label_city_display->setObjectName(QString::fromUtf8("label_city_display"));
        label_city_display->setAlignment(Qt::AlignCenter);
        label_city_display->setMinimumSize(QSize(0, 0));
        label_city_display->setMaximumSize(QSize(16777215, 16777215));
        label_city_display->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #2c3e50;\n"
"    font-size: 36px;\n"
"    font-weight: bold;\n"
"    background-color: #ecf0f1;\n"
"    border: 1px solid #bdc3c7;\n"
"    border-radius: 10px;\n"
"    padding: 15px;\n"
"}"));

        verticalLayout_city_slider->addWidget(label_city_display);


        verticalLayout_sensor_page->addLayout(verticalLayout_city_slider);

        gridLayout_sensor_data = new QGridLayout();
        gridLayout_sensor_data->setSpacing(15);
        gridLayout_sensor_data->setObjectName(QString::fromUtf8("gridLayout_sensor_data"));
        gridLayout_sensor_data->setContentsMargins(0, 0, 0, 0);
        label_weather_title = new QLabel(page_sensor);
        label_weather_title->setObjectName(QString::fromUtf8("label_weather_title"));

        gridLayout_sensor_data->addWidget(label_weather_title, 0, 0, 1, 1);

        label_weather = new QLabel(page_sensor);
        label_weather->setObjectName(QString::fromUtf8("label_weather"));
        label_weather->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #f39c12;\n"
"    font-weight: bold;\n"
"}"));

        gridLayout_sensor_data->addWidget(label_weather, 0, 1, 1, 1);

        label_temp_title = new QLabel(page_sensor);
        label_temp_title->setObjectName(QString::fromUtf8("label_temp_title"));

        gridLayout_sensor_data->addWidget(label_temp_title, 1, 0, 1, 1);

        label_temperature = new QLabel(page_sensor);
        label_temperature->setObjectName(QString::fromUtf8("label_temperature"));
        label_temperature->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #e74c3c;\n"
"    font-weight: bold;\n"
"}"));

        gridLayout_sensor_data->addWidget(label_temperature, 1, 1, 1, 1);

        label_humidity_title = new QLabel(page_sensor);
        label_humidity_title->setObjectName(QString::fromUtf8("label_humidity_title"));

        gridLayout_sensor_data->addWidget(label_humidity_title, 2, 0, 1, 1);

        label_humidity = new QLabel(page_sensor);
        label_humidity->setObjectName(QString::fromUtf8("label_humidity"));
        label_humidity->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #3498db;\n"
"    font-weight: bold;\n"
"}"));

        gridLayout_sensor_data->addWidget(label_humidity, 2, 1, 1, 1);

        label_wind_title = new QLabel(page_sensor);
        label_wind_title->setObjectName(QString::fromUtf8("label_wind_title"));

        gridLayout_sensor_data->addWidget(label_wind_title, 3, 0, 1, 1);

        label_wind = new QLabel(page_sensor);
        label_wind->setObjectName(QString::fromUtf8("label_wind"));
        label_wind->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #27ae60;\n"
"    font-weight: bold;\n"
"}"));

        gridLayout_sensor_data->addWidget(label_wind, 3, 1, 1, 1);


        verticalLayout_sensor_page->addLayout(gridLayout_sensor_data);

        horizontalLayout_sensor_buttons = new QHBoxLayout();
        horizontalLayout_sensor_buttons->setSpacing(20);
        horizontalLayout_sensor_buttons->setObjectName(QString::fromUtf8("horizontalLayout_sensor_buttons"));
        horizontalLayout_sensor_buttons->setContentsMargins(0, 0, 0, 0);
        pushButton_refresh_weather = new QPushButton(page_sensor);
        pushButton_refresh_weather->setObjectName(QString::fromUtf8("pushButton_refresh_weather"));
        pushButton_refresh_weather->setMinimumSize(QSize(0, 100));
        pushButton_refresh_weather->setMaximumSize(QSize(16777215, 120));
        pushButton_refresh_weather->setStyleSheet(QString::fromUtf8("/* Inherits from global QPushButton style */"));

        horizontalLayout_sensor_buttons->addWidget(pushButton_refresh_weather);


        verticalLayout_sensor_page->addLayout(horizontalLayout_sensor_buttons);

        hardwareTestGroupBox = new QGroupBox(page_sensor);
        hardwareTestGroupBox->setObjectName(QString::fromUtf8("hardwareTestGroupBox"));
        horizontalLayout_hardware_test = new QHBoxLayout(hardwareTestGroupBox);
        horizontalLayout_hardware_test->setObjectName(QString::fromUtf8("horizontalLayout_hardware_test"));
        testSpeakerButton = new QPushButton(hardwareTestGroupBox);
        testSpeakerButton->setObjectName(QString::fromUtf8("testSpeakerButton"));

        horizontalLayout_hardware_test->addWidget(testSpeakerButton);

        testMicrophoneButton = new QPushButton(hardwareTestGroupBox);
        testMicrophoneButton->setObjectName(QString::fromUtf8("testMicrophoneButton"));

        horizontalLayout_hardware_test->addWidget(testMicrophoneButton);

        testCameraButton = new QPushButton(hardwareTestGroupBox);
        testCameraButton->setObjectName(QString::fromUtf8("testCameraButton"));

        horizontalLayout_hardware_test->addWidget(testCameraButton);


        verticalLayout_sensor_page->addWidget(hardwareTestGroupBox);

        verticalSpacer_sensor = new QSpacerItem(20, 5, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_sensor_page->addItem(verticalSpacer_sensor);

        stackedWidget_content->addWidget(page_sensor);
        page_camera = new QWidget();
        page_camera->setObjectName(QString::fromUtf8("page_camera"));
        verticalLayout_camera_page = new QVBoxLayout(page_camera);
        verticalLayout_camera_page->setSpacing(15);
        verticalLayout_camera_page->setObjectName(QString::fromUtf8("verticalLayout_camera_page"));
        verticalLayout_camera_page->setContentsMargins(15, 15, 15, 15);
        label_camera_title = new QLabel(page_camera);
        label_camera_title->setObjectName(QString::fromUtf8("label_camera_title"));
        label_camera_title->setMinimumSize(QSize(0, 0));
        label_camera_title->setMaximumSize(QSize(16777215, 16777215));
        label_camera_title->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    font-size: 42px;\n"
"    font-weight: bold;\n"
"    color: #2c3e50;\n"
"    margin-bottom: 20px;\n"
"    padding: 15px;\n"
"    background-color: #f0f0f0;\n"
"    border-radius: 15px;\n"
"}"));

        verticalLayout_camera_page->addWidget(label_camera_title);

        label_camera_display = new QLabel(page_camera);
        label_camera_display->setObjectName(QString::fromUtf8("label_camera_display"));
        label_camera_display->setAlignment(Qt::AlignCenter);
        label_camera_display->setMinimumSize(QSize(960, 540));
        label_camera_display->setMaximumSize(QSize(16777215, 16777215));
        label_camera_display->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    background-color: #1e1e1e;\n"
"    border: 4px solid #3498db;\n"
"    border-radius: 15px;\n"
"    color: #ecf0f1;\n"
"    font-size: 38px;\n"
"    font-weight: bold;\n"
"}"));
        label_camera_display->setScaledContents(true);

        verticalLayout_camera_page->addWidget(label_camera_display);

        horizontalLayout_camera_info = new QHBoxLayout();
        horizontalLayout_camera_info->setSpacing(20);
        horizontalLayout_camera_info->setObjectName(QString::fromUtf8("horizontalLayout_camera_info"));
        label_camera_status = new QLabel(page_camera);
        label_camera_status->setObjectName(QString::fromUtf8("label_camera_status"));
        label_camera_status->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #e74c3c;\n"
"    font-size: 36px;\n"
"    font-weight: bold;\n"
"}"));

        horizontalLayout_camera_info->addWidget(label_camera_status);

        label_camera_resolution = new QLabel(page_camera);
        label_camera_resolution->setObjectName(QString::fromUtf8("label_camera_resolution"));
        label_camera_resolution->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #7f8c8d;\n"
"    font-size: 36px;\n"
"}"));

        horizontalLayout_camera_info->addWidget(label_camera_resolution);

        label_camera_fps = new QLabel(page_camera);
        label_camera_fps->setObjectName(QString::fromUtf8("label_camera_fps"));
        label_camera_fps->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #7f8c8d;\n"
"    font-size: 36px;\n"
"}"));

        horizontalLayout_camera_info->addWidget(label_camera_fps);


        verticalLayout_camera_page->addLayout(horizontalLayout_camera_info);

        horizontalLayout_camera_buttons = new QHBoxLayout();
        horizontalLayout_camera_buttons->setSpacing(15);
        horizontalLayout_camera_buttons->setObjectName(QString::fromUtf8("horizontalLayout_camera_buttons"));
        pushButton_camera_start = new QPushButton(page_camera);
        pushButton_camera_start->setObjectName(QString::fromUtf8("pushButton_camera_start"));
        pushButton_camera_start->setMinimumSize(QSize(0, 120));
        pushButton_camera_start->setMaximumSize(QSize(16777215, 120));
        pushButton_camera_start->setStyleSheet(QString::fromUtf8("QPushButton { background-color: #27ae60; }\n"
"QPushButton:hover { background-color: #229954; }\n"
"QPushButton:pressed { background-color: #1e8449; }"));

        horizontalLayout_camera_buttons->addWidget(pushButton_camera_start);

        pushButton_camera_stop = new QPushButton(page_camera);
        pushButton_camera_stop->setObjectName(QString::fromUtf8("pushButton_camera_stop"));
        pushButton_camera_stop->setEnabled(false);
        pushButton_camera_stop->setMinimumSize(QSize(0, 120));
        pushButton_camera_stop->setMaximumSize(QSize(16777215, 120));
        pushButton_camera_stop->setStyleSheet(QString::fromUtf8("QPushButton { background-color: #e74c3c; }\n"
"QPushButton:hover { background-color: #c0392b; }\n"
"QPushButton:pressed { background-color: #a93226; }"));

        horizontalLayout_camera_buttons->addWidget(pushButton_camera_stop);

        pushButton_camera_snapshot = new QPushButton(page_camera);
        pushButton_camera_snapshot->setObjectName(QString::fromUtf8("pushButton_camera_snapshot"));
        pushButton_camera_snapshot->setEnabled(false);
        pushButton_camera_snapshot->setMinimumSize(QSize(0, 120));
        pushButton_camera_snapshot->setMaximumSize(QSize(16777215, 120));
        pushButton_camera_snapshot->setStyleSheet(QString::fromUtf8("QPushButton { background-color: #f39c12; }\n"
"QPushButton:hover { background-color: #e67e22; }\n"
"QPushButton:pressed { background-color: #d68910; }"));

        horizontalLayout_camera_buttons->addWidget(pushButton_camera_snapshot);


        verticalLayout_camera_page->addLayout(horizontalLayout_camera_buttons);

        verticalSpacer_camera = new QSpacerItem(20, 5, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_camera_page->addItem(verticalSpacer_camera);

        stackedWidget_content->addWidget(page_camera);
        page_system = new QWidget();
        page_system->setObjectName(QString::fromUtf8("page_system"));
        verticalLayout_system_page = new QVBoxLayout(page_system);
        verticalLayout_system_page->setSpacing(3);
        verticalLayout_system_page->setObjectName(QString::fromUtf8("verticalLayout_system_page"));
        verticalLayout_system_page->setContentsMargins(5, 5, 5, 5);
        label_system_title = new QLabel(page_system);
        label_system_title->setObjectName(QString::fromUtf8("label_system_title"));
        label_system_title->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    font-size: 42px;\n"
"    font-weight: bold;\n"
"    color: #2c3e50;\n"
"    margin-bottom: 20px;\n"
"    padding: 15px;\n"
"    background-color: #f0f0f0;\n"
"    border-radius: 15px;\n"
"}"));

        verticalLayout_system_page->addWidget(label_system_title);

        label_system_status = new QLabel(page_system);
        label_system_status->setObjectName(QString::fromUtf8("label_system_status"));
        label_system_status->setStyleSheet(QString::fromUtf8("QLabel {\n"
"    color: #27ae60;\n"
"    font-weight: bold;\n"
"    font-size: 36px;\n"
"}"));

        verticalLayout_system_page->addWidget(label_system_status);

        horizontalLayout_debug = new QHBoxLayout();
        horizontalLayout_debug->setSpacing(15);
        horizontalLayout_debug->setObjectName(QString::fromUtf8("horizontalLayout_debug"));
        label_debug_mode = new QLabel(page_system);
        label_debug_mode->setObjectName(QString::fromUtf8("label_debug_mode"));

        horizontalLayout_debug->addWidget(label_debug_mode);

        comboBox_debug_level = new QComboBox(page_system);
        comboBox_debug_level->addItem(QString());
        comboBox_debug_level->addItem(QString());
        comboBox_debug_level->addItem(QString());
        comboBox_debug_level->addItem(QString());
        comboBox_debug_level->setObjectName(QString::fromUtf8("comboBox_debug_level"));
        comboBox_debug_level->setStyleSheet(QString::fromUtf8("QComboBox {\n"
"    border: 3px solid #bdc3c7;\n"
"    border-radius: 10px;\n"
"    padding: 12px 20px;\n"
"    background-color: white;\n"
"    font-size: 36px;\n"
"    color: #2c3e50;\n"
"    min-height: 80px;\n"
"}\n"
"QComboBox:hover { border-color: #3498db; }\n"
"QComboBox::drop-down { border: none; }\n"
"QComboBox::down-arrow { image: none; }\n"
"QComboBox QAbstractItemView {\n"
"    font-size: 36px;\n"
"}"));

        horizontalLayout_debug->addWidget(comboBox_debug_level);


        verticalLayout_system_page->addLayout(horizontalLayout_debug);

        textEdit_logs = new QTextEdit(page_system);
        textEdit_logs->setObjectName(QString::fromUtf8("textEdit_logs"));
        textEdit_logs->setReadOnly(true);
        textEdit_logs->setMinimumSize(QSize(0, 400));
        textEdit_logs->setMaximumSize(QSize(16777215, 600));

        verticalLayout_system_page->addWidget(textEdit_logs);

        horizontalLayout_system_buttons = new QHBoxLayout();
        horizontalLayout_system_buttons->setSpacing(15);
        horizontalLayout_system_buttons->setObjectName(QString::fromUtf8("horizontalLayout_system_buttons"));
        pushButton_reconnect = new QPushButton(page_system);
        pushButton_reconnect->setObjectName(QString::fromUtf8("pushButton_reconnect"));

        horizontalLayout_system_buttons->addWidget(pushButton_reconnect);

        pushButton_settings = new QPushButton(page_system);
        pushButton_settings->setObjectName(QString::fromUtf8("pushButton_settings"));

        horizontalLayout_system_buttons->addWidget(pushButton_settings);


        verticalLayout_system_page->addLayout(horizontalLayout_system_buttons);

        verticalSpacer_system = new QSpacerItem(20, 10, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_system_page->addItem(verticalSpacer_system);

        stackedWidget_content->addWidget(page_system);

        verticalLayout_main->addWidget(stackedWidget_content);

        navWidget = new QWidget(centralwidget);
        navWidget->setObjectName(QString::fromUtf8("navWidget"));
        navWidget->setMinimumSize(QSize(0, 150));
        navWidget->setMaximumSize(QSize(16777215, 150));
        horizontalLayout_nav = new QHBoxLayout(navWidget);
        horizontalLayout_nav->setSpacing(0);
        horizontalLayout_nav->setObjectName(QString::fromUtf8("horizontalLayout_nav"));
        horizontalLayout_nav->setContentsMargins(0, 0, 0, 0);
        pushButton_nav_speech = new QPushButton(navWidget);
        pushButton_nav_speech->setObjectName(QString::fromUtf8("pushButton_nav_speech"));
        pushButton_nav_speech->setCheckable(true);
        pushButton_nav_speech->setChecked(true);
        pushButton_nav_speech->setStyleSheet(QString::fromUtf8("/* Style is inherited from #navWidget QPushButton */"));

        horizontalLayout_nav->addWidget(pushButton_nav_speech);

        pushButton_nav_emotion = new QPushButton(navWidget);
        pushButton_nav_emotion->setObjectName(QString::fromUtf8("pushButton_nav_emotion"));
        pushButton_nav_emotion->setCheckable(true);
        pushButton_nav_emotion->setStyleSheet(QString::fromUtf8("/* Style is inherited from #navWidget QPushButton */"));

        horizontalLayout_nav->addWidget(pushButton_nav_emotion);

        pushButton_nav_sensor = new QPushButton(navWidget);
        pushButton_nav_sensor->setObjectName(QString::fromUtf8("pushButton_nav_sensor"));
        pushButton_nav_sensor->setCheckable(true);
        pushButton_nav_sensor->setStyleSheet(QString::fromUtf8("/* Style is inherited from #navWidget QPushButton */"));

        horizontalLayout_nav->addWidget(pushButton_nav_sensor);

        pushButton_nav_camera = new QPushButton(navWidget);
        pushButton_nav_camera->setObjectName(QString::fromUtf8("pushButton_nav_camera"));
        pushButton_nav_camera->setCheckable(true);
        pushButton_nav_camera->setStyleSheet(QString::fromUtf8("/* Style is inherited from #navWidget QPushButton */"));

        horizontalLayout_nav->addWidget(pushButton_nav_camera);

        pushButton_nav_system = new QPushButton(navWidget);
        pushButton_nav_system->setObjectName(QString::fromUtf8("pushButton_nav_system"));
        pushButton_nav_system->setCheckable(true);
        pushButton_nav_system->setStyleSheet(QString::fromUtf8("/* Style is inherited from #navWidget QPushButton */"));

        horizontalLayout_nav->addWidget(pushButton_nav_system);


        verticalLayout_main->addWidget(navWidget);

        MainWindow->setCentralWidget(centralwidget);

        retranslateUi(MainWindow);

        stackedWidget_content->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "RK3588\346\231\272\350\203\275\344\272\244\344\272\222\347\263\273\347\273\237", nullptr));
        label_header_title->setText(QCoreApplication::translate("MainWindow", "\360\237\247\240 RK3588 \346\231\272\350\203\275\344\272\244\344\272\222\347\263\273\347\273\237", nullptr));
        groupBox_music_player->setTitle(QCoreApplication::translate("MainWindow", "\360\237\216\265 \351\237\263\344\271\220\346\222\255\346\224\276\345\231\250", nullptr));
        label_current_song->setText(QCoreApplication::translate("MainWindow", "\360\237\216\265 \345\275\223\345\211\215\346\222\255\346\224\276: \346\232\202\346\227\240", nullptr));
        pushButton_pause_music->setText(QCoreApplication::translate("MainWindow", "\342\217\270\357\270\217 \346\232\202\345\201\234", nullptr));
        pushButton_stop_music->setText(QCoreApplication::translate("MainWindow", "\342\217\271\357\270\217 \345\201\234\346\255\242", nullptr));
        label_conversation_status->setText(QCoreApplication::translate("MainWindow", "\347\212\266\346\200\201: \347\251\272\351\227\262", nullptr));
        pushButton_start_conversation->setText(QCoreApplication::translate("MainWindow", "\360\237\216\244 \345\274\200\345\247\213\350\257\264\350\257\235", nullptr));
        pushButton_stop_reply->setText(QCoreApplication::translate("MainWindow", "\342\217\271\357\270\217 \345\201\234\346\255\242\345\233\236\345\244\215", nullptr));
        label_emotion_title->setText(QCoreApplication::translate("MainWindow", "\360\237\230\212 \350\241\250\346\203\205\346\216\247\345\210\266", nullptr));
        pushButton_happy->setText(QCoreApplication::translate("MainWindow", "\360\237\230\212 \345\274\200\345\277\203", nullptr));
        pushButton_sad->setText(QCoreApplication::translate("MainWindow", "\360\237\230\242 \344\274\244\345\277\203", nullptr));
        pushButton_angry->setText(QCoreApplication::translate("MainWindow", "\360\237\230\240 \346\204\244\346\200\222", nullptr));
        pushButton_surprised->setText(QCoreApplication::translate("MainWindow", "\360\237\230\262 \346\203\212\350\256\266", nullptr));
        pushButton_fear->setText(QCoreApplication::translate("MainWindow", "\360\237\230\260 \346\201\220\346\203\247", nullptr));
        pushButton_neutral->setText(QCoreApplication::translate("MainWindow", "\360\237\230\220 \344\270\255\346\200\247", nullptr));
        label_current_emotion->setText(QCoreApplication::translate("MainWindow", "\345\275\223\345\211\215\350\241\250\346\203\205: \344\270\255\346\200\247", nullptr));
        label_device_status->setText(QCoreApplication::translate("MainWindow", "\360\237\224\215 \346\243\200\346\265\213\350\256\276\345\244\207\350\277\236\346\216\245\347\212\266\346\200\201...", nullptr));
        label_left_eye_title->setText(QCoreApplication::translate("MainWindow", "\360\237\221\201\357\270\217 \345\267\246\347\234\274\346\230\276\347\244\272\345\261\217", nullptr));
        label_left_eye_ip->setText(QCoreApplication::translate("MainWindow", "**************", nullptr));
        label_left_eye_status->setText(QCoreApplication::translate("MainWindow", "\342\235\214 \346\234\252\350\277\236\346\216\245", nullptr));
        label_right_eye_title->setText(QCoreApplication::translate("MainWindow", "\360\237\221\201\357\270\217 \345\217\263\347\234\274\346\230\276\347\244\272\345\261\217", nullptr));
        label_right_eye_ip->setText(QCoreApplication::translate("MainWindow", "**************", nullptr));
        label_right_eye_status->setText(QCoreApplication::translate("MainWindow", "\342\235\214 \346\234\252\350\277\236\346\216\245", nullptr));
        label_left_eye_ip1->setText(QCoreApplication::translate("MainWindow", "\345\267\246\347\234\274IP:", nullptr));
        lineEdit_left_eye_ip->setText(QCoreApplication::translate("MainWindow", "**************", nullptr));
        pushButton_keyboard_left->setText(QCoreApplication::translate("MainWindow", "\342\214\250\357\270\217", nullptr));
        pushButton_connect_left_eye->setText(QCoreApplication::translate("MainWindow", "\360\237\224\214 \350\277\236\346\216\245\345\267\246\347\234\274", nullptr));
        label_right_eye_ip1->setText(QCoreApplication::translate("MainWindow", "\345\217\263\347\234\274IP:", nullptr));
        lineEdit_right_eye_ip->setText(QCoreApplication::translate("MainWindow", "**************", nullptr));
        pushButton_keyboard_right->setText(QCoreApplication::translate("MainWindow", "\342\214\250\357\270\217", nullptr));
        pushButton_connect_right_eye->setText(QCoreApplication::translate("MainWindow", "\360\237\224\214 \350\277\236\346\216\245\345\217\263\347\234\274", nullptr));
        label_left_eye_status1->setText(QCoreApplication::translate("MainWindow", "\345\267\246\347\234\274: \346\234\252\350\277\236\346\216\245", nullptr));
        label_right_eye_status1->setText(QCoreApplication::translate("MainWindow", "\345\217\263\347\234\274: \346\234\252\350\277\236\346\216\245", nullptr));
        label_sensor_title->setText(QCoreApplication::translate("MainWindow", "\360\237\214\244\357\270\217 \345\244\251\346\260\224\344\277\241\346\201\257", nullptr));
        label_city->setText(QCoreApplication::translate("MainWindow", "\345\237\216\345\270\202:", nullptr));
        label_city_slider->setText(QCoreApplication::translate("MainWindow", "\346\213\226\346\213\275\351\200\211\346\213\251\345\237\216\345\270\202:", nullptr));
        label_city_display->setText(QCoreApplication::translate("MainWindow", "\345\275\223\345\211\215: \351\235\222\345\262\233", nullptr));
        label_weather_title->setText(QCoreApplication::translate("MainWindow", "\345\244\251\346\260\224:", nullptr));
        label_weather->setText(QCoreApplication::translate("MainWindow", "\346\231\264", nullptr));
        label_temp_title->setText(QCoreApplication::translate("MainWindow", "\346\270\251\345\272\246:", nullptr));
        label_temperature->setText(QCoreApplication::translate("MainWindow", "22\302\260C", nullptr));
        label_humidity_title->setText(QCoreApplication::translate("MainWindow", "\346\271\277\345\272\246:", nullptr));
        label_humidity->setText(QCoreApplication::translate("MainWindow", "45%", nullptr));
        label_wind_title->setText(QCoreApplication::translate("MainWindow", "\351\243\216\345\212\233:", nullptr));
        label_wind->setText(QCoreApplication::translate("MainWindow", "3\347\272\247 \344\270\234\345\215\227\351\243\216", nullptr));
        pushButton_refresh_weather->setText(QCoreApplication::translate("MainWindow", "\345\210\267\346\226\260\345\244\251\346\260\224", nullptr));
        hardwareTestGroupBox->setTitle(QCoreApplication::translate("MainWindow", "\347\241\254\344\273\266\346\265\213\350\257\225", nullptr));
        testSpeakerButton->setText(QCoreApplication::translate("MainWindow", "\346\265\213\350\257\225\345\226\207\345\217\255", nullptr));
        testMicrophoneButton->setText(QCoreApplication::translate("MainWindow", "\346\265\213\350\257\225\351\272\246\345\205\213\351\243\216", nullptr));
        testCameraButton->setText(QCoreApplication::translate("MainWindow", "\346\265\213\350\257\225\346\221\204\345\203\217\345\244\264", nullptr));
        label_camera_title->setText(QCoreApplication::translate("MainWindow", "\360\237\223\267 \346\221\204\345\203\217\345\244\264\347\224\273\351\235\242", nullptr));
        label_camera_display->setText(QCoreApplication::translate("MainWindow", "\346\221\204\345\203\217\345\244\264\347\224\273\351\235\242\346\230\276\347\244\272\345\214\272\345\237\237", nullptr));
        label_camera_status->setText(QCoreApplication::translate("MainWindow", "\347\212\266\346\200\201: \347\246\273\347\272\277", nullptr));
        label_camera_resolution->setText(QCoreApplication::translate("MainWindow", "\345\210\206\350\276\250\347\216\207: 640x480", nullptr));
        label_camera_fps->setText(QCoreApplication::translate("MainWindow", "\345\270\247\347\216\207: 0 FPS", nullptr));
        pushButton_camera_start->setText(QCoreApplication::translate("MainWindow", "\345\220\257\345\212\250", nullptr));
        pushButton_camera_stop->setText(QCoreApplication::translate("MainWindow", "\345\201\234\346\255\242", nullptr));
        pushButton_camera_snapshot->setText(QCoreApplication::translate("MainWindow", "\346\213\215\347\205\247", nullptr));
        label_system_title->setText(QCoreApplication::translate("MainWindow", "\342\232\231\357\270\217 \347\263\273\347\273\237\344\277\241\346\201\257", nullptr));
        label_system_status->setText(QCoreApplication::translate("MainWindow", "\347\263\273\347\273\237\350\277\220\350\241\214\344\270\255 | \350\277\236\346\216\245: \345\220\246", nullptr));
        label_debug_mode->setText(QCoreApplication::translate("MainWindow", "\350\260\203\350\257\225:", nullptr));
        comboBox_debug_level->setItemText(0, QCoreApplication::translate("MainWindow", "\344\277\241\346\201\257", nullptr));
        comboBox_debug_level->setItemText(1, QCoreApplication::translate("MainWindow", "\350\255\246\345\221\212", nullptr));
        comboBox_debug_level->setItemText(2, QCoreApplication::translate("MainWindow", "\351\224\231\350\257\257", nullptr));
        comboBox_debug_level->setItemText(3, QCoreApplication::translate("MainWindow", "\350\260\203\350\257\225", nullptr));

        textEdit_logs->setHtml(QCoreApplication::translate("MainWindow", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"</style></head><body style=\" font-family:'Ubuntu'; font-size:8pt; font-weight:400; font-style:normal;\">\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" color:#27ae60;\">[2025-06-12 10:42:54] \347\263\273\347\273\237\345\220\257\345\212\250\345\256\214\346\210\220</span></p></body></html>", nullptr));
        pushButton_reconnect->setText(QCoreApplication::translate("MainWindow", "\351\207\215\350\277\236", nullptr));
        pushButton_settings->setText(QCoreApplication::translate("MainWindow", "\350\256\276\347\275\256", nullptr));
        pushButton_nav_speech->setText(QCoreApplication::translate("MainWindow", "\360\237\216\244\n"
"\350\257\255\351\237\263", nullptr));
        pushButton_nav_emotion->setText(QCoreApplication::translate("MainWindow", "\360\237\230\212\n"
"\350\241\250\346\203\205", nullptr));
        pushButton_nav_sensor->setText(QCoreApplication::translate("MainWindow", "\360\237\214\244\357\270\217\n"
"\345\244\251\346\260\224", nullptr));
        pushButton_nav_camera->setText(QCoreApplication::translate("MainWindow", "\360\237\223\267\n"
"\346\221\204\345\203\217\345\244\264", nullptr));
        pushButton_nav_system->setText(QCoreApplication::translate("MainWindow", "\342\232\231\357\270\217\n"
"\347\263\273\347\273\237", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
