#!/bin/bash

# Hi3861设备网络连接测试工具
# 用于诊断设备连接问题

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印带颜色的信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认配置
DEFAULT_IP="*************"
DEFAULT_PORT="8889"
TIMEOUT=3

# 显示使用说明
show_usage() {
    echo "Hi3861设备网络连接测试工具"
    echo ""
    echo "用法: $0 [IP地址] [端口]"
    echo ""
    echo "参数:"
    echo "  IP地址    Hi3861设备的IP地址 (默认: $DEFAULT_IP)"
    echo "  端口      Hi3861设备的TCP端口 (默认: $DEFAULT_PORT)"
    echo ""
    echo "示例:"
    echo "  $0                           # 使用默认IP和端口"
    echo "  $0 *************            # 指定IP，使用默认端口"
    echo "  $0 ************* 8889       # 指定IP和端口"
    echo ""
}

# 测试网络连通性
test_ping() {
    local ip=$1
    print_info "测试ping连通性: $ip"
    
    if ping -c 3 -W $TIMEOUT "$ip" > /dev/null 2>&1; then
        print_success "ping测试成功 - 设备网络可达"
        return 0
    else
        print_error "ping测试失败 - 设备网络不可达"
        print_warning "可能原因:"
        print_warning "1. 设备未连接到网络"
        print_warning "2. IP地址错误"
        print_warning "3. 网络路由问题"
        return 1
    fi
}

# 测试端口连通性
test_port() {
    local ip=$1
    local port=$2
    print_info "测试TCP端口连通性: $ip:$port"
    
    # 使用nc (netcat) 测试端口
    if command -v nc > /dev/null 2>&1; then
        if timeout $TIMEOUT nc -z "$ip" "$port" 2>/dev/null; then
            print_success "端口$port连接成功 - TCP服务可用"
            return 0
        else
            print_error "端口$port连接失败 - TCP服务不可用"
            return 1
        fi
    # 如果没有nc，使用telnet测试
    elif command -v telnet > /dev/null 2>&1; then
        if timeout $TIMEOUT bash -c "echo '' | telnet $ip $port" 2>/dev/null | grep -q "Connected"; then
            print_success "端口$port连接成功 - TCP服务可用"
            return 0
        else
            print_error "端口$port连接失败 - TCP服务不可用"
            return 1
        fi
    # 使用bash内建的TCP连接测试
    else
        if timeout $TIMEOUT bash -c "exec 3<>/dev/tcp/$ip/$port" 2>/dev/null; then
            exec 3<&-
            exec 3>&-
            print_success "端口$port连接成功 - TCP服务可用"
            return 0
        else
            print_error "端口$port连接失败 - TCP服务不可用"
            return 1
        fi
    fi
}

# 尝试连接并发送测试命令
test_communication() {
    local ip=$1
    local port=$2
    print_info "测试Hi3861通信协议: $ip:$port"
    
    # 创建临时脚本测试通信
    local test_script=$(mktemp)
    cat > "$test_script" << 'EOF'
#!/bin/bash
IP=$1
PORT=$2

exec 3<>/dev/tcp/$IP/$PORT 2>/dev/null
if [ $? -eq 0 ]; then
    echo "DEVICE_INFO" >&3
    read -t 2 response <&3
    exec 3<&-
    exec 3>&-
    echo "Response: $response"
    if [[ "$response" == *"DEVICE_INFO"* ]]; then
        exit 0
    else
        exit 1
    fi
else
    exit 2
fi
EOF

    chmod +x "$test_script"
    
    if timeout 5 "$test_script" "$ip" "$port" > /dev/null 2>&1; then
        print_success "Hi3861通信协议测试成功"
        rm -f "$test_script"
        return 0
    else
        print_warning "Hi3861通信协议测试失败或超时"
        print_warning "设备可能未实现标准Hi3861协议"
        rm -f "$test_script"
        return 1
    fi
}

# 显示网络状态
show_network_status() {
    print_info "当前网络状态:"
    
    # 显示本机IP地址
    print_info "本机IP地址:"
    ip route get ******* 2>/dev/null | awk '{print $7}' | head -1 | while read ip; do
        if [[ -n "$ip" ]]; then
            echo "  主要IP: $ip"
        fi
    done
    
    # 显示WiFi连接状态
    if command -v iwgetid > /dev/null 2>&1; then
        local wifi_ssid=$(iwgetid -r 2>/dev/null)
        if [[ -n "$wifi_ssid" ]]; then
            print_info "WiFi连接: $wifi_ssid"
        fi
    fi
    
    # 显示路由表
    print_info "网络路由:"
    ip route | grep default | head -3 | while read line; do
        echo "  $line"
    done
    
    echo ""
}

# 显示排查建议
show_troubleshooting() {
    print_warning "Hi3861设备连接问题排查建议:"
    echo ""
    echo "1. 📡 设备端检查:"
    echo "   - 确认Hi3861设备已上电启动"
    echo "   - 检查设备WiFi连接状态"
    echo "   - 确认TCP服务器程序正在运行"
    echo "   - 检查端口8889是否被监听"
    echo ""
    echo "2. 🖥️ PC端检查:"
    echo "   - 确认PC和设备在同一WiFi网络"
    echo "   - 检查防火墙设置"
    echo "   - 尝试不同的IP地址"
    echo ""
    echo "3. 🔧 手动测试命令:"
    echo "   ping $DEFAULT_IP"
    echo "   telnet $DEFAULT_IP $DEFAULT_PORT"
    echo "   nc -v $DEFAULT_IP $DEFAULT_PORT"
    echo ""
    echo "4. 📋 Hi3861设备端调试:"
    echo "   - 检查串口输出日志"
    echo "   - 确认WiFi连接成功"
    echo "   - 确认TCP服务器启动成功"
    echo "   - 检查IP地址是否正确"
    echo ""
}

# 主函数
main() {
    local target_ip="${1:-$DEFAULT_IP}"
    local target_port="${2:-$DEFAULT_PORT}"
    
    echo "========================================"
    echo "    Hi3861设备网络连接测试工具"
    echo "========================================"
    echo ""
    
    print_info "测试目标: $target_ip:$target_port"
    echo ""
    
    # 显示网络状态
    show_network_status
    
    # 1. 测试ping连通性
    local ping_ok=0
    if test_ping "$target_ip"; then
        ping_ok=1
    fi
    echo ""
    
    # 2. 测试端口连通性
    local port_ok=0
    if test_port "$target_ip" "$target_port"; then
        port_ok=1
    fi
    echo ""
    
    # 3. 测试Hi3861通信协议
    local comm_ok=0
    if [[ $port_ok -eq 1 ]]; then
        if test_communication "$target_ip" "$target_port"; then
            comm_ok=1
        fi
    else
        print_warning "跳过通信协议测试 - 端口不可用"
    fi
    echo ""
    
    # 显示测试结果汇总
    echo "========================================"
    print_info "测试结果汇总:"
    
    if [[ $ping_ok -eq 1 ]]; then
        print_success "✓ 网络连通性: 正常"
    else
        print_error "✗ 网络连通性: 失败"
    fi
    
    if [[ $port_ok -eq 1 ]]; then
        print_success "✓ 端口连通性: 正常"
    else
        print_error "✗ 端口连通性: 失败"
    fi
    
    if [[ $comm_ok -eq 1 ]]; then
        print_success "✓ 协议通信: 正常"
    else
        print_error "✗ 协议通信: 失败"
    fi
    
    echo "========================================"
    echo ""
    
    # 如果有问题，显示排查建议
    if [[ $ping_ok -eq 0 ]] || [[ $port_ok -eq 0 ]]; then
        show_troubleshooting
    fi
    
    # 返回合适的退出码
    if [[ $ping_ok -eq 1 ]] && [[ $port_ok -eq 1 ]]; then
        print_success "网络连接测试通过！设备应该可以正常连接。"
        exit 0
    else
        print_error "网络连接测试失败！请根据上述建议进行排查。"
        exit 1
    fi
}

# 检查是否请求帮助
if [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]]; then
    show_usage
    exit 0
fi

# 运行主函数
main "$@" 