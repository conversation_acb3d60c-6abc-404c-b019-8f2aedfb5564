#!/bin/bash

# RK3588智能表情交互系统 - 新构建脚本
# 适应重新整理后的项目结构

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="qt_cpp"
BUILD_DIR="build"
SRC_DIR="src"
DOCS_DIR="docs"

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_info "检查构建依赖..."
    
    # 检查Qt5
    if ! command -v qmake &> /dev/null; then
        print_error "qmake未找到，请安装Qt5开发环境"
        exit 1
    fi
    
    # 检查gcc
    if ! command -v gcc &> /dev/null; then
        print_error "gcc未找到，请安装GCC编译器"
        exit 1
    fi
    
    # 检查g++
    if ! command -v g++ &> /dev/null; then
        print_error "g++未找到，请安装G++编译器"
        exit 1
    fi
    
    print_success "所有依赖检查通过"
}

# 检查项目结构
check_project_structure() {
    print_info "检查项目结构..."
    
    # 检查必要的目录
    local dirs=("$SRC_DIR/core" "$SRC_DIR/speech" "$SRC_DIR/emotion" "$SRC_DIR/sensor" "$SRC_DIR/hardware" "$SRC_DIR/ui" "$SRC_DIR/include")
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            print_error "目录不存在: $dir"
            exit 1
        fi
    done
    
    # 检查关键文件
    local files=("$SRC_DIR/core/main.cpp" "qt_cpp.pro")
    
    for file in "${files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "文件不存在: $file"
            exit 1
        fi
    done
    
    print_success "项目结构检查通过"
}

# 清理构建目录
clean_build() {
    print_info "清理构建目录..."
    
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"/*
        print_success "构建目录已清理"
    else
        mkdir -p "$BUILD_DIR"
        print_info "创建构建目录: $BUILD_DIR"
    fi
}

# 生成Makefile
generate_makefile() {
    print_info "生成Makefile..."
    
    if qmake qt_cpp.pro -o "$BUILD_DIR/Makefile"; then
        print_success "Makefile生成成功"
    else
        print_error "Makefile生成失败"
        exit 1
    fi
}

# 编译项目
compile_project() {
    print_info "开始编译项目..."
    
    # 进入构建目录
    cd "$BUILD_DIR"
    
    # 使用make编译
    if make -j$(nproc); then
        print_success "项目编译成功"
    else
        print_error "项目编译失败"
        exit 1
    fi
    
    # 返回项目根目录
    cd ..
}

# 检查编译结果
check_build_result() {
    print_info "检查编译结果..."
    
    local executable="$BUILD_DIR/$PROJECT_NAME"
    
    if [ -f "$executable" ]; then
        print_success "可执行文件生成成功: $executable"
        
        # 显示文件信息
        print_info "文件信息:"
        ls -lh "$executable"
        
        # 检查依赖
        print_info "检查动态库依赖:"
        ldd "$executable" | head -10
        
    else
        print_error "可执行文件未生成"
        exit 1
    fi
}

# 运行测试
run_tests() {
    print_info "运行基本测试..."
    
    local executable="$BUILD_DIR/$PROJECT_NAME"
    
    if [ -f "$executable" ]; then
        # 检查是否可以正常启动（无界面模式）
        print_info "测试程序启动..."
        if timeout 5s "$executable" --help &>/dev/null || timeout 5s "$executable" --version &>/dev/null; then
            print_success "程序启动测试通过"
        else
            print_warning "程序启动测试未通过（这在无显示环境下是正常的）"
        fi
    else
        print_error "找不到可执行文件进行测试"
        exit 1
    fi
}

# 显示构建摘要  
show_build_summary() {
    print_info "构建摘要:"
    echo "=========================================="
    echo "项目名称: $PROJECT_NAME"
    echo "构建目录: $BUILD_DIR"
    echo "可执行文件: $BUILD_DIR/$PROJECT_NAME"
    echo "=========================================="
    
    # 显示目录结构
    print_info "项目结构:"
    if command -v tree &> /dev/null; then
        tree -L 2 -I '__pycache__|*.pyc|*.o|moc_*'
    else
        find . -type d -not -path '*/.*' | head -20 | sort
    fi
}

# 主函数
main() {
    print_info "开始构建 $PROJECT_NAME..."
    print_info "时间: $(date)"
    echo "=========================================="
    
    # 检查是否在正确的目录
    if [ ! -f "qt_cpp.pro" ]; then
        print_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行构建步骤
    check_dependencies
    check_project_structure
    clean_build
    generate_makefile
    compile_project
    check_build_result
    run_tests
    show_build_summary
    
    print_success "构建完成！"
    print_info "运行程序: cd $BUILD_DIR && ./$PROJECT_NAME"
}

# 处理命令行参数
case "${1:-}" in
    clean)
        print_info "仅清理构建目录"
        clean_build
        ;;
    check)
        print_info "仅检查依赖和项目结构"
        check_dependencies
        check_project_structure
        ;;
    *)
        main
        ;;
esac 