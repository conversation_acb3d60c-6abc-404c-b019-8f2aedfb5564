#!/bin/bash

echo "==============================================="
echo "RK3588智能表情交互系统 - 依赖安装脚本"
echo "==============================================="

# 设置错误时退出
set -e

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        echo "请以root权限运行此脚本: sudo $0"
        exit 1
    fi
}

# 更新包管理器
update_system() {
    echo "正在更新包管理器..."
    apt-get update
    echo "✓ 包管理器更新完成"
}

# 安装基础开发工具
install_basic_tools() {
    echo "正在安装基础开发工具..."
    apt-get install -y \
        build-essential \
        cmake \
        pkg-config \
        git \
        wget \
        curl \
        unzip \
        vim \
        htop
    echo "✓ 基础开发工具安装完成"
}

# 安装Qt开发环境
install_qt() {
    echo "正在安装Qt开发环境..."
    apt-get install -y \
        qt5-default \
        qtbase5-dev \
        qtchooser \
        qt5-qmake \
        qtbase5-dev-tools \
        qtcreator \
        qt5-doc \
        qt5-doc-html \
        qtbase5-examples
    echo "✓ Qt开发环境安装完成"
}

# 安装硬件接口库
install_hardware_libs() {
    echo "正在安装硬件接口库..."
    apt-get install -y \
        libi2c-dev \
        i2c-tools \
        spi-tools \
        wiringpi \
        libgpiod-dev \
        gpiod
    echo "✓ 硬件接口库安装完成"
}

# 安装音频支持
install_audio_support() {
    echo "正在安装音频支持..."
    apt-get install -y \
        alsa-utils \
        libasound2-dev \
        pulseaudio \
        pulseaudio-utils \
        espeak \
        espeak-data \
        festival \
        festvox-kallpc16k \
        sox \
        libsox-fmt-all
    echo "✓ 音频支持安装完成"
}

# 安装传感器库
install_sensor_libs() {
    echo "正在安装传感器库..."
    apt-get install -y \
        lm-sensors \
        libsensors4-dev \
        libraspberrypi-dev \
        python3-pip
    
    # 安装Python传感器库
    pip3 install \
        RPi.GPIO \
        adafruit-circuitpython-dht \
        adafruit-circuitpython-bmp280 \
        smbus
    echo "✓ 传感器库安装完成"
}

# 安装网络工具
install_network_tools() {
    echo "正在安装网络工具..."
    apt-get install -y \
        net-tools \
        wireless-tools \
        wpasupplicant \
        hostapd \
        dnsmasq \
        iptables \
        curl \
        wget
    echo "✓ 网络工具安装完成"
}

# 创建用户组和权限
setup_permissions() {
    echo "正在设置用户权限..."
    
    # 添加用户到必要的组
    usermod -a -G dialout,gpio,i2c,spi,audio,video $SUDO_USER 2>/dev/null || true
    
    # 设置GPIO权限
    if [ -f /etc/udev/rules.d/99-gpio.rules ]; then
        echo 'SUBSYSTEM=="gpio", GROUP="gpio", MODE="0660"' > /etc/udev/rules.d/99-gpio.rules
    fi
    
    # 设置I2C权限
    if [ -f /etc/udev/rules.d/99-i2c.rules ]; then
        echo 'SUBSYSTEM=="i2c-dev", GROUP="i2c", MODE="0660"' > /etc/udev/rules.d/99-i2c.rules
    fi
    
    # 设置SPI权限
    if [ -f /etc/udev/rules.d/99-spi.rules ]; then
        echo 'SUBSYSTEM=="spidev", GROUP="spi", MODE="0660"' > /etc/udev/rules.d/99-spi.rules
    fi
    
    echo "✓ 用户权限设置完成"
}

# 安装科大讯飞SDK依赖
install_xunfei_deps() {
    echo "正在安装科大讯飞SDK依赖..."
    apt-get install -y \
        libssl-dev \
        libcurl4-openssl-dev \
        libjson-c-dev \
        libpthread-stubs0-dev
    echo "✓ 科大讯飞SDK依赖安装完成"
}

# 配置系统服务
configure_services() {
    echo "正在配置系统服务..."
    
    # 启用I2C
    if ! grep -q "^i2c-dev" /etc/modules; then
        echo "i2c-dev" >> /etc/modules
    fi
    
    # 启用SPI
    if ! grep -q "^spi-dev" /etc/modules; then
        echo "spi-dev" >> /etc/modules
    fi
    
    # 启用GPIO
    if ! grep -q "^gpio" /etc/modules; then
        echo "gpio" >> /etc/modules
    fi
    
    echo "✓ 系统服务配置完成"
}

# 检验安装
verify_installation() {
    echo "正在检验安装..."
    
    # 检查Qt
    if command -v qmake >/dev/null 2>&1; then
        echo "✓ Qt安装成功: $(qmake --version | head -1)"
    else
        echo "✗ Qt安装失败"
        return 1
    fi
    
    # 检查编译工具
    if command -v g++ >/dev/null 2>&1; then
        echo "✓ 编译工具安装成功: $(g++ --version | head -1)"
    else
        echo "✗ 编译工具安装失败"
        return 1
    fi
    
    # 检查硬件工具
    if command -v i2cdetect >/dev/null 2>&1; then
        echo "✓ I2C工具安装成功"
    else
        echo "✗ I2C工具安装失败"
    fi
    
    if command -v gpio >/dev/null 2>&1; then
        echo "✓ GPIO工具安装成功"
    else
        echo "✗ GPIO工具安装失败"
    fi
    
    echo "✓ 安装检验完成"
}

# 主安装流程
main() {
    echo "开始安装依赖..."
    
    check_root
    update_system
    install_basic_tools
    install_qt
    install_hardware_libs
    install_audio_support
    install_sensor_libs
    install_network_tools
    install_xunfei_deps
    setup_permissions
    configure_services
    verify_installation
    
    echo ""
    echo "==============================================="
    echo "依赖安装完成！"
    echo "==============================================="
    echo "请重启系统以使权限设置生效："
    echo "sudo reboot"
    echo ""
    echo "重启后可以运行项目构建："
    echo "./build_test.sh"
}

# 运行主函数
main "$@" 