#ifndef RK3588_GPIO_PINS_H
#define RK3588_GPIO_PINS_H

// RK3588 GPIO引脚定义
// 基于RK3588芯片手册的GPIO配置

// 网络相关引脚
#define ETHERNET_RST_PIN    89    // GPIO2_C1 - 以太网复位
#define WIFI_EN_PIN         90    // GPIO2_C2 - WiFi使能
#define WIFI_RST_PIN        91    // GPIO2_C3 - WiFi复位

// 显示屏控制引脚（通过网络控制Hi3861）
#define DISPLAY1_STATUS_PIN     97    // GPIO2_D1 - 显示屏1状态
#define DISPLAY1_READY_PIN      98    // GPIO2_D2 - 显示屏1就绪
#define DISPLAY2_STATUS_PIN     100   // GPIO2_D4 - 显示屏2状态
#define DISPLAY2_READY_PIN      101   // GPIO2_D5 - 显示屏2就绪

// I2C相关引脚（传感器）
#define I2C1_SDA_PIN    103   // GPIO2_D7
#define I2C1_SCL_PIN    104   // GPIO3_A0

#define I2C2_SDA_PIN    105   // GPIO3_A1
#define I2C2_SCL_PIN    106   // GPIO3_A2

// 传感器相关引脚
#define TEMP_SENSOR_INT_PIN     107   // GPIO3_A3
#define HUMIDITY_SENSOR_INT_PIN 108   // GPIO3_A4
#define LIGHT_SENSOR_INT_PIN    109   // GPIO3_A5

// LED控制引脚
#define LED_PWR_PIN     111   // GPIO3_A7
#define LED_STATUS_PIN  112   // GPIO3_B0
#define LED_ERROR_PIN   113   // GPIO3_B1
#define LED_NETWORK_PIN 114   // GPIO3_B2 - 网络状态指示

// 音频相关引脚
#define I2S_LRCK_PIN    115   // GPIO3_B3
#define I2S_BCLK_PIN    116   // GPIO3_B4
#define I2S_SDATA_PIN   117   // GPIO3_B5

// 电源控制引脚
#define PWR_EN_PIN      118   // GPIO3_B6
#define PWR_GOOD_PIN    119   // GPIO3_B7

// 调试用引脚
#define DEBUG_PIN1      120   // GPIO3_C0
#define DEBUG_PIN2      121   // GPIO3_C1

// GPIO访问宏
#define GPIO_EXPORT_PATH        "/sys/class/gpio/export"
#define GPIO_UNEXPORT_PATH      "/sys/class/gpio/unexport"
#define GPIO_BASE_PATH          "/sys/class/gpio/gpio"

// GPIO方向定义
#define GPIO_DIRECTION_IN       "in"
#define GPIO_DIRECTION_OUT      "out"

// GPIO值定义
#define GPIO_VALUE_LOW          "0"
#define GPIO_VALUE_HIGH         "1"

// GPIO边缘触发定义
#define GPIO_EDGE_NONE          "none"
#define GPIO_EDGE_RISING        "rising"
#define GPIO_EDGE_FALLING       "falling"
#define GPIO_EDGE_BOTH          "both"

// GPIO活动状态定义
#define GPIO_ACTIVE_LOW         0
#define GPIO_ACTIVE_HIGH        1

// 网络状态定义
#define NETWORK_STATUS_DISCONNECTED 0
#define NETWORK_STATUS_CONNECTING   1
#define NETWORK_STATUS_CONNECTED    2
#define NETWORK_STATUS_ERROR        3

// 函数声明
#ifdef __cplusplus
extern "C" {
#endif

int gpio_export(int pin);
int gpio_unexport(int pin);
int gpio_set_direction(int pin, const char* direction);
int gpio_set_value(int pin, int value);
int gpio_get_value(int pin);
int gpio_set_edge(int pin, const char* edge);
int gpio_is_exported(int pin);

// 网络状态指示函数
int set_network_status_led(int status);
int get_display_status(int display_index);

#ifdef __cplusplus
}
#endif

#endif // RK3588_GPIO_PINS_H 