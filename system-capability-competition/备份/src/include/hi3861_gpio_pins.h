#ifndef HI3861_GPIO_PINS_H
#define HI3861_GPIO_PINS_H

// Hi3861 GPIO引脚定义
// 基于Hi3861芯片手册的GPIO配置

// 表情显示相关引脚
#define HI3861_GPIO_0    0    // 表情LED 1
#define HI3861_GPIO_1    1    // 表情LED 2
#define HI3861_GPIO_2    2    // 表情LED 3
#define HI3861_GPIO_3    3    // 表情LED 4
#define HI3861_GPIO_4    4    // 表情LED 5
#define HI3861_GPIO_5    5    // 表情LED 6
#define HI3861_GPIO_6    6    // 表情LED 7
#define HI3861_GPIO_7    7    // 表情LED 8
#define HI3861_GPIO_8    8    // 表情LED 9
#define HI3861_GPIO_9    9    // 表情LED 10
#define HI3861_GPIO_10   10   // 表情LED 11
#define HI3861_GPIO_11   11   // 表情LED 12

// WiFi网络通信引脚
#define HI3861_WIFI_EN   12   // WiFi使能
#define HI3861_WIFI_RST  13   // WiFi复位

// 控制信号引脚
#define HI3861_RESET_PIN 16   // 复位引脚
#define HI3861_STATUS_PIN 17  // 状态指示引脚
#define HI3861_READY_PIN 18   // 就绪指示引脚

// 调试引脚
#define HI3861_DEBUG_TX  19   // 调试串口发送
#define HI3861_DEBUG_RX  20   // 调试串口接收

// LED控制模式定义
#define LED_MODE_OFF     0    // 关闭
#define LED_MODE_ON      1    // 常亮
#define LED_MODE_BLINK   2    // 闪烁
#define LED_MODE_FADE    3    // 渐变
#define LED_MODE_PULSE   4    // 脉冲

// 表情图案定义
typedef struct {
    unsigned char pattern[12];  // 12个LED的状态
    unsigned char brightness;   // 亮度 (0-255)
    unsigned char mode;         // 显示模式
    unsigned short duration;    // 持续时间 (ms)
} emotion_pattern_t;

// 预定义表情图案
extern const emotion_pattern_t EMOTION_HAPPY;
extern const emotion_pattern_t EMOTION_SAD;
extern const emotion_pattern_t EMOTION_ANGRY;
extern const emotion_pattern_t EMOTION_SURPRISED;
extern const emotion_pattern_t EMOTION_NEUTRAL;
extern const emotion_pattern_t EMOTION_FEAR;

// 网络通信协议定义
#define NET_CMD_SET_EMOTION      0x01
#define NET_CMD_SET_BRIGHTNESS   0x02
#define NET_CMD_SET_PATTERN      0x03
#define NET_CMD_GET_STATUS       0x04
#define NET_CMD_RESET            0x05
#define NET_CMD_TEST             0x06

#define NET_RESP_OK              0x00
#define NET_RESP_ERROR           0xFF
#define NET_RESP_BUSY            0xFE

// 网络配置
#define HI3861_DISCOVERY_PORT    8888
#define HI3861_COMMAND_PORT      8889
#define HI3861_DEVICE_TYPE       "HI3861_DISPLAY"

// 函数声明
#ifdef __cplusplus
extern "C" {
#endif

// Hi3861控制函数
int hi3861_init(void);
int hi3861_deinit(void);
int hi3861_set_emotion(const emotion_pattern_t* pattern);
int hi3861_set_brightness(unsigned char brightness);
int hi3861_set_led(int led_index, unsigned char value);
int hi3861_get_status(void);
int hi3861_reset(void);
int hi3861_test_pattern(void);

// 网络通信函数
int hi3861_network_init(void);
int hi3861_send_response(const char* json_response);
int hi3861_process_command(const char* json_command);

#ifdef __cplusplus
}
#endif

#endif // HI3861_GPIO_PINS_H 