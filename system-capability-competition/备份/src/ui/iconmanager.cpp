#include "iconmanager.h"
#include <QApplication>
#include <QFontDatabase>
#include <QDebug>

// 静态成员变量定义
QMap<IconManager::IconType, QString> IconManager::m_unicodeIcons;
QMap<IconManager::IconType, QString> IconManager::m_compatibleIcons;
bool IconManager::m_globalCompatibleMode = false;

QString IconManager::getIcon(IconType type, bool useCompatible)
{
    // 初始化图标映射（如果未初始化）
    if (m_unicodeIcons.isEmpty()) {
        initializeIcons();
    }
    
    // 决定使用哪种模式
    bool useCompat = useCompatible || m_globalCompatibleMode || shouldUseCompatibleMode();
    
    if (useCompat) {
        return m_compatibleIcons.value(type, "[?]");
    } else {
        return m_unicodeIcons.value(type, "?");
    }
}

QString IconManager::getHtmlIcon(IconType type, const QString& color, int size, bool useCompatible)
{
    QString iconText = getIcon(type, useCompatible);
    
    // 决定使用哪种模式
    bool useCompat = useCompatible || m_globalCompatibleMode || shouldUseCompatibleMode();
    
    if (useCompat) {
        // ASCII兼容模式：使用带背景的文本样式
        QString bgColor;
        QString textColor = "#ffffff";
        
        switch (type) {
            case ROBOT_ICON:
                bgColor = "#4a90e2"; break;
            case USER_ICON:
                bgColor = "#7ed321"; break;
            case MUSIC_ICON:
                bgColor = "#f5a623"; break;
            case PLAY_ICON:
                bgColor = "#50e3c2"; break;
            case PAUSE_ICON:
                bgColor = "#f5a623"; break;
            case STOP_ICON:
                bgColor = "#d0021b"; break;
            case VOLUME_UP_ICON:
                bgColor = "#7ed321"; break;
            case VOLUME_MUTE_ICON:
                bgColor = "#9013fe"; break;
            case LED_ICON:
                bgColor = "#f8e71c"; textColor = "#333333"; break;
            case CAMERA_ICON:
                bgColor = "#50e3c2"; break;
            case TEMPERATURE_ICON:
                bgColor = "#bd10e0"; break;
            case DEBUG_ICON:
                bgColor = "#b8e986"; textColor = "#333333"; break;
            case INFO_ICON:
                bgColor = "#4a90e2"; break;
            case WARNING_ICON:
                bgColor = "#f5a623"; break;
            case SUCCESS_ICON:
                bgColor = "#7ed321"; break;
            case ERROR_ICON:
                bgColor = "#d0021b"; break;
            default:
                bgColor = "#666666"; break;
        }
        
        return QString(
            "<span style='background-color: %1; color: %2; padding: 2px 6px; "
            "border-radius: 4px; font-size: %3px; font-weight: bold; "
            "font-family: monospace;'>%4</span>"
        ).arg(bgColor).arg(textColor).arg(size).arg(iconText);
    } else {
        // Unicode模式：使用emoji
        return QString(
            "<span style='color: %1; font-size: %2px;'>%3</span>"
        ).arg(color).arg(size).arg(iconText);
    }
}

bool IconManager::shouldUseCompatibleMode()
{
    // 检查是否为ARM平台
    #ifdef __arm__
        return true;
    #endif
    
    // 检查是否有支持emoji的字体
    QFontDatabase fontDb;
    QStringList families = fontDb.families();
    
    // 常见的支持emoji的字体
    QStringList emojiFonts = {
        "Noto Color Emoji",
        "Apple Color Emoji", 
        "Segoe UI Emoji",
        "Twitter Color Emoji",
        "EmojiOne Color"
    };
    
    for (const QString& font : emojiFonts) {
        if (families.contains(font, Qt::CaseInsensitive)) {
            return false; // 找到支持emoji的字体
        }
    }
    
    // 如果没有找到支持emoji的字体，使用兼容模式
    qDebug() << "[IconManager] 未找到支持emoji的字体，启用兼容模式";
    return true;
}

void IconManager::setGlobalCompatibleMode(bool compatible)
{
    m_globalCompatibleMode = compatible;
    qDebug() << "[IconManager] 全局兼容模式设置为:" << compatible;
}

void IconManager::initializeIcons()
{
    // Unicode图标（emoji）
    m_unicodeIcons[ROBOT_ICON] = "🤖";
    m_unicodeIcons[USER_ICON] = "👤";
    m_unicodeIcons[MUSIC_ICON] = "🎵";
    m_unicodeIcons[PLAY_ICON] = "▶️";
    m_unicodeIcons[PAUSE_ICON] = "⏸️";
    m_unicodeIcons[STOP_ICON] = "⏹️";
    m_unicodeIcons[VOLUME_UP_ICON] = "🔊";
    m_unicodeIcons[VOLUME_MUTE_ICON] = "🔇";
    m_unicodeIcons[LED_ICON] = "💡";
    m_unicodeIcons[CAMERA_ICON] = "📷";
    m_unicodeIcons[TEMPERATURE_ICON] = "🌡️";
    m_unicodeIcons[DEBUG_ICON] = "🔧";
    m_unicodeIcons[INFO_ICON] = "ℹ️";
    m_unicodeIcons[WARNING_ICON] = "⚠️";
    m_unicodeIcons[SUCCESS_ICON] = "✅";
    m_unicodeIcons[ERROR_ICON] = "❌";
    
    // 兼容模式图标（ASCII文本）
    m_compatibleIcons[ROBOT_ICON] = "AI";
    m_compatibleIcons[USER_ICON] = "ME";
    m_compatibleIcons[MUSIC_ICON] = "♪";
    m_compatibleIcons[PLAY_ICON] = ">";
    m_compatibleIcons[PAUSE_ICON] = "||";
    m_compatibleIcons[STOP_ICON] = "[]";
    m_compatibleIcons[VOLUME_UP_ICON] = "♫";
    m_compatibleIcons[VOLUME_MUTE_ICON] = "X";
    m_compatibleIcons[LED_ICON] = "LED";
    m_compatibleIcons[CAMERA_ICON] = "CAM";
    m_compatibleIcons[TEMPERATURE_ICON] = "°C";
    m_compatibleIcons[DEBUG_ICON] = "DBG";
    m_compatibleIcons[INFO_ICON] = "i";
    m_compatibleIcons[WARNING_ICON] = "!";
    m_compatibleIcons[SUCCESS_ICON] = "OK";
    m_compatibleIcons[ERROR_ICON] = "ERR";
}