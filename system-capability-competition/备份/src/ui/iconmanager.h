#ifndef ICONMANAGER_H
#define ICONMANAGER_H

#include <QString>
#include <QMap>

/**
 * @brief 图标管理器类
 * 
 * 解决ARM开发板上emoji图标显示问题
 * 提供ASCII文本和Unicode符号的兼容方案
 */
class IconManager
{
public:
    enum IconType {
        // 用户和AI图标
        ROBOT_ICON,         // 🤖 机器人
        USER_ICON,          // 👤 用户
        
        // 音乐播放图标
        MUSIC_ICON,         // 🎵 音乐
        PLAY_ICON,          // ▶️ 播放
        PAUSE_ICON,         // ⏸️ 暂停
        STOP_ICON,          // ⏹️ 停止
        VOLUME_UP_ICON,     // 🔊 音量高
        VOLUME_MUTE_ICON,   // 🔇 静音
        
        // 硬件设备图标
        LED_ICON,           // 💡 LED灯
        CAMERA_ICON,        // 📷 摄像头
        TEMPERATURE_ICON,   // 🌡️ 温度
        
        // 调试和提示图标
        DEBUG_ICON,         // 🔧 调试
        INFO_ICON,          // ℹ️ 信息
        WARNING_ICON,       // ⚠️ 警告
        SUCCESS_ICON,       // ✅ 成功
        ERROR_ICON          // ❌ 错误
    };
    
    /**
     * @brief 获取图标字符串
     * @param type 图标类型
     * @param useCompatible 是否使用兼容模式（ASCII字符）
     * @return 图标字符串
     */
    static QString getIcon(IconType type, bool useCompatible = false);
    
    /**
     * @brief 获取带样式的HTML图标
     * @param type 图标类型
     * @param color 颜色
     * @param size 大小（像素）
     * @param useCompatible 是否使用兼容模式
     * @return HTML格式的图标
     */
    static QString getHtmlIcon(IconType type, const QString& color = "#333333", 
                              int size = 16, bool useCompatible = false);
    
    /**
     * @brief 检测是否应该使用兼容模式
     * @return true表示应该使用兼容模式
     */
    static bool shouldUseCompatibleMode();
    
    /**
     * @brief 设置全局兼容模式
     * @param compatible 是否启用兼容模式
     */
    static void setGlobalCompatibleMode(bool compatible);

private:
    static QMap<IconType, QString> m_unicodeIcons;
    static QMap<IconType, QString> m_compatibleIcons;
    static bool m_globalCompatibleMode;
    
    static void initializeIcons();
};

#endif // ICONMANAGER_H 