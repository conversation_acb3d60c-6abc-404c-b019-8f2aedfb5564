/* =======================================================
   RK3588 智能交互系统 - 现代化样式表
   ======================================================= */

/* ======================= 全局样式 ======================= */
QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8fafc, stop:1 #e2e8f0);
    color: #334155;
    font-family: "Microsoft YaHei", "SimHei", Arial, sans-serif;
}

QWidget {
    background-color: transparent;
    color: #334155;
}

/* ======================= 标题栏样式 ======================= */
#label_header_title {
    font-size: 64px;
    font-weight: 900;
    color: white;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #6366f1, stop:0.5 #8b5cf6, stop:1 #06b6d4);
    padding: 40px 20px;
    border-radius: 0px;
    border-bottom: 5px solid rgba(255,255,255,0.3);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

/* ======================= 卡片容器样式 ======================= */
.content-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    border: 1px solid rgba(203, 213, 225, 0.5);
    margin: 15px;
    padding: 25px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* ======================= 页面标题样式 ======================= */
.page-title {
    font-size: 48px;
    font-weight: bold;
    color: white;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #667eea, stop:1 #764ba2);
    padding: 30px;
    border-radius: 16px;
    margin-bottom: 20px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

/* ======================= 主要按钮样式 ======================= */
QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #3b82f6, stop:1 #1d4ed8);
    border: none;
    border-radius: 16px;
    padding: 25px 35px;
    color: white;
    font-weight: bold;
    font-size: 38px;
    min-height: 80px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #2563eb, stop:1 #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1d4ed8, stop:1 #1e3a8a);
    transform: translateY(0px);
    box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);
}

QPushButton:disabled {
    background: #94a3b8;
    color: #cbd5e1;
    box-shadow: none;
}

/* ======================= 特殊功能按钮 ======================= */
.btn-success {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #10b981, stop:1 #059669);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-success:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #059669, stop:1 #047857);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.btn-danger {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ef4444, stop:1 #dc2626);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #dc2626, stop:1 #b91c1c);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

.btn-warning {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f59e0b, stop:1 #d97706);
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.btn-warning:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #d97706, stop:1 #b45309);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
}

/* ======================= 标签样式 ======================= */
QLabel {
    color: #475569;
    font-size: 36px;
    background-color: transparent;
    padding: 8px;
}

.status-label {
    font-size: 32px;
    padding: 15px 20px;
    border-radius: 12px;
    background: rgba(241, 245, 249, 0.8);
    border: 1px solid #e2e8f0;
    margin: 10px 0;
}

.info-label {
    color: #0f766e;
    background: rgba(204, 251, 241, 0.8);
    border: 1px solid #14b8a6;
}

.warning-label {
    color: #a16207;
    background: rgba(254, 243, 199, 0.8);
    border: 1px solid #f59e0b;
}

.error-label {
    color: #be123c;
    background: rgba(254, 226, 226, 0.8);
    border: 1px solid #ef4444;
}

/* ======================= 底部导航栏 ======================= */
#navWidget {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1e293b, stop:1 #0f172a);
    border-top: 3px solid #334155;
    min-height: 160px;
    max-height: 160px;
}

#navWidget QPushButton {
    background-color: transparent;
    border: none;
    color: #94a3b8;
    text-align: center;
    padding: 20px 10px;
    font-size: 32px;
    font-weight: bold;
    border-radius: 12px;
    margin: 8px 4px;
    transition: all 0.3s ease;
}

#navWidget QPushButton:hover {
    background: rgba(59, 130, 246, 0.2);
    color: #e2e8f0;
    transform: scale(1.05);
}

#navWidget QPushButton:pressed {
    background: rgba(59, 130, 246, 0.4);
    transform: scale(0.98);
}

#navWidget QPushButton:checked {
    color: white;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #3b82f6, stop:1 #1d4ed8);
    border: 2px solid #60a5fa;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

/* ======================= 输入框样式 ======================= */
QTextEdit, QLineEdit {
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 15px;
    font-size: 34px;
    color: #334155;
    selection-background-color: #bfdbfe;
}

QTextEdit:focus, QLineEdit:focus {
    border: 2px solid #3b82f6;
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ======================= 滚动条样式 ======================= */
QScrollBar:vertical {
    background: rgba(241, 245, 249, 0.8);
    width: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: #94a3b8;
    border-radius: 6px;
    min-height: 30px;
}

QScrollBar::handle:vertical:hover {
    background: #64748b;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0;
}

/* ======================= 特殊组件样式 ======================= */
.emotion-display {
    background: qradial-gradient(circle, 
        rgba(255, 255, 255, 0.95) 0%, 
        rgba(241, 245, 249, 0.9) 100%);
    border: 3px solid #e2e8f0;
    border-radius: 20px;
    min-height: 200px;
    text-align: center;
    font-size: 120px;
    padding: 30px;
    margin: 20px;
}

.sensor-card {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    padding: 25px;
    margin: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.chat-bubble-user {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #3b82f6, stop:1 #6366f1);
    color: white;
    border-radius: 20px 20px 8px 20px;
    padding: 15px 20px;
    margin: 8px 60px 8px 20px;
    font-size: 32px;
}

.chat-bubble-ai {
    background: rgba(241, 245, 249, 0.9);
    color: #334155;
    border: 1px solid #e2e8f0;
    border-radius: 20px 20px 20px 8px;
    padding: 15px 20px;
    margin: 8px 20px 8px 60px;
    font-size: 32px;
}

/* ======================= 动画效果 ======================= */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse-animation {
    animation: pulse 2s ease-in-out infinite;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
} 