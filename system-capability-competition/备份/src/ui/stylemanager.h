#ifndef STYLEMANAGER_H
#define STYLEMANAGER_H

#include <QObject>
#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include <QString>
#include <QPropertyAnimation>
#include <QGraphicsDropShadowEffect>
#include <QTimer>

/**
 * @brief 样式管理器类 - 负责管理界面样式和动画效果
 */
class StyleManager : public QObject
{
    Q_OBJECT

public:
    explicit StyleManager(QObject *parent = nullptr);
    
    // 样式应用方法
    static void applyModernStyle(QWidget *widget);
    static void loadStyleSheet(QWidget *widget, const QString &styleFile);
    
    // 按钮样式设置
    static void setButtonStyle(QPushButton *button, const QString &style = "primary");
    static void setButtonStyleSuccess(QPushButton *button);
    static void setButtonStyleDanger(QPushButton *button);
    static void setButtonStyleWarning(QPushButton *button);
    
    // 标签样式设置
    static void setLabelStyle(QLabel *label, const QString &style = "normal");
    static void setLabelStyleInfo(QLabel *label);
    static void setLabelStyleWarning(QLabel *label);
    static void setLabelStyleError(QLabel *label);
    
    // 动画效果
    static void addHoverEffect(QWidget *widget);
    static void addClickEffect(QWidget *widget);
    static void addPulseEffect(QWidget *widget);
    static void addFadeInEffect(QWidget *widget);
    static void addShadowEffect(QWidget *widget);
    
    // 主题切换
    static void applyLightTheme(QWidget *widget);
    static void applyDarkTheme(QWidget *widget);
    
    // 卡片样式
    static void applyCardStyle(QWidget *widget);
    static void applyGlassEffect(QWidget *widget);
    
    // 颜色常量
    struct Colors {
        static const QString PRIMARY;
        static const QString SUCCESS;
        static const QString DANGER;
        static const QString WARNING;
        static const QString INFO;
        static const QString LIGHT;
        static const QString DARK;
        static const QString BACKGROUND;
        static const QString TEXT;
    };

private:
    static void createHoverAnimation(QWidget *widget);
    static void createClickAnimation(QWidget *widget);
    static QString getStyleSheet(const QString &styleFile);
    
signals:
    void themeChanged(const QString &themeName);
    
public slots:
    void onThemeChangeRequested(const QString &themeName);
};

#endif // STYLEMANAGER_H 