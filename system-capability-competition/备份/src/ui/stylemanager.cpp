#include "stylemanager.h"
#include <QFile>
#include <QTextStream>
#include <QApplication>
#include <QPropertyAnimation>
#include <QGraphicsDropShadowEffect>
#include <QEasingCurve>
#include <QDebug>

// 颜色常量定义
const QString StyleManager::Colors::PRIMARY = "#3b82f6";
const QString StyleManager::Colors::SUCCESS = "#10b981";
const QString StyleManager::Colors::DANGER = "#ef4444";
const QString StyleManager::Colors::WARNING = "#f59e0b";
const QString StyleManager::Colors::INFO = "#06b6d4";
const QString StyleManager::Colors::LIGHT = "#f8fafc";
const QString StyleManager::Colors::DARK = "#1e293b";
const QString StyleManager::Colors::BACKGROUND = "#f1f5f9";
const QString StyleManager::Colors::TEXT = "#334155";

StyleManager::StyleManager(QObject *parent)
    : QObject(parent)
{
}

void StyleManager::applyModernStyle(QWidget *widget)
{
    loadStyleSheet(widget, ":/styles/modern_style.qss");
    addShadowEffect(widget);
}

void StyleManager::loadStyleSheet(QWidget *widget, const QString &styleFile)
{
    QString style = getStyleSheet(styleFile);
    if (!style.isEmpty()) {
        widget->setStyleSheet(style);
    }
}

QString StyleManager::getStyleSheet(const QString &styleFile)
{
    QFile file(styleFile);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "无法打开样式文件:" << styleFile;
        return QString();
    }
    
    QTextStream in(&file);
    QString content = in.readAll();
    file.close();
    
    return content;
}

void StyleManager::setButtonStyle(QPushButton *button, const QString &style)
{
    if (!button) return;
    
    if (style == "primary") {
        button->setProperty("class", "btn-primary");
    } else if (style == "success") {
        setButtonStyleSuccess(button);
    } else if (style == "danger") {
        setButtonStyleDanger(button);
    } else if (style == "warning") {
        setButtonStyleWarning(button);
    }
    
    addHoverEffect(button);
    addClickEffect(button);
}

void StyleManager::setButtonStyleSuccess(QPushButton *button)
{
    if (!button) return;
    
    button->setStyleSheet(QString(
        "QPushButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 %1, stop:1 #059669);"
        "    border: none;"
        "    border-radius: 16px;"
        "    padding: 25px 35px;"
        "    color: white;"
        "    font-weight: bold;"
        "    font-size: 38px;"
        "    min-height: 80px;"
        "    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);"
        "}"
        "QPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #059669, stop:1 #047857);"
        "    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);"
        "}"
        "QPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #047857, stop:1 #065f46);"
        "}"
    ).arg(Colors::SUCCESS));
}

void StyleManager::setButtonStyleDanger(QPushButton *button)
{
    if (!button) return;
    
    button->setStyleSheet(QString(
        "QPushButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 %1, stop:1 #dc2626);"
        "    border: none;"
        "    border-radius: 16px;"
        "    padding: 25px 35px;"
        "    color: white;"
        "    font-weight: bold;"
        "    font-size: 38px;"
        "    min-height: 80px;"
        "    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);"
        "}"
        "QPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #dc2626, stop:1 #b91c1c);"
        "    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);"
        "}"
        "QPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #b91c1c, stop:1 #991b1b);"
        "}"
    ).arg(Colors::DANGER));
}

void StyleManager::setButtonStyleWarning(QPushButton *button)
{
    if (!button) return;
    
    button->setStyleSheet(QString(
        "QPushButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 %1, stop:1 #d97706);"
        "    border: none;"
        "    border-radius: 16px;"
        "    padding: 25px 35px;"
        "    color: white;"
        "    font-weight: bold;"
        "    font-size: 38px;"
        "    min-height: 80px;"
        "    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);"
        "}"
        "QPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #d97706, stop:1 #b45309);"
        "    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);"
        "}"
        "QPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #b45309, stop:1 #92400e);"
        "}"
    ).arg(Colors::WARNING));
}

void StyleManager::setLabelStyleInfo(QLabel *label)
{
    if (!label) return;
    
    label->setStyleSheet(
        "QLabel {"
        "    color: #0f766e;"
        "    background: rgba(204, 251, 241, 0.8);"
        "    border: 1px solid #14b8a6;"
        "    border-radius: 12px;"
        "    padding: 15px 20px;"
        "    font-size: 32px;"
        "    margin: 10px 0;"
        "}"
    );
}

void StyleManager::setLabelStyleWarning(QLabel *label)
{
    if (!label) return;
    
    label->setStyleSheet(
        "QLabel {"
        "    color: #a16207;"
        "    background: rgba(254, 243, 199, 0.8);"
        "    border: 1px solid #f59e0b;"
        "    border-radius: 12px;"
        "    padding: 15px 20px;"
        "    font-size: 32px;"
        "    margin: 10px 0;"
        "}"
    );
}

void StyleManager::setLabelStyleError(QLabel *label)
{
    if (!label) return;
    
    label->setStyleSheet(
        "QLabel {"
        "    color: #be123c;"
        "    background: rgba(254, 226, 226, 0.8);"
        "    border: 1px solid #ef4444;"
        "    border-radius: 12px;"
        "    padding: 15px 20px;"
        "    font-size: 32px;"
        "    margin: 10px 0;"
        "}"
    );
}

void StyleManager::addHoverEffect(QWidget *widget)
{
    if (!widget) return;
    
    // 为按钮添加悬停效果动画
    widget->setAttribute(Qt::WA_Hover, true);
    createHoverAnimation(widget);
}

void StyleManager::addClickEffect(QWidget *widget)
{
    if (!widget) return;
    
    createClickAnimation(widget);
}

void StyleManager::addPulseEffect(QWidget *widget)
{
    if (!widget) return;
    
    QPropertyAnimation *animation = new QPropertyAnimation(widget, "geometry");
    animation->setDuration(2000);
    animation->setLoopCount(-1); // 无限循环
    animation->setEasingCurve(QEasingCurve::InOutQuad);
    
    QRect originalGeometry = widget->geometry();
    QRect scaledGeometry = originalGeometry.adjusted(-5, -5, 5, 5);
    
    animation->setKeyValueAt(0, originalGeometry);
    animation->setKeyValueAt(0.5, scaledGeometry);
    animation->setKeyValueAt(1, originalGeometry);
    
    animation->start(QAbstractAnimation::DeleteWhenStopped);
}

void StyleManager::addFadeInEffect(QWidget *widget)
{
    if (!widget) return;
    
    QPropertyAnimation *animation = new QPropertyAnimation(widget, "windowOpacity");
    animation->setDuration(500);
    animation->setEasingCurve(QEasingCurve::OutQuad);
    animation->setStartValue(0.0);
    animation->setEndValue(1.0);
    
    animation->start(QAbstractAnimation::DeleteWhenStopped);
}

void StyleManager::addShadowEffect(QWidget *widget)
{
    if (!widget) return;
    
    QGraphicsDropShadowEffect *shadow = new QGraphicsDropShadowEffect();
    shadow->setBlurRadius(20);
    shadow->setXOffset(0);
    shadow->setYOffset(4);
    shadow->setColor(QColor(0, 0, 0, 50));
    
    widget->setGraphicsEffect(shadow);
}

void StyleManager::applyCardStyle(QWidget *widget)
{
    if (!widget) return;
    
    widget->setStyleSheet(
        "QWidget {"
        "    background: rgba(255, 255, 255, 0.95);"
        "    border-radius: 20px;"
        "    border: 1px solid rgba(203, 213, 225, 0.5);"
        "    margin: 15px;"
        "    padding: 25px;"
        "}"
    );
    
    addShadowEffect(widget);
}

void StyleManager::applyGlassEffect(QWidget *widget)
{
    if (!widget) return;
    
    widget->setStyleSheet(
        "QWidget {"
        "    background: rgba(255, 255, 255, 0.1);"
        "    border: 1px solid rgba(255, 255, 255, 0.2);"
        "    border-radius: 20px;"
        "    backdrop-filter: blur(10px);"
        "}"
    );
}

void StyleManager::createHoverAnimation(QWidget *widget)
{
    // 这里可以添加更复杂的悬停动画逻辑
    // 由于Qt的限制，主要通过CSS实现
}

void StyleManager::createClickAnimation(QWidget *widget)
{
    // 这里可以添加点击动画逻辑
    // 可以通过属性动画实现按下效果
}

void StyleManager::onThemeChangeRequested(const QString &themeName)
{
    emit themeChanged(themeName);
} 