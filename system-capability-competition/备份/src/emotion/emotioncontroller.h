#ifndef EMOTIONCONTROLLER_H
#define EMOTIONCONTROLLER_H

#include <QObject>
#include <QTimer>
#include <QMap>
#include <QColor>
#include <QPropertyAnimation>
#include <QSequentialAnimationGroup>

class NetworkController;

class EmotionController : public QObject
{
    Q_OBJECT

public:
    explicit EmotionController(QObject *parent = nullptr);
    ~EmotionController();

    enum EmotionType {
        Neutral,
        Happy,
        Sad,
        Angry,
        Surprised,
        Fear,
        Sleep,  // 新增休眠状态
        Custom
    };

    struct EmotionCommand {
        QString command;      // 发送给Hi3861的指令
        QString displayName;  // 界面显示名称
        int defaultIntensity; // 默认强度
        bool hasAnimation;    // 是否有动画
    };

public slots:
    void setEmotion(const QString &emotion, int intensity = 50);
    void setEmotion(EmotionType emotion, int intensity = 50);
    void setSleepMode(); // 休眠模式
    void testDeviceConnection(); // 测试设备连接
    void setIntensity(int intensity);
    void setBrightness(int brightness);

    // 设备连接状态
    bool isLeftEyeConnected() const;
    bool isRightEyeConnected() const;
    QString getConnectionStatus() const;

signals:
    void emotionChanged(const QString &emotion, int intensity);
    void connectionStatusChanged(bool leftEye, bool rightEye);
    void deviceTestResult(int deviceIndex, bool success);

private slots:
    void onDeviceStatusChanged(int deviceIndex, bool online);
    void onConnectionTestResult();

private:
    void initializeEmotions();
    void sendEmotionCommand(EmotionType emotion, int intensity);
    void sendSimpleCommand(const QString &command, int deviceIndex = -1); // -1表示发送给所有设备
    
    // 设备连接管理
    void checkDeviceConnections();
    void updateConnectionStatus();

    // 网络控制
    NetworkController *m_networkController;
    
    // 当前状态
    EmotionType m_currentEmotion;
    int m_currentIntensity;
    int m_brightness;
    
    // 表情指令映射
    QMap<EmotionType, EmotionCommand> m_emotionCommands;
    QMap<QString, EmotionType> m_emotionNames;
    
    // 设备连接状态
    bool m_leftEyeConnected;
    bool m_rightEyeConnected;
    QTimer *m_connectionCheckTimer;
    
    // 配置
    static const int LEFT_EYE_INDEX = 0;
    static const int RIGHT_EYE_INDEX = 1;
    static const int CONNECTION_CHECK_INTERVAL = 5000; // 5秒检查一次连接
};

#endif // EMOTIONCONTROLLER_H 