#ifndef SENSORMANAGER_H
#define SENSORMANAGER_H

#include <QObject>
#include <QTimer>
#include <QThread>
#include <QMutex>

class SensorManager : public QObject
{
    Q_OBJECT

public:
    explicit SensorManager(QObject *parent = nullptr);
    ~SensorManager();

    struct SensorData {
        double temperature;
        double humidity;
        int lightLevel;
        // bool motionDetected; // 运动检测已删除
        qint64 timestamp;
    };

    enum SensorType {
        TemperatureSensor,
        HumiditySensor,
        LightSensor,
        // MotionSensor // 运动检测已删除
    };

public slots:
    void startMonitoring();
    void stopMonitoring();
    void calibrateSensors();
    void setSamplingInterval(int milliseconds);

signals:
    void temperatureChanged(double temperature);
    void humidityChanged(double humidity);
    void lightLevelChanged(int level);
    void lightChanged(int level);
    // void motionDetected(); // 运动检测已删除
    void sensorDataUpdated(const SensorData &data);
    void sensorError(const QString &error);

private slots:
    void readSensors();
    // void processMotionData(); // 运动检测已删除

private:
    void initializeSensors();
    double readTemperature();
    double readHumidity();
    int readLightLevel();
    // bool readMotionSensor(); // 运动检测已删除
    bool validateSensorReading(SensorType type, double value);

    // 硬件接口
    bool m_sensorsInitialized;
    QTimer *m_readTimer;
    QMutex m_dataMutex;
    
    // 传感器数据
    SensorData m_currentData;
    SensorData m_previousData;
    
    // 配置
    int m_samplingInterval;
    bool m_isMonitoring;
    
    // 校准数据
    double m_temperatureOffset;
    double m_humidityOffset;
    int m_lightOffset;
    
    // 过滤器
    QList<double> m_temperatureBuffer;
    QList<double> m_humidityBuffer;
    QList<int> m_lightBuffer;
    static const int FILTER_SIZE = 5;
};

#endif // SENSORMANAGER_H 