#ifndef BAIDU_SPEECH_CLIENT_H
#define BAIDU_SPEECH_CLIENT_H

#include <QObject>
#include <QString>
#include <QByteArray>
#include <QTimer>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QJsonDocument>
#include <QJsonObject>
#include <QDebug>

class BaiduSpeechClient : public QObject
{
    Q_OBJECT

public:
    explicit BaiduSpeechClient(QObject *parent = nullptr);
    ~BaiduSpeechClient();

    // 初始化客户端
    bool initialize(const QString &appId, const QString &apiKey, const QString &secretKey);
    
    // 语音识别
    void recognizeAudio(const QByteArray &audioData, const QString &format = "pcm", int rate = 16000);
    
    // 停止识别
    void stopRecognition();
    
    // 检查是否已初始化
    bool isInitialized() const { return m_initialized; }

signals:
    void recognitionResult(const QString &text);
    void recognitionError(const QString &error);

private slots:
    void onTokenReplyFinished();
    void onRecognitionReplyFinished();

private:
    void requestAccessToken();
    QString getAccessToken();
    
    // 配置信息
    QString m_appId;
    QString m_apiKey;
    QString m_secretKey;
    QString m_accessToken;
    qint64 m_tokenExpireTime;
    
    // 网络管理
    QNetworkAccessManager *m_networkManager;
    QNetworkReply *m_currentReply;
    bool m_initialized;
    
    // API URLs
    static const QString TOKEN_URL;
    static const QString RECOGNITION_URL;
};

#endif // BAIDU_SPEECH_CLIENT_H 