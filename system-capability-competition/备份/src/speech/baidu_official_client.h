#ifndef BAIDU_OFFICIAL_CLIENT_H
#define BAIDU_OFFICIAL_CLIENT_H

#include <QObject>
#include <QString>
#include <QByteArray>
#include <QTimer>
#include <QThread>
#include <QDebug>
#include <memory>

// 前向声明，避免在头文件中包含SDK
namespace aip {
    class Speech;
}

class BaiduOfficialClient : public QObject
{
    Q_OBJECT

public:
    explicit BaiduOfficialClient(QObject *parent = nullptr);
    ~BaiduOfficialClient();

    // 初始化客户端
    bool initialize(const QString &appId, const QString &apiKey, const QString &secretKey);
    
    // 语音识别
    void recognizeAudio(const QByteArray &audioData, const QString &format = "pcm", int rate = 16000);
    
    // 停止识别
    void stopRecognition();
    
    // 检查是否已初始化
    bool isInitialized() const { return m_initialized; }

signals:
    void recognitionResult(const QString &text);
    void recognitionError(const QString &error);

private slots:
    void processRecognition();

private:
    // 百度官方SDK客户端
    std::unique_ptr<aip::Speech> m_speechClient;
    
    // 配置信息
    QString m_appId;
    QString m_apiKey;
    QString m_secretKey;
    
    // 状态
    bool m_initialized;
    
    // 待处理的音频数据
    QByteArray m_pendingAudioData;
    QString m_pendingFormat;
    int m_pendingRate;
    
    // 处理线程
    QTimer *m_processTimer;
};

#endif // BAIDU_OFFICIAL_CLIENT_H 