#ifndef ADVANCED_SPEECH_ENGINE_H
#define ADVANCED_SPEECH_ENGINE_H

#include <QObject>
#include <QProcess>
#include <QTimer>
#include <QAudioInput>
#include <QAudioOutput>
#include <QBuffer>

class AdvancedSpeechEngine : public QObject
{
    Q_OBJECT

public:
    explicit AdvancedSpeechEngine(QObject *parent = nullptr);
    ~AdvancedSpeechEngine();

    bool initialize();
    bool isAvailable() const;
    
    bool startListening();
    void stopListening();
    bool synthesizeText(const QString &text);
    
    void setVolume(int volume);
    void setSpeed(int speed);

signals:
    void speechRecognized(const QString &text);
    void speechError(const QString &error);
    void synthesisFinished();

private slots:
    void onRecognitionFinished(int exitCode, QProcess::ExitStatus exitStatus);
    void onSynthesisFinished(int exitCode, QProcess::ExitStatus exitStatus);
    void onRecognitionTimeout();

private:
    void setupAudioDevices();
    void cleanupTemporaryFiles();
    QString generateTempFileName(const QString &suffix);
    void processRecordedAudio();
    void performSpeechRecognition(const QString &audioFile);
    
    // 进程管理
    QProcess *m_recognitionProcess;
    QProcess *m_synthesisProcess;
    
    // 音频设备
    QAudioInput *m_audioInput;
    QAudioOutput *m_audioOutput;
    QBuffer *m_audioBuffer;
    
    // 配置
    QString m_tempDir;
    int m_volume;
    int m_speed;
    int m_sampleRate;
    
    // 状态
    bool m_isInitialized;
    bool m_isListening;
    
    // 超时保护
    QTimer *m_recognitionTimer;
    static const int RECOGNITION_TIMEOUT = 10000; // 10秒
    
    // 临时文件
    QStringList m_tempFiles;
};

#endif // ADVANCED_SPEECH_ENGINE_H 