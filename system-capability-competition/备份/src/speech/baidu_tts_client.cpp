#include "baidu_tts_client.h"
#include <QDebug>
#include <QUrl>
#include <QUrlQuery>
#include <QJsonParseError>
#include <QAudioDeviceInfo>
#include <QDateTime>
#include <QCoreApplication>
#include <QSslConfiguration>
#include <QSslSocket>
#include <QProcess>
#include <QStandardPaths>

const QString BaiduTTSClient::TOKEN_URL = "https://aip.baidubce.com/oauth/2.0/token";
const QString BaiduTTSClient::TTS_URL = "https://tsn.baidu.com/text2audio";

BaiduTTSClient::BaiduTTSClient(QObject *parent)
    : QObject(parent)
    , m_networkManager(nullptr)
    , m_currentReply(nullptr)
    , m_audioOutput(nullptr)
    , m_audioBuffer(nullptr)
    , m_tempAudioFile(nullptr)
    , m_tokenExpireTime(0)
    , m_voice("1")        // 默认普通女声
    , m_speed(5)          // 默认语速
    , m_pitch(5)          // 默认音调
    , m_volume(5)         // 默认音量
    , m_language("zh")    // 默认中文
    , m_initialized(false)
    , m_synthesizing(false)
{
    m_networkManager = new QNetworkAccessManager(this);
    m_audioBuffer = new QBuffer(this);
}

BaiduTTSClient::~BaiduTTSClient()
{
    stopSynthesis();
    if (m_tempAudioFile) {
        delete m_tempAudioFile;
    }
}

bool BaiduTTSClient::initialize(const QString &appId, const QString &apiKey, const QString &secretKey)
{
    if (apiKey.isEmpty() || secretKey.isEmpty()) {
        setLastError("API Key 或 Secret Key 为空");
        return false;
    }

    m_appId = appId;
    m_apiKey = apiKey;
    m_secretKey = secretKey;

    qDebug() << "初始化百度语音合成客户端...";
    qDebug() << "AppID:" << m_appId;
    qDebug() << "API Key:" << (m_apiKey.left(8) + "...");

    // 设置音频输出
    setupAudioOutput();

    // 请求访问令牌
    requestAccessToken();

    return true;
}

void BaiduTTSClient::requestAccessToken()
{
    if (m_currentReply) {
        m_currentReply->abort();
        m_currentReply->deleteLater();
        m_currentReply = nullptr;
    }

    QUrl url(TOKEN_URL);
    QUrlQuery query;
    query.addQueryItem("grant_type", "client_credentials");
    query.addQueryItem("client_id", m_apiKey);
    query.addQueryItem("client_secret", m_secretKey);

    qDebug() << "请求访问令牌:" << url.toString() + "?" + query.toString();

    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/x-www-form-urlencoded");
    request.setHeader(QNetworkRequest::UserAgentHeader, "Qt-BaiduTTS/1.0");
    
    // 禁用SSL证书验证（解决嵌入式系统SSL证书问题）
    QSslConfiguration sslConfig = QSslConfiguration::defaultConfiguration();
    sslConfig.setPeerVerifyMode(QSslSocket::VerifyNone);
    request.setSslConfiguration(sslConfig);

    QString postData = query.toString();
    m_currentReply = m_networkManager->post(request, postData.toUtf8());
    connect(m_currentReply, &QNetworkReply::finished, this, &BaiduTTSClient::onTokenReplyFinished);
}

void BaiduTTSClient::onTokenReplyFinished()
{
    if (!m_currentReply) {
        return;
    }

    QNetworkReply *reply = m_currentReply;
    m_currentReply = nullptr;

    if (reply->error() != QNetworkReply::NoError) {
        setLastError(QString("网络请求失败: %1").arg(reply->errorString()));
        reply->deleteLater();
        return;
    }

    QByteArray responseData = reply->readAll();
    reply->deleteLater();

    QJsonParseError parseError;
    QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData, &parseError);

    if (parseError.error != QJsonParseError::NoError) {
        setLastError(QString("JSON解析失败: %1").arg(parseError.errorString()));
        return;
    }

    QJsonObject jsonObj = jsonDoc.object();

    if (jsonObj.contains("error")) {
        QString error = jsonObj["error"].toString();
        QString errorDesc = jsonObj["error_description"].toString();
        setLastError(QString("获取访问令牌失败: %1 - %2").arg(error, errorDesc));
        return;
    }

    if (jsonObj.contains("access_token")) {
        m_accessToken = jsonObj["access_token"].toString();
        int expiresIn = jsonObj["expires_in"].toInt();
        m_tokenExpireTime = QDateTime::currentSecsSinceEpoch() + expiresIn;

        qDebug() << "成功获取访问令牌，有效期:" << expiresIn << "秒";
        m_initialized = true;
    } else {
        setLastError("响应中没有访问令牌");
    }
}

bool BaiduTTSClient::isTokenValid() const
{
    return !m_accessToken.isEmpty() && 
           QDateTime::currentSecsSinceEpoch() < (m_tokenExpireTime - 60); // 提前60秒刷新
}

bool BaiduTTSClient::synthesizeText(const QString &text)
{
    if (!m_initialized) {
        setLastError("客户端未初始化");
        return false;
    }

    if (text.isEmpty()) {
        setLastError("合成文本为空");
        return false;
    }

    if (m_synthesizing) {
        setLastError("正在进行语音合成");
        return false;
    }

    if (!isTokenValid()) {
        setLastError("访问令牌无效或已过期");
        requestAccessToken();
        return false;
    }

    qDebug() << "开始语音合成:" << text;

    m_synthesizing = true;
    emit synthesisStarted();

    // 构建请求URL和参数
    QUrl url(TTS_URL);
    
    QUrlQuery query;
    query.addQueryItem("tex", text);                    // 要合成的文本
    query.addQueryItem("tok", m_accessToken);           // 访问令牌
    query.addQueryItem("cuid", "qt_baidu_tts_client");  // 用户唯一标识
    query.addQueryItem("ctp", "1");                     // 客户端类型
    query.addQueryItem("lan", m_language);              // 语言
    query.addQueryItem("spd", QString::number(m_speed)); // 语速
    query.addQueryItem("pit", QString::number(m_pitch)); // 音调
    query.addQueryItem("vol", QString::number(m_volume)); // 音量
    query.addQueryItem("per", m_voice);                 // 发音人
    query.addQueryItem("aue", "3");                     // 音频编码(3=mp3)

    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/x-www-form-urlencoded");
    request.setHeader(QNetworkRequest::UserAgentHeader, "Qt-BaiduTTS/1.0");
    
    // 禁用SSL证书验证（解决嵌入式系统SSL证书问题）
    QSslConfiguration sslConfig = QSslConfiguration::defaultConfiguration();
    sslConfig.setPeerVerifyMode(QSslSocket::VerifyNone);
    request.setSslConfiguration(sslConfig);

    QString postData = query.toString();
    
    qDebug() << "发送TTS请求到:" << url.toString();
    qDebug() << "请求参数:" << postData;

    if (m_currentReply) {
        m_currentReply->abort();
        m_currentReply->deleteLater();
    }

    m_currentReply = m_networkManager->post(request, postData.toUtf8());
    connect(m_currentReply, &QNetworkReply::finished, this, &BaiduTTSClient::onTTSReplyFinished);

    return true;
}

void BaiduTTSClient::onTTSReplyFinished()
{
    if (!m_currentReply) {
        return;
    }

    QNetworkReply *reply = m_currentReply;
    m_currentReply = nullptr;

    m_synthesizing = false;

    if (reply->error() != QNetworkReply::NoError) {
        setLastError(QString("网络请求失败: %1").arg(reply->errorString()));
        emit synthesisError(m_lastError);
        reply->deleteLater();
        return;
    }

    QByteArray responseData = reply->readAll();
    QString contentType = reply->header(QNetworkRequest::ContentTypeHeader).toString();
    
    qDebug() << "收到TTS响应，内容类型:" << contentType << "数据大小:" << responseData.size();

    reply->deleteLater();

    // 检查是否是音频数据
    if (contentType.contains("audio") || 
        (responseData.size() > 1000 && !responseData.startsWith('{'))) {
        // 这是音频数据
        qDebug() << "语音合成成功，音频大小:" << responseData.size() << "字节";
        
        emit audioDataReady(responseData);
        playAudioData(responseData);
        emit synthesisFinished();
    } else {
        // 这可能是错误响应
        QString responseText = QString::fromUtf8(responseData);
        qDebug() << "TTS响应内容:" << responseText;

        // 尝试解析JSON错误
        QJsonParseError parseError;
        QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData, &parseError);
        
        if (parseError.error == QJsonParseError::NoError) {
            QJsonObject jsonObj = jsonDoc.object();
            if (jsonObj.contains("err_msg")) {
                QString errorMsg = jsonObj["err_msg"].toString();
                int errorNo = jsonObj["err_no"].toInt();
                setLastError(QString("语音合成失败[%1]: %2").arg(errorNo).arg(errorMsg));
            } else {
                setLastError("语音合成失败: 未知错误");
            }
        } else {
            setLastError("语音合成失败: 响应格式错误");
        }
        
        emit synthesisError(m_lastError);
    }
}

void BaiduTTSClient::setupAudioOutput()
{
    // 设置音频格式
    QAudioFormat format;
    format.setSampleRate(16000);        // 16kHz采样率
    format.setChannelCount(1);          // 单声道
    format.setSampleSize(16);           // 16位
    format.setCodec("audio/pcm");       // PCM编码
    format.setByteOrder(QAudioFormat::LittleEndian);
    format.setSampleType(QAudioFormat::SignedInt);

    // 获取默认音频输出设备
    QAudioDeviceInfo outputDevice = QAudioDeviceInfo::defaultOutputDevice();
    
    if (!outputDevice.isFormatSupported(format)) {
        qWarning() << "音频格式不支持，使用最接近的格式";
        format = outputDevice.nearestFormat(format);
    }

    qDebug() << "TTS音频格式:" << format;

    if (m_audioOutput) {
        delete m_audioOutput;
    }

    m_audioOutput = new QAudioOutput(outputDevice, format, this);
    connect(m_audioOutput, &QAudioOutput::stateChanged, 
            this, &BaiduTTSClient::onAudioOutputStateChanged);
}

void BaiduTTSClient::playAudioData(const QByteArray &audioData)
{
    if (audioData.isEmpty()) {
        qWarning() << "❌ [AUDIO] 音频数据为空，无法播放";
        return;
    }

    // 将音频数据写入临时文件
    if (m_tempAudioFile) {
        delete m_tempAudioFile;
    }

    m_tempAudioFile = new QTemporaryFile(this);
    m_tempAudioFile->setFileTemplate(QCoreApplication::applicationDirPath() + "/tts_XXXXXX.mp3");
    
    if (!m_tempAudioFile->open()) {
        qWarning() << "❌ [AUDIO] 无法创建临时音频文件";
        return;
    }
    
    m_tempAudioFile->write(audioData);
    m_tempAudioFile->close();
    
    QString fileName = m_tempAudioFile->fileName();
    qDebug() << "🎵 [AUDIO] 音频文件保存到:" << fileName;
    qDebug() << "🎵 [AUDIO] 文件大小:" << audioData.size() << "字节";
    
    // 验证音频文件内容
    qDebug() << "🔍 [AUDIO] 验证音频文件内容...";
    QByteArray header = audioData.left(16);
    qDebug() << "🎵 [AUDIO] 文件头部(前16字节):" << header.toHex();
    
    // 检查是否是有效的MP3文件
    bool isValidMP3 = header.startsWith("\xFF\xFB") || 
                     header.startsWith("\xFF\xFA") ||
                     header.startsWith("ID3");
    
    if (!isValidMP3) {
        qWarning() << "⚠️ [AUDIO] 警告: 文件可能不是有效的MP3格式";
        QString textContent = QString::fromUtf8(audioData.left(100));
        if (textContent.contains("error") || textContent.contains("Error")) {
            qWarning() << "❌ [AUDIO] 错误: 音频数据包含错误信息:" << textContent;
            return;
        }
        qDebug() << "🔍 [AUDIO] 数据内容(前100字符):" << textContent;
    } else {
        qDebug() << "✅ [AUDIO] MP3文件格式验证通过";
    }
    
    // 🎵 估算音频播放时长
    int estimatedDurationMs = estimateAudioDuration(audioData);
    qDebug() << "⏱️ [AUDIO] 估算音频时长:" << estimatedDurationMs << "毫秒";
    
    // 使用系统播放器播放MP3文件
    QProcess *playProcess = new QProcess(this);
    
    // 设置播放完成的回调
    connect(playProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            [this, playProcess, fileName, estimatedDurationMs](int exitCode, QProcess::ExitStatus exitStatus) {
                qDebug() << "🎵 [AUDIO] 播放进程结束，退出码:" << exitCode << "状态:" << exitStatus;
                if (exitCode == 0) {
                    qDebug() << "✅ [AUDIO] 音频播放成功完成，实际耗时:" << estimatedDurationMs << "ms";
                } else {
                    qWarning() << "❌ [AUDIO] 音频播放失败，退出码:" << exitCode;
                }
                playProcess->deleteLater();
                
                // 🎯 关键修复：播放完成后再发送信号
                qDebug() << "🎵 [TTS] TTS音频播放完毕，可以开始下一步操作";
                emit synthesisFinished();
                emit ttsPlaybackFinished();  // 新增专用信号，表示TTS播放完成
            });
            
    connect(playProcess, &QProcess::errorOccurred,
            [this, playProcess](QProcess::ProcessError error) {
                qWarning() << "❌ [AUDIO] 播放进程错误:" << error;
                playProcess->deleteLater();
                // 即使出错也要发送完成信号，避免阻塞后续操作
                emit synthesisFinished();
                emit ttsPlaybackFinished();
            });

    qDebug() << "🔍 [AUDIO] 检查可用的音频播放器...";
    
    // 🎵 支持MP3的播放器列表（按优先级排序）
    QStringList mp3Players = {
        "mpg123",       // 专门的MP3播放器
        "mplayer",      // 多媒体播放器
        "mpv",          // 现代多媒体播放器
        "ffplay",       // FFmpeg播放器
        "cvlc",         // VLC命令行版本
        "vlc",          // VLC播放器
        "paplay"        // PulseAudio播放器（支持MP3）
    };
    
    // 🎵 不支持MP3的播放器（会产生噪音）
    QStringList wavOnlyPlayers = {
        "aplay",        // ALSA播放器（只支持WAV/PCM）
        "speaker-test"  // 音频测试工具
    };
    
    QString selectedPlayer;
    QString playerPath;
    
    // 优先查找支持MP3的播放器
    for (const QString &player : mp3Players) {
        QString path = QStandardPaths::findExecutable(player);
        if (!path.isEmpty()) {
            selectedPlayer = player;
            playerPath = path;
            qDebug() << "✅ [AUDIO] 找到MP3播放器:" << player << "路径:" << path;
            break;
        } else {
            qDebug() << "❌ [AUDIO] 未找到播放器:" << player;
        }
    }
    
    // 如果没找到MP3播放器，再查找其他播放器，但给出警告
    if (selectedPlayer.isEmpty()) {
        qWarning() << "⚠️ [AUDIO] 未找到专用MP3播放器，尝试备用播放器（可能产生噪音）";
        for (const QString &player : wavOnlyPlayers) {
            QString path = QStandardPaths::findExecutable(player);
            if (!path.isEmpty()) {
                selectedPlayer = player;
                playerPath = path;
                qDebug() << "⚠️ [AUDIO] 找到备用播放器:" << player << "路径:" << path;
                qWarning() << "⚠️ [AUDIO]" << player << "通常只支持WAV格式，MP3可能产生噪音";
                break;
            }
        }
    }
    
    if (selectedPlayer.isEmpty()) {
        qWarning() << "❌ [AUDIO] 未找到任何可用的音频播放器";
        emit synthesisFinished();
        return;
    }
    
    qDebug() << "🎵 [AUDIO] 使用播放器:" << selectedPlayer;
    
    // 根据不同播放器设置合适的参数
    QStringList playArgs;
    if (selectedPlayer == "mpg123") {
        playArgs << "-q" << fileName;  // 安静模式
    } else if (selectedPlayer == "mplayer") {
        playArgs << "-really-quiet" << "-noconfig" << "all" << fileName;
    } else if (selectedPlayer == "mpv") {
        playArgs << "--no-terminal" << "--no-video" << fileName;
    } else if (selectedPlayer == "ffplay") {
        playArgs << "-nodisp" << "-autoexit" << "-loglevel" << "quiet" << fileName;
    } else if (selectedPlayer == "cvlc" || selectedPlayer == "vlc") {
        playArgs << "--intf" << "dummy" << "--play-and-exit" << fileName;
    } else if (selectedPlayer == "paplay") {
        playArgs << fileName;
    } else if (selectedPlayer == "aplay") {
        // aplay不支持MP3，但保留兼容性
        playArgs << "-q" << fileName;
        qWarning() << "⚠️ [AUDIO] aplay通常只支持WAV格式，MP3可能无法正常播放";
    } else {
        // 默认参数
        playArgs << fileName;
    }
    
    qDebug() << "🎵 [AUDIO] 播放参数:" << playArgs.join(" ");
    qDebug() << "🚀 [AUDIO] 启动音频播放进程...";
    
    playProcess->start(selectedPlayer, playArgs);
    
    if (!playProcess->waitForStarted(3000)) {
        qWarning() << "❌ [AUDIO] 播放器启动失败:" << selectedPlayer;
        playProcess->deleteLater();
        emit synthesisFinished();
        return;
    }
    
    qDebug() << "✅ [AUDIO] 播放进程已启动，等待播放完成...";
}

void BaiduTTSClient::onAudioOutputStateChanged(QAudio::State state)
{
    qDebug() << "音频输出状态变化:" << state;
    
    if (state == QAudio::IdleState) {
        m_audioOutput->stop();
        emit synthesisFinished();
    }
}

void BaiduTTSClient::stopSynthesis()
{
    if (m_currentReply) {
        m_currentReply->abort();
        m_currentReply->deleteLater();
        m_currentReply = nullptr;
    }

    if (m_audioOutput && m_audioOutput->state() != QAudio::StoppedState) {
        m_audioOutput->stop();
    }

    m_synthesizing = false;
}

QString BaiduTTSClient::urlEncode(const QString &text)
{
    return QUrl::toPercentEncoding(text);
}

int BaiduTTSClient::estimateAudioDuration(const QByteArray &audioData)
{
    // 🎵 估算MP3音频播放时长
    if (audioData.isEmpty()) {
        return 0;
    }
    
    // 基于文件大小的粗略估算
    // 百度TTS通常生成的是128kbps的MP3文件
    // 计算公式: 时长(秒) = 文件大小(字节) * 8 / 比特率(bps)
    int fileSizeBytes = audioData.size();
    int bitRate = 128000; // 128 kbps，百度TTS的默认比特率
    
    // 计算时长（毫秒）
    int durationMs = (fileSizeBytes * 8 * 1000) / bitRate;
    
    // 根据语速调整时长估算
    double speedFactor = 1.0;
    if (m_speed <= 5) {
        speedFactor = 1.3; // 慢速
    } else if (m_speed >= 10) {
        speedFactor = 0.8; // 快速
    }
    
    durationMs = static_cast<int>(durationMs * speedFactor);
    
    // 添加一些缓冲时间（播放器启动时间等）
    durationMs += 500; // 500ms缓冲
    
    qDebug() << "🎵 [AUDIO] 音频时长估算详情:";
    qDebug() << "   文件大小:" << fileSizeBytes << "字节";
    qDebug() << "   比特率:" << bitRate << "bps";
    qDebug() << "   语速因子:" << speedFactor;
    qDebug() << "   估算时长:" << durationMs << "ms (" << (durationMs/1000.0) << "秒)";
    
    return durationMs;
}

void BaiduTTSClient::setLastError(const QString &error)
{
    m_lastError = error;
    qWarning() << "百度TTS错误:" << error;
} 