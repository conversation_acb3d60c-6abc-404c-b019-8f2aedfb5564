#include "speechmanager.h"
#include "advanced_speech_engine.h"
#include "baidu_speech_client.h"
#include "baidu_official_client.h"
#include "baidu_tts_client.h"
#include <QDebug>
#include <QRandomGenerator>
#include <QAudioInput>
#include <QAudioOutput>
#include <QAudioFormat>
#include <QAudioDeviceInfo>
#include <QApplication>
#include <QMessageBox>
#include <QProcess>
#include <QDateTime>
#include <QVector>
#include <QMap>
#include <cmath>

SpeechManager::SpeechManager(QObject *parent)
    : QObject(parent)
    , m_advancedEngine(nullptr)
    , m_currentEngine(EngineBaidu)
    , m_preferredEngine(EngineBaidu)
    , m_currentState(StateIdle)
    , m_volume(70)
    , m_speed(50)
    , m_isInitialized(false)
    , m_retryTimer(new QTimer(this))
    , m_retryCount(0)
    , m_audioLevelTimer(new QTimer(this))
    , m_audioInput(nullptr)
    , m_audioOutput(nullptr)
    , m_inputDevice(nullptr)
    , m_outputDevice(nullptr)
    , m_audioBuffer(new QBuffer(this))
    , m_voiceRecognitionTimer(nullptr)
    , m_recognitionStartTime(0)
    , m_networkManager(new QNetworkAccessManager(this))
    , m_isTTSPlaying(false)
{
    initializeAudioDevices();
    initializeEngines();
    
    m_retryTimer->setSingleShot(true);
    m_retryTimer->setInterval(3000); // 3秒后重试
    connect(m_retryTimer, &QTimer::timeout, this, [this]() {
        if (m_retryCount < MAX_RETRY_COUNT) {
            m_retryCount++;
            qDebug() << "语音引擎重试，第" << m_retryCount << "次";
            switchToFallbackEngine();
        } else {
            qDebug() << "语音引擎重试次数已达上限";
            emit speechError("所有语音引擎都不可用");
        }
    });
    
    // 设置音频级别更新定时器
    m_audioLevelTimer->setInterval(50); // 20fps更新音频级别
    connect(m_audioLevelTimer, &QTimer::timeout, this, &SpeechManager::updateAudioLevel);
}

SpeechManager::~SpeechManager()
{
    stopListening();
    

    if (m_advancedEngine) {
        delete m_advancedEngine;
    }
    if (m_baiduClient) {
        delete m_baiduClient;
    }
    if (m_baiduOfficialClient) {
        delete m_baiduOfficialClient;
    }
    if (m_baiduTTSClient) {
        delete m_baiduTTSClient;
    }
}

void SpeechManager::initializeEngines()
{
    qDebug() << "初始化语音引擎...";
    
    // 不使用科大讯飞引擎，只使用百度API
    
    // 初始化百度官方SDK客户端
    m_baiduOfficialClient = new BaiduOfficialClient(this);
    if (m_baiduOfficialClient->initialize("6931178", "uWNmAojNSQce0GL1n73X76Vl", "XkU27gxIRB8tUqhqmVtFux2F6Hj1tznH")) {
        connect(m_baiduOfficialClient, &BaiduOfficialClient::recognitionResult, this, &SpeechManager::onBaiduOfficialRecognitionResult);
        connect(m_baiduOfficialClient, &BaiduOfficialClient::recognitionError, this, &SpeechManager::onBaiduOfficialRecognitionError);
        qDebug() << "✅ 百度官方SDK客户端初始化成功";
    } else {
        qWarning() << "❌ 百度官方SDK客户端初始化失败";
    }
    
    // 保留原有的百度客户端作为备用
    m_baiduClient = new BaiduSpeechClient(this);
    if (m_baiduClient->initialize("6931178", "uWNmAojNSQce0GL1n73X76Vl", "XkU27gxIRB8tUqhqmVtFux2F6Hj1tznH")) {
        connect(m_baiduClient, &BaiduSpeechClient::recognitionResult, this, &SpeechManager::onBaiduRecognitionResult);
        connect(m_baiduClient, &BaiduSpeechClient::recognitionError, this, &SpeechManager::onBaiduRecognitionError);
        qDebug() << "✅ 百度语音识别客户端初始化成功";
    } else {
        qWarning() << "❌ 百度语音识别客户端初始化失败";
    }
    
    // 初始化高级语音引擎
    m_advancedEngine = new AdvancedSpeechEngine(this);
    if (m_advancedEngine && m_advancedEngine->initialize()) {
        connect(m_advancedEngine, &AdvancedSpeechEngine::speechRecognized, this, &SpeechManager::onAdvancedSpeechResult);
        connect(m_advancedEngine, &AdvancedSpeechEngine::speechError, this, &SpeechManager::onAdvancedSpeechError);
        connect(m_advancedEngine, &AdvancedSpeechEngine::synthesisFinished, this, &SpeechManager::onAdvancedTTSFinished);
        qDebug() << "✅ 高级语音引擎初始化成功";
    } else {
        qWarning() << "❌ 高级语音引擎初始化失败";
    }
    
    // 初始化百度TTS客户端
    m_baiduTTSClient = new BaiduTTSClient(this);
    if (m_baiduTTSClient->initialize("6931178", "uWNmAojNSQce0GL1n73X76Vl", "XkU27gxIRB8tUqhqmVtFux2F6Hj1tznH")) {
        connect(m_baiduTTSClient, &BaiduTTSClient::synthesisStarted, this, &SpeechManager::onBaiduTTSStarted);
        connect(m_baiduTTSClient, &BaiduTTSClient::synthesisFinished, this, &SpeechManager::onBaiduTTSFinished);
        connect(m_baiduTTSClient, &BaiduTTSClient::synthesisError, this, &SpeechManager::onBaiduTTSError);
        
        // 🎵 连接TTS播放完成信号，用于音乐播放控制
        connect(m_baiduTTSClient, &BaiduTTSClient::ttsPlaybackFinished, this, &SpeechManager::ttsPlaybackFinished);
        
        qDebug() << "✅ 百度TTS客户端初始化成功";
    } else {
        qWarning() << "❌ 百度TTS客户端初始化失败";
    }
    
    // 检查引擎可用性
    bool hasValidEngine = false;
    if (m_baiduOfficialClient && m_baiduOfficialClient->isInitialized()) {
        qDebug() << "🔧 百度官方SDK可用";
        hasValidEngine = true;
    }
    if (m_baiduClient && m_baiduClient->isInitialized()) {
        qDebug() << "🔧 百度语音客户端可用";
        hasValidEngine = true;
    }
    if (m_advancedEngine) {
        qDebug() << "🔧 高级语音引擎可用";
        hasValidEngine = true;
    }
    
    if (!hasValidEngine) {
        qWarning() << "⚠️ 警告：没有可用的语音识别引擎！";
    }
    
    m_isInitialized = true;
    setState(StateIdle);
}

void SpeechManager::initializeAudioDevices()
{
    qDebug() << "初始化音频设备...";
    
    // 设置音频格式
    QAudioFormat format;
    format.setSampleRate(16000);      // 16kHz采样率
    format.setChannelCount(1);        // 单声道
    format.setSampleSize(16);         // 16位采样
    format.setCodec("audio/pcm");
    format.setByteOrder(QAudioFormat::LittleEndian);
    format.setSampleType(QAudioFormat::SignedInt);
    
    // 检查默认音频输入设备
    QAudioDeviceInfo inputInfo = QAudioDeviceInfo::defaultInputDevice();
    if (!inputInfo.isFormatSupported(format)) {
        qWarning() << "默认音频输入格式不支持，尝试最近似格式";
        format = inputInfo.nearestFormat(format);
    }
    
    // 检查默认音频输出设备  
    QAudioDeviceInfo outputInfo = QAudioDeviceInfo::defaultOutputDevice();
    if (!outputInfo.isFormatSupported(format)) {
        qWarning() << "默认音频输出格式不支持，尝试最近似格式";
        format = outputInfo.nearestFormat(format);
    }
    
    qDebug() << "音频格式:" << format;
    qDebug() << "输入设备:" << inputInfo.deviceName();
    qDebug() << "输出设备:" << outputInfo.deviceName();
    
    // 创建音频输入
    m_audioInput = new QAudioInput(inputInfo, format, this);
    m_audioInput->setVolume(0.8);
    
    // 创建音频输出
    m_audioOutput = new QAudioOutput(outputInfo, format, this);
    m_audioOutput->setVolume(m_volume / 100.0);
    
    // 准备音频缓冲区
    m_audioBuffer->open(QIODevice::ReadWrite);
    
    qDebug() << "音频设备初始化完成";
}

void SpeechManager::startListening()
{
    QMutexLocker locker(&m_mutex);
    
    qDebug() << "🎤 [DEBUG] 开始语音监听请求...";
    
    if (m_currentState == StateListening) {
        qDebug() << "⚠️ [DEBUG] 已经在监听状态，忽略请求";
        return;
    }
    
    // 🔧 防止语音反馈：TTS播放期间不启动监听
    if (m_isTTSPlaying) {
        qDebug() << "🔊 [DEBUG] TTS正在播放，延迟启动监听";
        QTimer::singleShot(3000, this, [this]() { // 延迟3秒再启动
            if (!m_isTTSPlaying) { // 再次检查TTS状态
                startListening();
            }
        });
        return;
    }
    
    qDebug() << "🔍 [DEBUG] 检查语音引擎可用性...";
    
    // 检查是否有可用的语音识别引擎
    bool hasEngine = false;
    if (m_baiduOfficialClient && m_baiduOfficialClient->isInitialized()) {
        qDebug() << "✅ [DEBUG] 百度官方SDK可用";
        hasEngine = true;
    }
    if (m_baiduClient && m_baiduClient->isInitialized()) {
        qDebug() << "✅ [DEBUG] 百度语音客户端可用";
        hasEngine = true;
    }
    if (m_advancedEngine) {
        qDebug() << "✅ [DEBUG] 高级语音引擎可用";
        hasEngine = true;
    }
    
    if (!hasEngine) {
        qWarning() << "❌ [ERROR] 没有可用的语音识别引擎！";
        emit speechError("语音识别引擎不可用");
        return;
    }
    
    // 申请麦克风权限
    qDebug() << "🎙️ [DEBUG] 检查麦克风权限...";
    if (!checkMicrophonePermission()) {
        qWarning() << "❌ [ERROR] 麦克风权限检查失败";
        emit speechError("无法访问麦克风，请检查设备或权限");
        return;
    }
    qDebug() << "✅ [DEBUG] 麦克风权限检查通过";
    
    qDebug() << "🎯 [DEBUG] 开始语音识别（简单模式）";
    setState(StateListening);
    
    // 开始音频录制
    qDebug() << "🎵 [DEBUG] 启动音频录制...";
    startAudioRecording();
    
    // 启动音频级别更新
    qDebug() << "📊 [DEBUG] 启动音频级别监控...";
    m_audioLevelTimer->start();
    
    // 清空缓冲区（push-to-talk 录音模式）
    m_audioBuffer->buffer().clear();
    m_audioBuffer->seek(0);
    qDebug() << "🗂️ [DEBUG] 音频缓冲区已清空";
    
    // 启动语音识别处理
    qDebug() << "🚀 [DEBUG] 启动语音识别处理...";
    startSimpleVoiceRecognition();
    
    qDebug() << "✅ [SUCCESS] 语音识别监听已成功启动！";
    // 不立即启动识别定时器，等待用户点击"结束说话"
}

void SpeechManager::stopListening()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_currentState != StateListening && m_currentState != StateRecognizing) {
        return;
    }
    
    qDebug() << "停止语音识别";
    
    // 停止音频录制
    stopAudioRecording();
    
    // 停止音频级别更新
    m_audioLevelTimer->stop();
    
    // push-to-talk：执行一次语音识别（stopSimpleVoiceRecognition内部已处理）
    stopSimpleVoiceRecognition(); // 确保定时器停止并已调用processVoiceRecognition()
    
    // 录音结束后已进入识别阶段，此时不应立即停止正在进行的百度识别请求，
    // 也不应将状态切换为 Idle，避免回调结果被忽略。
    // 状态将在识别回调（onBaiduOfficialRecognitionResult / onBaiduRecognitionResult 等）中恢复。
}

void SpeechManager::synthesizeText(const QString &text)
{
    QMutexLocker locker(&m_mutex);
    
    if (text.isEmpty()) {
        emit speechError("合成文本为空");
        return;
    }
    
    qDebug() << "开始语音合成:" << text;
    setState(StateSynthesizing);
    
    // 🔧 防止语音反馈：设置TTS播放状态，停止语音识别
    m_isTTSPlaying = true;
    if (m_currentState == StateListening) {
        stopListening();
    }
    
    // 优先使用百度TTS
    if (m_baiduTTSClient && m_baiduTTSClient->isInitialized()) {
        qDebug() << "使用百度TTS进行语音合成";
        if (m_baiduTTSClient->synthesizeText(text)) {
            return; // 成功启动百度TTS
        } else {
            qWarning() << "百度TTS启动失败，切换到系统TTS";
        }
    }
    
    // 备用方案：使用系统TTS
    qDebug() << "使用系统TTS进行语音合成";
    useSystemTTS(text);
}

void SpeechManager::setEngine(SpeechEngine engine)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_currentState == StateListening || m_currentState == StateSynthesizing) {
        qWarning() << "无法在工作状态下切换引擎";
        return;
    }
    
    if (isEngineAvailable(engine)) {
        m_preferredEngine = engine;
        m_currentEngine = engine;
        qDebug() << "切换到引擎:" << engine;
    } else {
        qWarning() << "指定的引擎不可用:" << engine;
    }
}

void SpeechManager::setVolume(int volume)
{
    m_volume = qBound(0, volume, 100);
    
    if (m_advancedEngine) {
        m_advancedEngine->setVolume(m_volume);
    }
    // 百度API音量控制通过系统TTS实现
}

void SpeechManager::setSpeed(int speed)
{
    m_speed = qBound(0, speed, 100);
    
    if (m_advancedEngine) {
        m_advancedEngine->setSpeed(m_speed);
    }
    // 百度API语速控制通过系统TTS实现
}

bool SpeechManager::isEngineAvailable(SpeechEngine engine)
{
    switch (engine) {
    case EngineBaidu:
        return (m_baiduOfficialClient && m_baiduOfficialClient->isInitialized()) || 
               (m_baiduClient && m_baiduClient->isInitialized());
    case EngineAdvanced:
        return m_advancedEngine && m_advancedEngine->isAvailable();
    case EngineOffline:
        return false; // 暂未实现
    }
    return false;
}

void SpeechManager::switchToFallbackEngine()
{
    if (m_currentEngine == EngineBaidu && isEngineAvailable(EngineAdvanced)) {
        m_currentEngine = EngineAdvanced;
        qDebug() << "切换到高级语音引擎";
    } else if (m_currentEngine == EngineAdvanced && isEngineAvailable(EngineBaidu)) {
        m_currentEngine = EngineBaidu;
        qDebug() << "切换到百度语音引擎";
    } else {
        qWarning() << "没有可用的备用引擎";
        setState(StateError);
        m_retryTimer->start();
        return;
    }
    
    // 重新尝试当前操作
    if (m_currentState == StateListening) {
        setState(StateIdle);
        startListening();
    }
}

void SpeechManager::setState(SpeechState state)
{
    if (m_currentState != state) {
        m_currentState = state;
        emit stateChanged(state);
    }
}

// 删除科大讯飞引擎回调 - 使用系统TTS替代

// 百度官方SDK回调
void SpeechManager::onBaiduOfficialRecognitionResult(const QString &text)
{
    qDebug() << "百度官方SDK识别成功:" << text;
    
    // 无论当前状态如何，都处理结果以保证用户可以得到回复
    setState(StateIdle);
    emit speechRecognized(text);
}

void SpeechManager::onBaiduOfficialRecognitionError(const QString &error)
{
    qWarning() << "百度官方SDK识别错误:" << error;
    
    // 只有在识别状态时才处理错误，防止在停止监听后仍然接收到错误
    if (m_currentState == StateRecognizing) {
    // 如果是token错误，尝试使用自定义百度客户端作为备用
    if (error.contains("token error") && m_baiduClient && m_baiduClient->isInitialized()) {
        qDebug() << "百度官方SDK token错误，切换到自定义百度客户端";
        // 使用最后一次的音频数据重新识别
        if (!m_lastAudioData.isEmpty()) {
            m_baiduClient->recognizeAudio(m_lastAudioData, "pcm", 16000);
            return; // 不重置状态，等待自定义客户端的回调
        }
    }
    
    setState(StateListening); // 重置为监听状态，允许下次识别
        emit speechError("百度官方SDK错误: " + error);
    } else {
        qDebug() << "忽略识别错误，当前状态:" << m_currentState;
    }
}

// 百度语音识别回调（备用）
void SpeechManager::onBaiduRecognitionResult(const QString &text)
{
    qDebug() << "百度语音识别成功:" << text;
    
    // 无论当前状态如何，都处理结果以保证用户可以得到回复
    setState(StateIdle);
    emit speechRecognized(text);
}

void SpeechManager::onBaiduRecognitionError(const QString &error)
{
    qWarning() << "百度语音识别错误:" << error;
    
    // 只有在识别状态时才处理错误，防止在停止监听后仍然接收到错误
    if (m_currentState == StateRecognizing) {
        setState(StateListening); // 重置为监听状态，允许下次识别
        emit speechError("百度识别错误: " + error);
    } else {
        qDebug() << "忽略识别错误，当前状态:" << m_currentState;
    }
}

// 高级语音引擎回调
void SpeechManager::onAdvancedSpeechResult(const QString &text)
{
    setState(StateIdle);
    emit speechRecognized(text);
}

void SpeechManager::onAdvancedSpeechError(const QString &error)
{
    qWarning() << "高级语音引擎错误:" << error;
    setState(StateError);
    
    // 尝试切换到百度引擎
    if (isEngineAvailable(EngineBaidu)) {
        switchToFallbackEngine();
    } else {
        emit speechError("高级语音引擎错误: " + error);
    }
}

void SpeechManager::onAdvancedTTSFinished()
{
    setState(StateIdle);
    // 🔧 防止语音反馈：重置TTS播放状态
    m_isTTSPlaying = false;
    emit synthesisFinished();
    emit ttsFinished();
}

bool SpeechManager::checkMicrophonePermission()
{
    qDebug() << "🔍 [MIC] 开始麦克风设备检查...";
    
    // 检查音频输入设备是否可用
    QAudioDeviceInfo inputInfo = QAudioDeviceInfo::defaultInputDevice();
    if (inputInfo.isNull()) {
        qWarning() << "❌ [MIC] 没有找到音频输入设备";
        return false;
    }
    
    qDebug() << "📱 [MIC] 找到音频输入设备:" << inputInfo.deviceName();
    
    // 列出所有可用的音频输入设备
    QList<QAudioDeviceInfo> inputDevices = QAudioDeviceInfo::availableDevices(QAudio::AudioInput);
    qDebug() << "📋 [MIC] 可用的音频输入设备数量:" << inputDevices.size();
    for (int i = 0; i < inputDevices.size(); ++i) {
        qDebug() << "  " << (i+1) << "." << inputDevices[i].deviceName();
    }
    
    // 尝试创建音频输入来测试权限
    QAudioFormat format;
    format.setSampleRate(16000);
    format.setChannelCount(1);
    format.setSampleSize(16);
    format.setCodec("audio/pcm");
    format.setByteOrder(QAudioFormat::LittleEndian);
    format.setSampleType(QAudioFormat::SignedInt);
    
    qDebug() << "🎛️ [MIC] 音频格式配置:";
    qDebug() << "  采样率:" << format.sampleRate() << "Hz";
    qDebug() << "  声道数:" << format.channelCount();
    qDebug() << "  采样位数:" << format.sampleSize();
    qDebug() << "  编码:" << format.codec();
    
    if (!inputInfo.isFormatSupported(format)) {
        qDebug() << "⚠️ [MIC] 默认格式不支持，尝试最近似格式...";
        format = inputInfo.nearestFormat(format);
        qDebug() << "🔧 [MIC] 调整后的音频格式:";
        qDebug() << "  采样率:" << format.sampleRate() << "Hz";
        qDebug() << "  声道数:" << format.channelCount();
        qDebug() << "  采样位数:" << format.sampleSize();
        qDebug() << "  编码:" << format.codec();
    } else {
        qDebug() << "✅ [MIC] 音频格式支持检查通过";
    }
    
    // 简单测试是否能创建音频输入
    qDebug() << "🧪 [MIC] 测试音频输入设备创建...";
    QAudioInput testInput(inputInfo, format);
    if (testInput.error() != QAudio::NoError) {
        qWarning() << "❌ [MIC] 音频输入设备错误:" << testInput.error();
        qWarning() << "❌ [MIC] 错误详情:";
        switch (testInput.error()) {
            case QAudio::NoError:
                qDebug() << "  无错误";
                break;
            case QAudio::OpenError:
                qDebug() << "  设备打开错误";
                break;
            case QAudio::IOError:
                qDebug() << "  IO错误";
                break;
            case QAudio::UnderrunError:
                qDebug() << "  缓冲区下溢错误";
                break;
            case QAudio::FatalError:
                qDebug() << "  致命错误";
                break;
            default:
                qDebug() << "  未知错误";
                break;
        }
        return false;
    }
    
    qDebug() << "✅ [MIC] 麦克风设备检查完成，一切正常！";
    return true;
}

void SpeechManager::startAudioRecording()
{
    if (!m_audioInput) {
        qWarning() << "音频输入设备未初始化";
        return;
    }
    
    m_audioBuffer->reset();
    m_inputDevice = m_audioInput->start();
    
    if (!m_inputDevice) {
        qWarning() << "无法启动音频录制";
        emit speechError("麦克风启动失败");
        return;
    }
    
    connect(m_inputDevice, &QIODevice::readyRead, this, &SpeechManager::onAudioDataReady);
    qDebug() << "音频录制已启动";
}

void SpeechManager::stopAudioRecording()
{
    if (m_audioInput) {
        m_audioInput->stop();
        m_inputDevice = nullptr;
    }
    qDebug() << "音频录制已停止";
}

void SpeechManager::onAudioDataReady()
{
    if (!m_inputDevice) return;
    
    QByteArray audioData = m_inputDevice->readAll();
    if (audioData.isEmpty()) return;
    
    // 写入缓冲区用于处理
    m_audioBuffer->write(audioData);
    
    // 计算音频级别
    calculateAudioLevel(audioData);
}

void SpeechManager::calculateAudioLevel(const QByteArray &audioData)
{
    if (audioData.isEmpty() || audioData.size() < 2) return;
    
    // 计算16位PCM数据的RMS值
    const qint16 *samples = reinterpret_cast<const qint16*>(audioData.constData());
    int sampleCount = audioData.size() / sizeof(qint16);
    
    if (sampleCount <= 0) return;
    
    double sum = 0.0;
    for (int i = 0; i < sampleCount; ++i) {
        sum += samples[i] * samples[i];
    }
    
    double rms = sqrt(sum / sampleCount);
    double level = rms / 32768.0; // 归一化到0-1范围
    
    m_currentAudioLevel = qBound(0.0, level, 1.0);
}

void SpeechManager::updateAudioLevel()
{
    if (m_currentState == StateListening && m_currentAudioLevel >= 0) {
        emit audioLevelChanged(m_currentAudioLevel);
        // 添加一些衰减效果
        m_currentAudioLevel *= 0.9;
    }
}

void SpeechManager::startSimpleVoiceRecognition()
{
    // 清空音频缓冲区
    m_audioBuffer->buffer().clear();
    m_audioBuffer->close();
    m_audioBuffer->open(QIODevice::ReadWrite);
    
    // 🔧 禁用自动识别定时器，改为纯Push-to-Talk模式
    // 只有用户点击"结束说话"按钮时才进行语音识别
    qDebug() << "🎤 [PTT] 启用纯Push-to-Talk模式，等待用户手动结束录音";
    
    // 不启动自动识别定时器
    // if (!m_voiceRecognitionTimer) {
    //     m_voiceRecognitionTimer = new QTimer(this);
    //     connect(m_voiceRecognitionTimer, &QTimer::timeout, this, &SpeechManager::processVoiceRecognition);
    // }
    // m_voiceRecognitionTimer->start(500); // 禁用自动检查
    
    m_recognitionStartTime = QDateTime::currentMSecsSinceEpoch();
    
    qDebug() << "🎙️ Push-to-Talk模式已启动，请说话完毕后点击'结束说话'按钮...";
}

void SpeechManager::stopSimpleVoiceRecognition()
{
    if (m_voiceRecognitionTimer) {
        m_voiceRecognitionTimer->stop();
    }
    
    // 🎤 用户手动结束录音，现在开始处理音频数据
    qDebug() << "🎤 [PTT] 用户手动结束录音，开始处理语音识别";
    
    // 检查音频缓冲区大小
    int bufferSize = m_audioBuffer->size();
    qDebug() << "📊 [PTT] 最终音频缓冲区大小:" << bufferSize << "字节";
    
    if (bufferSize < 1000) {
        qDebug() << "⏳ [PTT] 音频数据不足，需要至少1000字节，当前:" << bufferSize;
        emit speechError("录音时间太短，请重新录音");
        setState(StateIdle);
        return;
    }
    
    // 处理最后的音频数据
    if (m_audioBuffer->size() > 0) {
        QByteArray audioData = m_audioBuffer->data();
        qDebug() << "🎵 [PTT] 用户结束录音，处理音频数据:" << audioData.size() << "字节";
        forcePushToTalkRecognition(audioData);
    }
}

void SpeechManager::processVoiceRecognition()
{
    // 🔧 在纯Push-to-Talk模式下，此函数不应被自动调用
    // 只有在用户手动结束录音时才调用语音识别
    
    // 安全检查 - 必须在监听状态
    if (!m_audioBuffer || (m_currentState != StateListening && m_currentState != StateRecognizing)) {
        qDebug() << "⚠️ [VOICE] 状态检查失败，当前状态:" << m_currentState;
        return;
    }
    
    // 在纯Push-to-Talk模式下，不进行自动识别
    qDebug() << "⏸️ [PTT] 纯Push-to-Talk模式：跳过自动语音识别，等待用户手动结束";
    return;
    
    // 以下代码被禁用，只保留用于兼容性
    /*
    // 检查是否有足够的音频数据
    int bufferSize = m_audioBuffer->size();
    qDebug() << "📊 [VOICE] 音频缓冲区大小:" << bufferSize << "字节";
    
    if (bufferSize < 1000) { // 进一步降低阈值：至少需要0.06秒的音频数据 (16000Hz * 0.06s * 2bytes)
        qDebug() << "⏳ [VOICE] 音频数据不足，需要至少1000字节，当前:" << bufferSize;
        emit speechError("录音时间太短，请重新录音");
        return;
    }
    
    qDebug() << "✅ [VOICE] Push-to-Talk模式：跳过音频级别检查，直接处理用户录音";
    
    // 获取音频数据
    QByteArray audioData = m_audioBuffer->data();
    if (audioData.isEmpty()) {
        qDebug() << "❌ [VOICE] 音频数据为空";
        emit speechError("录音数据为空");
        return;
    }
    
    qDebug() << "🎵 [VOICE] 获取到音频数据:" << audioData.size() << "字节";
    
    // 直接进行语音识别（Push-to-Talk模式不检查音频级别）
    qDebug() << "🚀 [VOICE] Push-to-Talk模式：强制进行语音识别";
    forcePushToTalkRecognition(audioData);
    */
}

void SpeechManager::forcePushToTalkRecognition(const QByteArray &audioData)
{
    qDebug() << "🎤 [PTT] 强制语音识别模式，音频大小:" << audioData.size() << "字节";
    
    // 检查音频数据是否有效
    if (audioData.isEmpty()) {
        qDebug() << "❌ [PTT] 音频数据为空";
        emit speechError("录音数据为空");
        return;
    }
    
    // 保存音频数据用于备用识别
    m_lastAudioData = audioData;
    
    // 设置为识别状态
    setState(StateRecognizing);
    
    // 优先使用百度官方SDK
    if (m_baiduOfficialClient && m_baiduOfficialClient->isInitialized()) {
        qDebug() << "🔧 [PTT] 使用百度官方SDK进行Push-to-Talk识别";
        m_baiduOfficialClient->recognizeAudio(audioData, "pcm", 16000);
        return; // 异步识别，等待回调
    } 
    // 备用：使用自定义百度客户端
    else if (m_baiduClient && m_baiduClient->isInitialized()) {
        qDebug() << "🔧 [PTT] 使用百度语音客户端进行Push-to-Talk识别";
        m_baiduClient->recognizeAudio(audioData, "pcm", 16000);
        return; // 异步识别，等待回调
    } 
    // 最后备用：使用高级语音引擎 
    else if (m_advancedEngine) {
        qDebug() << "🔧 [PTT] 尝试使用高级语音引擎进行Push-to-Talk识别";
        // 高级语音引擎需要启动监听模式
        if (m_advancedEngine->startListening()) {
            qDebug() << "✅ [PTT] 高级语音引擎已启动监听";
        } else {
            qDebug() << "❌ [PTT] 高级语音引擎启动失败";
            setState(StateIdle);
            emit speechError("语音识别引擎启动失败");
        }
        return; // 异步识别
    }
    else {
        qWarning() << "❌ [PTT] 所有语音识别客户端都未初始化";
        setState(StateIdle);
        emit speechError("语音识别服务不可用");
    }
}

QString SpeechManager::performVoiceRecognition(const QByteArray &audioData)
{
    // 使用百度语音识别API - 避免重复调用
    qDebug() << "🚀 [ASR] 开始语音识别处理...";
    
    // 检查音频数据是否有效
    if (audioData.isEmpty()) {
        qDebug() << "❌ [ASR] 音频数据为空";
        return QString();
    }
    
    qDebug() << "📊 [ASR] 音频数据大小:" << audioData.size() << "字节";
    
    // 如果正在识别中，跳过本次处理
    if (m_currentState == StateRecognizing) {
        qDebug() << "⏳ [ASR] 正在识别中，跳过本次音频数据";
        return QString();
    }
    
    // 提取音频特征
    qDebug() << "🔍 [ASR] 提取音频特征...";
    AudioFeatures features = extractAudioFeatures(audioData);
    
    qDebug() << "📈 [ASR] 音频特征 - 能量级别:" << features.energy << ", 阈值:0.01";
    
    // 检查是否有语音活动（大大降低阈值，更容易触发）
    if (features.energy > 0.01) { // 从0.05降低到0.01
        qDebug() << "✅ [ASR] 检测到语音活动，使用百度API识别，音频大小:" << audioData.size() << "字节";
        
        // 保存音频数据用于备用识别
        m_lastAudioData = audioData;
        
        // 优先使用百度官方SDK
        if (m_baiduOfficialClient && m_baiduOfficialClient->isInitialized()) {
            qDebug() << "🔧 [ASR] 使用百度官方SDK进行识别";
            setState(StateRecognizing); // 设置为识别状态，避免重复调用
            m_baiduOfficialClient->recognizeAudio(audioData, "pcm", 16000);
            return QString(); // 异步识别，不返回任何文本，等待回调
        } 
        // 备用：使用自定义百度客户端
        else if (m_baiduClient && m_baiduClient->isInitialized()) {
            qDebug() << "🔧 [ASR] 使用百度语音客户端进行识别";
            setState(StateRecognizing); // 设置为识别状态，避免重复调用
            m_baiduClient->recognizeAudio(audioData, "pcm", 16000);
            return QString(); // 异步识别，不返回任何文本，等待回调
        } 
        // 最后备用：使用高级语音引擎 
        else if (m_advancedEngine) {
            qDebug() << "🔧 [ASR] 尝试使用高级语音引擎进行识别";
            setState(StateRecognizing);
            // 高级语音引擎需要启动监听模式
            if (m_advancedEngine->startListening()) {
                qDebug() << "✅ [ASR] 高级语音引擎已启动监听";
            } else {
                qDebug() << "❌ [ASR] 高级语音引擎启动失败";
                setState(StateIdle);
            }
            return QString(); // 异步识别
        }
        else {
            qWarning() << "❌ [ASR] 所有语音识别客户端都未初始化";
            return QString("语音识别服务不可用");
        }
    } else {
        qDebug() << "🔇 [ASR] 未检测到有效语音，能量级别:" << features.energy << " (阈值:0.01)";
        return QString();
    }
}

SpeechManager::AudioFeatures SpeechManager::extractAudioFeatures(const QByteArray &audioData)
{
    AudioFeatures features = {0.0, 0.0, 0.0, 0.0};
    
    if (audioData.size() < 2) return features;
    
    const qint16 *samples = reinterpret_cast<const qint16*>(audioData.constData());
    int sampleCount = audioData.size() / sizeof(qint16);
    
    // 1. 计算能量 (RMS)
    double sumSquares = 0.0;
    for (int i = 0; i < sampleCount; ++i) {
        sumSquares += samples[i] * samples[i];
    }
    features.energy = sqrt(sumSquares / sampleCount) / 32768.0;
    
    // 2. 估算基频 (简单的自相关方法)
    features.pitch = estimatePitch(samples, sampleCount);
    
    // 3. 计算音调变化 (标准差)
    QVector<double> pitchValues;
    int windowSize = sampleCount / 10; // 分成10个窗口
    for (int i = 0; i < 10 && (i + 1) * windowSize < sampleCount; ++i) {
        double windowPitch = estimatePitch(samples + i * windowSize, windowSize);
        if (windowPitch > 0) {
            pitchValues.append(windowPitch);
        }
    }
    
    if (!pitchValues.isEmpty()) {
        double mean = 0.0;
        for (double p : pitchValues) mean += p;
        mean /= pitchValues.size();
        
        double variance = 0.0;
        for (double p : pitchValues) {
            variance += (p - mean) * (p - mean);
        }
        features.tonalVariation = (mean > 0.001) ? sqrt(variance / pitchValues.size()) / mean : 0.0;
    }
    
    // 4. 计算持续时间 (基于能量阈值)
    double energyThreshold = features.energy * 0.3;
    int activeSamples = 0;
    for (int i = 0; i < sampleCount; ++i) {
        if (abs(samples[i]) / 32768.0 > energyThreshold) {
            activeSamples++;
        }
    }
    features.duration = (double)activeSamples / 16000.0; // 假设16kHz采样率
    
    qDebug() << QString("音频特征: 能量=%1, 音调=%2Hz, 变化=%3, 时长=%4s")
                .arg(features.energy, 0, 'f', 2)
                .arg(features.pitch, 0, 'f', 0)
                .arg(features.tonalVariation, 0, 'f', 2)
                .arg(features.duration, 0, 'f', 1);
    
    return features;
}

double SpeechManager::estimatePitch(const qint16 *samples, int sampleCount)
{
    if (sampleCount < 100) return 0.0;
    
    // 简单的自相关基频估计
    int minPeriod = 16000 / 800;  // 最高800Hz
    int maxPeriod = 16000 / 80;   // 最低80Hz
    
    double maxCorrelation = 0.0;
    int bestPeriod = 0;
    
    for (int period = minPeriod; period < maxPeriod && period < sampleCount / 2; ++period) {
        double correlation = 0.0;
        int count = sampleCount - period;
        
        for (int i = 0; i < count; ++i) {
            correlation += samples[i] * samples[i + period];
        }
        
        correlation /= count;
        
        if (correlation > maxCorrelation) {
            maxCorrelation = correlation;
            bestPeriod = period;
        }
    }
    
    if (bestPeriod > 0 && maxCorrelation > 0) {
        return 16000.0 / bestPeriod; // 转换为频率
    }
    
    return 0.0;
}

double SpeechManager::calculateSimilarity(const AudioFeatures &f1, const AudioFeatures &f2)
{
    // 计算特征向量的相似度 (使用加权欧几里得距离)
    double energyMax = qMax(f1.energy, f2.energy);
    double pitchMax = qMax(f1.pitch, f2.pitch);
    double tonalMax = qMax(f1.tonalVariation, f2.tonalVariation);
    double durationMax = qMax(f1.duration, f2.duration);
    
    // 避免除零错误
    double energyDiff = (energyMax > 0.001) ? abs(f1.energy - f2.energy) / energyMax : 0.0;
    double pitchDiff = (pitchMax > 0.001) ? abs(f1.pitch - f2.pitch) / pitchMax : 0.0;
    double tonalDiff = (tonalMax > 0.001) ? abs(f1.tonalVariation - f2.tonalVariation) / tonalMax : 0.0;
    double durationDiff = (durationMax > 0.001) ? abs(f1.duration - f2.duration) / durationMax : 0.0;
    
    // 加权计算相似度 (能量和音调更重要)
    double similarity = 1.0 - (0.3 * energyDiff + 0.4 * pitchDiff + 0.2 * tonalDiff + 0.1 * durationDiff);
    
    return qMax(0.0, qMin(1.0, similarity));
}

// 删除executeVoiceCommand方法 - 只保留纯语音转文字功能

void SpeechManager::useSystemTTS(const QString &text)
{
    qDebug() << "使用系统TTS合成:" << text;
    
    // 优先尝试espeak和其他TTS引擎
    QStringList commands = {
        QString("spd-say"),         // speech-dispatcher最简单
        QString("espeak -s %1").arg(m_speed + 80),  // espeak英文
        QString("festival --tts")   // festival备选
    };
    
    QStringList arguments;
    QString program;
    
    static int currentCommandIndex = 0;
    if (currentCommandIndex >= commands.size()) {
        currentCommandIndex = 0;
    }
    
    // 根据不同的TTS程序设置不同的参数
    if (currentCommandIndex == 0) { // spd-say
        program = "spd-say";
        arguments << text;
    } else if (currentCommandIndex == 1) { // espeak
        program = "espeak";
        arguments << "-s" << QString::number(m_speed + 80) << text;
    } else if (currentCommandIndex == 2) { // festival
        program = "festival";
        arguments << "--tts" << text;
    }
    
    QProcess *ttsProcess = new QProcess(this);
    connect(ttsProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            [this, ttsProcess, text, currentCommandIndex](int exitCode, QProcess::ExitStatus exitStatus) {
                Q_UNUSED(exitStatus)
                if (exitCode == 0) {
                    qDebug() << "系统TTS播放完成:" << text;
                    setState(StateIdle);
                    // 🔧 防止语音反馈：重置TTS播放状态
                    m_isTTSPlaying = false;
                    emit synthesisFinished();
                    emit ttsFinished();
                } else {
                    qWarning() << "TTS命令失败，exitCode:" << exitCode;
                    // 尝试下一个TTS引擎
                    useNextTTSEngine(text);
                }
                ttsProcess->deleteLater();
            });
    
    qDebug() << "执行TTS命令:" << program << arguments;
    
    ttsProcess->start(program, arguments);
    if (!ttsProcess->waitForStarted(3000)) {
        qWarning() << "无法启动TTS程序:" << program;
        useNextTTSEngine(text);
        ttsProcess->deleteLater();
    }
}

void SpeechManager::useNextTTSEngine(const QString &text)
{
    static int commandIndex = 0;
    commandIndex++;
    
    if (commandIndex >= 3) { // 尝试了所有3个引擎
        commandIndex = 0;
        emit speechError("所有TTS引擎都不可用");
        setState(StateIdle);
        return;
    }
    
    // 延迟100ms后尝试下一个引擎
    QTimer::singleShot(100, [this, text]() {
        useSystemTTS(text);
    });
}

// 删除processVoiceCommand方法 - 只保留纯语音转文字功能

QString SpeechManager::trySystemSpeechRecognition(const QByteArray &audioData)
{
    qDebug() << "尝试简单语音识别...";
    
    // 简化的语音识别 - 避免卡死
    // 这里可以集成真实的语音识别API，目前返回模拟结果
    Q_UNUSED(audioData)
    
    // 模拟语音识别结果（避免长时间等待导致卡死）
    qDebug() << "简单语音识别完成";
    return QString(); // 返回空字符串表示识别失败
}

QString SpeechManager::tryOnlineSpeechRecognition(const QByteArray &audioData)
{
    qDebug() << "尝试在线语音识别...";
    
    // 简化的在线语音识别 - 避免网络请求导致卡死
    // 这里可以集成真实的在线语音识别API
    Q_UNUSED(audioData)
    
    // 模拟在线识别结果（避免网络请求导致卡死）
    qDebug() << "在线语音识别完成";
    return QString(); // 返回空字符串表示识别失败
}

// 删除百度API - 使用简化的语音识别

// 删除trySimpleCommandMatching方法 - 只保留纯语音转文字功能

bool SpeechManager::saveAudioToWavFile(const QByteArray &audioData, const QString &filename)
{
    if (audioData.isEmpty()) {
        return false;
    }
    
    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly)) {
        return false;
    }
    
    // WAV文件头
    struct WavHeader {
        char riff[4] = {'R', 'I', 'F', 'F'};
        quint32 fileSize;
        char wave[4] = {'W', 'A', 'V', 'E'};
        char fmt[4] = {'f', 'm', 't', ' '};
        quint32 fmtSize = 16;
        quint16 audioFormat = 1; // PCM
        quint16 numChannels = 1; // 单声道
        quint32 sampleRate = 16000; // 16kHz
        quint32 byteRate = 32000; // sampleRate * numChannels * bitsPerSample / 8
        quint16 blockAlign = 2; // numChannels * bitsPerSample / 8
        quint16 bitsPerSample = 16; // 16位
        char data[4] = {'d', 'a', 't', 'a'};
        quint32 dataSize;
    };
    
    WavHeader header;
    header.dataSize = audioData.size();
    header.fileSize = sizeof(WavHeader) - 8 + audioData.size();
    
    file.write(reinterpret_cast<const char*>(&header), sizeof(header));
    file.write(audioData);
    file.close();
    
    return true;
}

QString SpeechManager::convertAudioToBase64(const QByteArray &audioData)
{
    return audioData.toBase64();
}

// 百度TTS槽函数实现
void SpeechManager::onBaiduTTSStarted()
{
    qDebug() << "百度TTS开始合成";
    setState(StateSynthesizing);
}

void SpeechManager::onBaiduTTSFinished()
{
    qDebug() << "百度TTS合成完成";
    setState(StateIdle);
    // 🔧 防止语音反馈：重置TTS播放状态
    m_isTTSPlaying = false;
    emit synthesisFinished();
    emit ttsFinished();
}

void SpeechManager::onBaiduTTSError(const QString &error)
{
    qWarning() << "百度TTS错误:" << error;
    
    // 🔧 防止语音反馈：重置TTS播放状态
    m_isTTSPlaying = false;
    setState(StateIdle);
    
    // 遇到错误时，尝试使用系统TTS作为备用
    QString lastText = "语音合成失败，请重试"; // 默认提示文本
    qDebug() << "百度TTS失败，切换到系统TTS备用方案";
    useSystemTTS(lastText);
} 