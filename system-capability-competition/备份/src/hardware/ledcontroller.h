#ifndef LEDCONTROLLER_H
#define LEDCONTROLLER_H

#include <QObject>
#include <QFile>
#include <QDir>
#include <QFileInfo>
#include <QStringList>
#include <QTextStream>
#include <QDebug>
#include <QTimer>

class LedController : public QObject
{
    Q_OBJECT

public:
    explicit LedController(QObject *parent = nullptr);
    ~LedController();

    // LED控制方法
    bool setLedState(int ledIndex, bool on);
    bool getLedState(int ledIndex);
    bool toggleLed(int ledIndex);
    
    // 获取LED信息
    int getLedCount() const { return m_ledPaths.count(); }
    QString getLedName(int ledIndex) const;
    QStringList getAllLedNames() const;
    
    // 语音控制相关
    bool controlLedByName(const QString &ledName, bool on);
    bool toggleLedByName(const QString &ledName);
    
    // 初始化和检查
    bool initializeLeds();
    bool isLedValid(int ledIndex) const;

public slots:
    // 语音控制槽函数
    void handleVoiceLedCommand(const QString &command);
    
    // LED状态控制
    void turnOnAllLeds();
    void turnOffAllLeds();
    void toggleAllLeds();

signals:
    // LED状态变化信号
    void ledStateChanged(int ledIndex, bool isOn);
    void ledControlResult(bool success, const QString &message);
    void allLedsStateChanged(bool allOn);

private:
    // 内部方法
    void scanLedDevices();
    QString getBrightnessPath(int ledIndex) const;
    bool writeLedBrightness(const QString &path, bool on);
    bool readLedBrightness(const QString &path);
    
    // 成员变量
    QStringList m_ledPaths;        // LED设备路径列表
    QStringList m_ledNames;        // LED设备名称列表
    QString m_ledsBasePath;        // LED基础路径
    QTimer *m_statusCheckTimer;    // 状态检查定时器
    
    // 常量
    static const QString LED_BASE_PATH;
    static const QString BRIGHTNESS_FILE;
};

#endif // LEDCONTROLLER_H 