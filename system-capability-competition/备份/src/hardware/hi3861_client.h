#ifndef HI3861_CLIENT_H
#define HI3861_CLIENT_H

#include <QObject>
#include <QTcpSocket>
#include <QTimer>
#include <QHostAddress>
#include <QDateTime>

class Hi3861Client : public QObject
{
    Q_OBJECT

public:
    enum DeviceType {
        LEFT_EYE,
        RIGHT_EYE
    };

    enum EmotionType {
        NEUTRAL = 0,
        HAPPY,
        SAD,
        ANGRY,
        SURPRISED,
        FEAR,
        DISGUSTED
    };

    explicit Hi3861Client(DeviceType deviceType, QObject *parent = nullptr);
    ~Hi3861Client();

    // 连接控制
    void connectToDevice(const QString &ipAddress, quint16 port = 8889);
    void disconnectFromDevice();
    bool isConnected() const;

    // 设备信息
    DeviceType getDeviceType() const { return m_deviceType; }
    QString getDeviceTypeString() const;
    QString getIpAddress() const { return m_ipAddress; }
    void setIpAddress(const QString &ipAddress);

    // 表情控制
    void sendEmotionCommand(EmotionType emotion, int intensity = 100);
    void sendCustomCommand(const QString &command);  // 发送自定义命令
    void sendHeartbeat();
    void requestDeviceInfo();

signals:
    void connected();
    void disconnected();
    void connectionError(const QString &error);
    void emotionSent(EmotionType emotion, int intensity);
    void heartbeatResponse();
    void deviceInfoReceived(const QString &deviceType, const QString &status);

private slots:
    void onConnected();
    void onDisconnected();
    void onReadyRead();
    void onError(QAbstractSocket::SocketError error);
    void sendHeartbeatTimer();

private:
    void sendCommand(const QString &command);
    QString emotionTypeToString(EmotionType emotion) const;
    void performNetworkDiagnostics();

    DeviceType m_deviceType;
    QString m_ipAddress;
    quint16 m_port;
    QTcpSocket *m_socket;
    QTimer *m_heartbeatTimer;
    bool m_isConnected;
    QDateTime m_connectionStartTime;
};

#endif // HI3861_CLIENT_H 