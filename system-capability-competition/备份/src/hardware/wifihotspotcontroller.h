#ifndef WIFISPOTCONTROLLER_H
#define WIFISPOTCONTROLLER_H

#include <QObject>
#include <QProcess>

class WiFiHotspotController : public QObject
{
    Q_OBJECT
public:
    explicit WiFiHotspotController(QObject *parent = nullptr);
    ~WiFiHotspotController();

    bool isHotspotActive() const;
    QString getWirelessInterface() const;

signals:
    void hotspotStateChanged(bool isActive, const QString &message);
    void logMessage(const QString &message);

public slots:
    void startHotspot();
    void stopHotspot();

private slots:
    void onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus);
    void onProcessErrorOccurred(QProcess::ProcessError error);
    void handleReadyReadStandardOutput();
    void handleReadyReadStandardError();

private:
    void runCommand(const QString &program, const QStringList &arguments);
    bool checkDependencies();
    void findWirelessInterface();
    void cleanup();
    void startProcess(const QString& program, const QStringList& args);


    QProcess *m_process;
    bool m_hotspotActive;
    QString m_interfaceName;
    QList<QProcess*> m_managedProcesses;
};

#endif // WIFISPOTCONTROLLER_H 