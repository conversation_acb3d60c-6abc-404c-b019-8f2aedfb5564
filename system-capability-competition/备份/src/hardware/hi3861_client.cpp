#include "hi3861_client.h"
#include <QDebug>
#include <QDataStream>

Hi3861Client::Hi3861Client(DeviceType deviceType, QObject *parent)
    : QObject(parent)
    , m_deviceType(deviceType)
    , m_port(8889)
    , m_socket(nullptr)
    , m_heartbeatTimer(new QTimer(this))
    , m_isConnected(false)
{
    // 设置默认IP地址
    if (m_deviceType == LEFT_EYE) {
        m_ipAddress = "**************";  // 默认左眼IP
    } else {
        m_ipAddress = "**************";  // 默认右眼IP
    }

    // 设置心跳定时器
    m_heartbeatTimer->setInterval(5000);  // 5秒心跳
    connect(m_heartbeatTimer, &QTimer::timeout, this, &Hi3861Client::sendHeartbeatTimer);
}

Hi3861Client::~Hi3861Client()
{
    disconnectFromDevice();
}

void Hi3861Client::connectToDevice(const QString &ipAddress, quint16 port)
{
    if (!ipAddress.isEmpty()) {
        m_ipAddress = ipAddress;
    }
    m_port = port;

    // 详细的连接前检查
    qDebug() << QString("🔗 [%1] 开始连接流程").arg(getDeviceTypeString());
    qDebug() << QString("🔗 [%1] 目标IP: %2").arg(getDeviceTypeString()).arg(m_ipAddress);
    qDebug() << QString("🔗 [%1] 目标端口: %3").arg(getDeviceTypeString()).arg(m_port);
    
    // 验证IP地址格式
    QHostAddress hostAddr(m_ipAddress);
    if (hostAddr.isNull()) {
        qWarning() << QString("❌ [%1] IP地址格式无效: %2").arg(getDeviceTypeString()).arg(m_ipAddress);
        emit connectionError("IP地址格式无效");
        return;
    }
    
    // 执行网络连通性检查
    performNetworkDiagnostics();
    
    // 检查当前连接状态
    if (m_socket) {
        qDebug() << QString("🔗 [%1] 清理旧连接，当前状态: %2").arg(getDeviceTypeString()).arg(m_socket->state());
        m_socket->deleteLater();
    }

    m_socket = new QTcpSocket(this);
    
    // 设置连接超时
    connect(m_socket, &QTcpSocket::connected, this, &Hi3861Client::onConnected);
    connect(m_socket, &QTcpSocket::disconnected, this, &Hi3861Client::onDisconnected);
    connect(m_socket, &QTcpSocket::readyRead, this, &Hi3861Client::onReadyRead);
    connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::error),
            this, &Hi3861Client::onError);

    qDebug() << QString("🔗 [%1] 正在连接到设备: %2:%3")
                .arg(getDeviceTypeString())
                .arg(m_ipAddress)
                .arg(m_port);

    // 记录连接开始时间
    m_connectionStartTime = QDateTime::currentDateTime();
    
    m_socket->connectToHost(QHostAddress(m_ipAddress), m_port);
    
    qDebug() << QString("🔗 [%1] connectToHost() 调用完成，等待连接结果...").arg(getDeviceTypeString());
}

void Hi3861Client::disconnectFromDevice()
{
    m_heartbeatTimer->stop();
    
    if (m_socket && m_socket->state() != QAbstractSocket::UnconnectedState) {
        m_socket->disconnectFromHost();
        if (m_socket->state() != QAbstractSocket::UnconnectedState) {
            m_socket->waitForDisconnected(3000);
        }
    }
    
    m_isConnected = false;
}

bool Hi3861Client::isConnected() const
{
    return m_isConnected && m_socket && 
           m_socket->state() == QAbstractSocket::ConnectedState;
}

QString Hi3861Client::getDeviceTypeString() const
{
    return (m_deviceType == LEFT_EYE) ? "LEFT_EYE" : "RIGHT_EYE";
}

void Hi3861Client::setIpAddress(const QString &ipAddress)
{
    m_ipAddress = ipAddress;
    qDebug() << QString("%1设备IP地址更新为: %2")
                .arg(getDeviceTypeString())
                .arg(m_ipAddress);
}

void Hi3861Client::sendEmotionCommand(EmotionType emotion, int intensity)
{
    QString emotionStr = emotionTypeToString(emotion);
    
    if (!isConnected()) {
        qWarning() << QString("❌ [%1] 设备未连接，无法发送表情命令: %2").arg(getDeviceTypeString()).arg(emotionStr);
        return;
    }

    QString command = emotionStr;
    
    qDebug() << QString("📤 [%1] 发送表情命令: %2").arg(getDeviceTypeString()).arg(emotionStr);
    
    // 发送命令
    sendCommand(command);
                
    emit emotionSent(emotion, intensity);
}

void Hi3861Client::sendCustomCommand(const QString &command)
{
    if (!isConnected()) {
        qWarning() << QString("❌ [%1] 设备未连接，无法发送自定义命令: %2").arg(getDeviceTypeString()).arg(command);
        return;
    }
    
    qDebug() << QString("📤 [%1] 发送自定义命令: %2").arg(getDeviceTypeString()).arg(command);
    
    // 发送命令
    sendCommand(command);
}

void Hi3861Client::sendHeartbeat()
{
    if (!isConnected()) {
        return;
    }
    
    sendCommand("HEARTBEAT");
}

void Hi3861Client::requestDeviceInfo()
{
    if (!isConnected()) {
        return;
    }
    
    sendCommand("DEVICE_INFO");
}

void Hi3861Client::onConnected()
{
    m_isConnected = true;
    
    // 计算连接耗时
    qint64 connectionTime = m_connectionStartTime.msecsTo(QDateTime::currentDateTime());
    
    qDebug() << QString("✅ [%1] 设备连接成功").arg(getDeviceTypeString());
    
    // 启动心跳
    m_heartbeatTimer->start();
    
    // 请求设备信息
    requestDeviceInfo();
    
    emit connected();
}

void Hi3861Client::onDisconnected()
{
    m_isConnected = false;
    m_heartbeatTimer->stop();
    
    qDebug() << QString("%1设备连接断开").arg(getDeviceTypeString());
    emit disconnected();
}

void Hi3861Client::onReadyRead()
{
    if (!m_socket) {
        return;
    }
    
    QByteArray data = m_socket->readAll();
    QString response = QString::fromUtf8(data).trimmed();
    
    qDebug() << QString("收到%1设备响应: %2")
                .arg(getDeviceTypeString())
                .arg(response);
    
    // 解析响应
    if (response == "HEARTBEAT_OK") {
        emit heartbeatResponse();
    } else if (response.startsWith("DEVICE_INFO:")) {
        QStringList parts = response.split(":");
        if (parts.size() >= 3) {
            QString deviceType = parts[1];
            QString status = parts[2];
            emit deviceInfoReceived(deviceType, status);
        }
    } else if (response == "EMOTION_OK") {
        // 表情命令执行成功
        qDebug() << QString("%1设备表情命令执行成功").arg(getDeviceTypeString());
    }
}

void Hi3861Client::onError(QAbstractSocket::SocketError error)
{
    QString errorString;
    QString errorDetail;
    
    switch (error) {
        case QAbstractSocket::ConnectionRefusedError:
            errorString = "连接被拒绝";
            errorDetail = "目标设备可能未启动或端口未开放";
            break;
        case QAbstractSocket::RemoteHostClosedError:
            errorString = "远程主机关闭连接";
            errorDetail = "设备主动断开了连接";
            break;
        case QAbstractSocket::HostNotFoundError:
            errorString = "主机未找到";
            errorDetail = "IP地址无法解析或设备不在网络中";
            break;
        case QAbstractSocket::SocketTimeoutError:
            errorString = "连接超时";
            errorDetail = "网络延迟过高或设备响应缓慢";
            break;
        case QAbstractSocket::NetworkError:
            errorString = "网络错误";
            errorDetail = "网络连接问题或路由不可达";
            break;
        default:
            errorString = QString("未知错误 (代码: %1)").arg(error);
            errorDetail = "请检查网络连接和设备状态";
            break;
    }
    
    qWarning() << QString("❌ [%1] 连接失败: %2").arg(getDeviceTypeString()).arg(errorString);
    
    emit connectionError(errorString);
}

void Hi3861Client::sendHeartbeatTimer()
{
    sendHeartbeat();
}

void Hi3861Client::sendCommand(const QString &command)
{
    if (!m_socket || m_socket->state() != QAbstractSocket::ConnectedState) {
        qWarning() << QString("❌ [%1] 设备未连接，无法发送命令 (Socket状态: %2)")
                      .arg(getDeviceTypeString())
                      .arg(m_socket ? QString::number(m_socket->state()) : "null");
        return;
    }
    
    QByteArray data = command.toUtf8();
    
    // 执行发送
    qint64 written = m_socket->write(data);
    
    if (written == -1) {
        qWarning() << QString("❌ [%1] 命令发送失败: %2").arg(getDeviceTypeString()).arg(command);
    } else {
        // 强制发送缓冲区数据
        m_socket->flush();
        qDebug() << QString("✅ [%1] 命令已发送: %2 (%3字节)").arg(getDeviceTypeString()).arg(command).arg(written);
    }
}

QString Hi3861Client::emotionTypeToString(EmotionType emotion) const
{
    switch (emotion) {
        case HAPPY: return "HAPPY";
        case SAD: return "SAD";
        case ANGRY: return "ANGRY";
        case SURPRISED: return "SURPRISED";
        case FEAR: return "FEAR";
        case DISGUSTED: return "DISGUSTED";
        default: return "NEUTRAL";
    }
}

void Hi3861Client::performNetworkDiagnostics()
{
    qDebug() << QString("🔍 [%1] 开始网络诊断...").arg(getDeviceTypeString());
    
    // 简单的端口连通性测试
    QTcpSocket testSocket;
    testSocket.connectToHost(QHostAddress(m_ipAddress), m_port);
    
    if (testSocket.waitForConnected(3000)) {
        qDebug() << QString("✅ [%1] 端口%2可达").arg(getDeviceTypeString()).arg(m_port);
        testSocket.disconnectFromHost();
        if (testSocket.state() != QAbstractSocket::UnconnectedState) {
            testSocket.waitForDisconnected(1000);
        }
    } else {
        qWarning() << QString("❌ [%1] 端口%2不可达或无响应").arg(getDeviceTypeString()).arg(m_port);
        qWarning() << QString("❌ [%1] 端口测试错误: %2").arg(getDeviceTypeString()).arg(testSocket.errorString());
        
        // 提供排查建议
        qWarning() << QString("💡 [%1] 排查建议:").arg(getDeviceTypeString());
        qWarning() << QString("💡 [%1] 1. 检查Hi3861设备是否已启动").arg(getDeviceTypeString());
        qWarning() << QString("💡 [%1] 2. 检查设备TCP服务器是否在端口%2上监听").arg(getDeviceTypeString()).arg(m_port);
        qWarning() << QString("💡 [%1] 3. 检查防火墙设置").arg(getDeviceTypeString());
        qWarning() << QString("💡 [%1] 4. 尝试使用telnet命令测试: telnet %2 %3").arg(getDeviceTypeString()).arg(m_ipAddress).arg(m_port);
    }
} 