#ifndef DEVICEMANAGER_H
#define DEVICEMANAGER_H

#include <QObject>
#include <QTimer>
#include <QMap>

class DeviceManager : public QObject
{
    Q_OBJECT

public:
    explicit DeviceManager(QObject *parent = nullptr);
    ~DeviceManager();

    enum DeviceType {
        NetworkDisplay1,
        NetworkDisplay2,
        AudioDevice  // 只保留实际存在的设备
    };

    enum DeviceStatus {
        Unknown,
        Connected,
        Disconnected,
        Error,
        Initializing
    };

public slots:
    void initializeDevices();
    void checkDeviceStatus();
    void reconnectDevice(DeviceType type);
    void reconnectDevices();
    void resetDevice(DeviceType type);

signals:
    void deviceStatusChanged(DeviceType type, DeviceStatus status);
    void deviceConnectionChanged(bool connected);
    void allDevicesReady();
    void deviceError(DeviceType type, const QString &error);

private slots:
    void performStatusCheck();

private:
    void initializeDevice(DeviceType type);
    DeviceStatus checkDevice(DeviceType type);
    void setDeviceStatus(DeviceType type, DeviceStatus status);

    // 设备状态跟踪
    QMap<DeviceType, DeviceStatus> m_deviceStatus;
    QMap<DeviceType, QString> m_deviceNames;
    QTimer *m_statusCheckTimer;
    
    // 状态
    bool m_allInitialized;
    int m_connectedDevices;
    int m_totalDevices;
};

#endif // DEVICEMANAGER_H 