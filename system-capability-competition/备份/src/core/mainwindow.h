#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTimer>
#include <QVector>
#include <QButtonGroup>
#include <QKeyEvent>
#include <QComboBox>
#include <QLabel>
#include "../hardware/wifihotspotcontroller.h"
#include "../hardware/ledcontroller.h"
#include "../hardware/hi3861_client.h"
#include "chatdatabase.h"
#include "../ui/stylemanager.h"
#include "../ui/iconmanager.h"
#include "../sensor/weathermanager.h"
#include <QMediaPlayer>
#include <QAudioInput>
#include <QFile>

QT_BEGIN_NAMESPACE
class QLabel;
class QPushButton;
class QTextEdit;
class QProgressBar;
class QStackedWidget;
QT_END_NAMESPACE

namespace Ui {
class MainWindow;
}

class SpeechManager;
class EmotionController;
class SensorManager;
class NetworkController;
class DeviceManager;
class CameraManager;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

    // Hi3861设备管理公有接口
    void updateHi3861IpAddress(Hi3861Client::DeviceType deviceType, const QString &ipAddress);

private slots:
    // 导航相关槽函数
    void onNavSpeechClicked();
    void onNavEmotionClicked();
    void onNavSensorClicked();
    void onNavSystemClicked();

    // 语音相关槽函数
    void onStartListening();
    void onStopListening();
    void onTestTTS();
    void onSpeechResult(const QString &text);
    void onSpeechError(const QString &error);
    void onTTSFinished();
    void onTTSPlaybackFinished();  // 🎵 TTS播放完成槽函数
    void onWakeWordDetected();
    void onAudioLevelChanged(double level);
    // 删除onVoiceCommandDetected槽函数 - 只保留纯语音转文字功能

    // 表情相关槽函数
    void onSetHappyEmotion();
    void onSetSadEmotion();
    void onSetAngryEmotion();
    void onSetSurprisedEmotion();
    void onSetFearEmotion();
    void onSetNeutralEmotion();
    
    // 设备连接测试槽函数
    void onTestDeviceConnection();
    void onReconnectDevices();
    void onDeviceConnectionStatusChanged(bool leftEye, bool rightEye);
    void onDeviceTestResult(int deviceIndex, bool success);
    void onEmotionChanged(const QString &emotion, int intensity);

    // 传感器相关槽函数
    void onSensorDataUpdated();
    void onTemperatureChanged(double temperature);
    void onHumidityChanged(double humidity);
    void onLightChanged(int light);
    void onCalibrateClicked();
    void onRefreshSensorClicked();
    // 运动检测已删除
    
    // 天气相关槽函数
    void onWeatherDataUpdated();
    void onCityChanged();
    void onCitySliderChanged(int value);
    void onWeatherError(const QString &error);

    // 摄像头相关槽函数
    void onCameraStart();
    void onCameraStop();
    void onCameraSnapshot();
    void onCameraFrameReady();
    void onCameraError(const QString &error);

    // 系统相关槽函数
    void onReconnect();
    void onDebugLevelChanged(int index);
    void onDeviceConnectionChanged(bool connected);
    void onSettingsClicked();
    
    // WiFi热点相关槽函数
    void onStartHotspot();
    void onStopHotspot();
    void onHotspotConfigChanged();
    void onHotspotStateChanged(bool isRunning);
    void onHotspotDeviceConnected();
    void onHotspotDeviceDisconnected(const QString &macAddress);
    void onHotspotError(const QString &error);
    
    // 菜单槽函数（保留但可能用于快捷键）
    void onShowSettings();
    void onCalibrateDevices();
    void onTestDisplays();
    void onShowAbout();

    // 显示更新函数
    void updateStatusDisplay();
    void updateEmotionDisplay();
    void updateWaveDisplay();

    // 硬件测试槽函数
    void on_testSpeakerButton_clicked();
    void on_testMicrophoneButton_clicked();
    void on_testCameraButton_clicked();
    void stopRecording();

    // 休眠相关
    void onIdleTimeout();
    
    // 音乐播放相关槽函数
    void onPlayMusic();
    void onStopMusic();
    void onMusicStateChanged();
    void onMusicPositionChanged(qint64 position);
    void onMusicDurationChanged(qint64 duration);
    
    // LED控制相关槽函数
    void onLedStateChanged(int ledIndex, bool isOn);
    void onLedControlResult(bool success, const QString &message);
    void onAllLedsStateChanged(bool allOn);
    void onToggleLed();
    void onTurnOnAllLeds();
    void onTurnOffAllLeds();

    // Hi3861设备通信相关槽函数
    void onHi3861Connected();
    void onHi3861Disconnected();
    void onHi3861ConnectionError(const QString &error);
    void onHi3861EmotionSent(Hi3861Client::EmotionType emotion, int intensity);
    void onHi3861DeviceInfoReceived(const QString &deviceType, const QString &status);
    
    // Hi3861设备连接按钮槽函数
    void onConnectLeftEye();
    void onConnectRightEye();
    
    // 虚拟键盘按钮槽函数
    void onShowKeyboardForLeftEye();
    void onShowKeyboardForRightEye();

    // 🏠 离家回家处理函数
    void handleLeavingHomeCommand(const QString &text);
    void handleComingHomeCommand(const QString &text);
    QString generateWeatherAdvice(const WeatherManager::WeatherData &weather);
    QString generateTimeBasedAdvice();
    QString generateHomeStatusReport();
    QString generateTomorrowAdvice();
    
    // 💡 LED控制处理函数
    void handleLedCommand(const QString &text);
    void toggleLedByVoice(int ledIndex = 0);
    void turnOnAllLedsByVoice();
    void turnOffAllLedsByVoice();
    void queryLedStatus();
    
    // 🔘 按键事件处理函数（KEY7控制LED）
    void keyPressEvent(QKeyEvent *event) override;
    void keyReleaseEvent(QKeyEvent *event) override;

private:
    void setupWaveDisplay();
    void connectSignals();
    void logMessage(const QString &message);
    void setupNavigation();
    void switchToPage(int pageIndex);
    
    // 🎨 样式美化相关方法
    void applyModernStyles();
    void setupButtonStyles();
    void setupLabelStyles();
    void setupConversationDisplay();
    
    // 🆕 语音交互处理函数
    void processVoiceCommand(const QString &text);
    QString generateSmartResponse(const QString &command);
    void handleWeatherQuery(const QString &text);
    void handleEmotionControl(const QString &text);
    void handleTimeQuery();
    void handleGeneralChat(const QString &text);
    void speakResponse(const QString &response);
    
    // 🏠 离家回家助手辅助函数
    QString analyzeEmotionFromText(const QString &text);
    // 🆕 情感指令发送函数
    void sendEmotionCommand(ChatDB::EmotionType emotion);
    // 运动检测处理已删除
    
    // 🎵 音乐播放处理函数
    void handleMusicCommand(const QString &text);
    void startMusicPlayback();
    void stopMusicPlayback();
    void updateMusicUI();
    void updateMusicUIWithTitle(const QString &title);
    void switchToNextMusic();
    void initializeMusicPlaylist();
    // 🎵 音乐对话控制函数
    void pauseMusicForConversation();
    void resumeMusicAfterConversation();
    void adjustMusicVolume(bool increase);
    
    // 💡 LED控制辅助函数
    void updateLedUI();
    
    // 🎨 美化对话界面函数
    void addUserMessage(const QString &message);
    void addAIMessage(const QString &message);

    // 👁️ Hi3861设备管理函数
    void initializeHi3861Devices();
    void connectHi3861Devices();
    void disconnectHi3861Devices();
    void sendEmotionToHi3861(ChatDB::EmotionType emotion);
    void updateHi3861ConnectionStatus();

    // UI
    Ui::MainWindow *ui;

    // 管理器类
    SpeechManager *m_speechManager;
    EmotionController *m_emotionController;
    SensorManager *m_sensorManager;
    WeatherManager *m_weatherManager;
    NetworkController *m_networkController;
    DeviceManager *m_deviceManager;
    CameraManager *m_cameraManager;
    WiFiHotspotController *m_hotspotController;
    LedController *m_ledController;

    // Hi3861设备客户端
    Hi3861Client *m_leftEyeClient;
    Hi3861Client *m_rightEyeClient;

    // 导航相关
    QButtonGroup *m_navButtonGroup;

    // 定时器
    QTimer *m_statusUpdateTimer;
    QTimer *m_sensorUpdateTimer;
    QTimer *m_waveUpdateTimer;

    // 休眠相关
    QTimer *m_idleTimer;

    // 状态变量
    bool m_isListening;
    bool m_isConnected;
    bool m_autoRestartListening;  // 🆕 控制是否允许TTS结束后自动重新开始监听
    QString m_currentEmotion;
    
    // 波形显示相关
    double m_audioLevel;
    double m_wavePhase;
    QVector<double> m_waveData;

    QMediaPlayer *m_player;
    QAudioInput *m_audioInput = nullptr;
    QFile *m_audioFile = nullptr;
    QTimer *m_recordingTimer;
    
    // 🎵 音乐播放相关
    enum MusicAction {
        NO_ACTION,
        START_PLAYBACK,
        SWITCH_NEXT
    };
    
    QMediaPlayer *m_musicPlayer;
    bool m_isMusicPlaying;
    QString m_currentMusicFile;
    QStringList m_musicPlaylist;
    int m_currentMusicIndex;
    bool m_musicPausedForConversation; // 标记音乐是否因对话而暂停
    int m_musicVolume; // 当前音乐音量 (0-100)
    MusicAction m_pendingMusicAction; // 等待TTS完成后执行的音乐动作

    // 休眠定时器常量
    static const int IDLE_TIMEOUT_MS = 300000; // 5分钟
    void resetIdleTimer();

    void initConnections();
    void setupNav();
};

#endif // MAINWINDOW_H 