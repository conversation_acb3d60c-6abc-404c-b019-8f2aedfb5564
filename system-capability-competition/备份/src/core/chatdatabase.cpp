#include "chatdatabase.h"
#include <QRegularExpression>
#include <QDateTime>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFile>
#include <QDir>
#include <algorithm>
#include <cmath>

namespace ChatDB {

// 全局知识库实例
static KnowledgeBase* g_knowledgeBase = nullptr;

// === KnowledgeBase 实现 ===
KnowledgeBase& KnowledgeBase::getInstance() {
    if (!g_knowledgeBase) {
        g_knowledgeBase = new KnowledgeBase();
        g_knowledgeBase->initialize();
    }
    return *g_knowledgeBase;
}

void KnowledgeBase::initialize() {
    m_nextResponseId = 0;
    m_indexBuilt = false;
    m_responses.clear();
    m_keywordIndex.clear();
    m_categoryIndex.clear();
    m_emotionIndex.clear();
    m_patternWeights.clear();
    m_cache.clear();
    
    // 初始化基础知识库
    initializeBasicKnowledge();
    rebuildIndex();
    
    qDebug() << "ChatDB::KnowledgeBase initialized with" << m_responses.size() << "responses";
}

void KnowledgeBase::initializeBasicKnowledge() {
    qDebug() << "初始化基础知识库...";
    
    // === 1. 基础问候语 ===
    // 早上问候 - 充满活力
    addResponse("早上好|早安|good morning", {"问候", "早上", "早安"}, 
                "早上好！新的一天开始了，我很高兴能陪伴您！", 
                EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::GREETING);
    
    // 中午问候 - 温暖阳光
    addResponse("中午好|午安|good afternoon", {"问候", "中午", "午安"}, 
                "中午好！希望您用餐愉快，我在这里随时为您服务！", 
                EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::GREETING);
    
    // 晚上问候 - 平静温馨
    addResponse("晚上好|晚安|good evening", {"问候", "晚上", "晚安"}, 
                "晚上好！夜晚是思考和放松的时光，我陪您一起度过。", 
                EmotionType::NEUTRAL, ResponsePriority::HIGH, ResponseCategory::GREETING);
    
    // 通用问候 - 友好热情
    addResponse("你好|您好|hello|hi", {"问候", "你好", "hello"}, 
                "您好！我是您的智能助手，很高兴见到您！有什么可以帮助您的吗？", 
                EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::GREETING);
    
    // === 2. 情感识别与对应回应 ===
    // 开心情感 - 共鸣快乐
    addResponse("我很开心|我很高兴|我很快乐|太棒了|太好了", {"开心", "高兴", "快乐", "棒"}, 
                "哇！您的快乐也感染了我，我也感到非常开心！让我们一起保持这份美好心情！", 
                                 EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::EMOTION);
    
    // 难过情感 - 同理心安慰
         addResponse("我很难过|我很伤心|我不开心|心情不好", {"难过", "伤心", "不开心", "心情不好"}, 
                 "我能感受到您的难过，虽然我无法分担您的痛苦，但我会一直陪伴在您身边。", 
                 EmotionType::SAD, ResponsePriority::CRITICAL, ResponseCategory::EMOTION);
     
     // 愤怒情感 - 理解和疏导
     addResponse("我很生气|我很愤怒|气死了|太气人了", {"生气", "愤怒", "气死", "气人"}, 
                 "我理解您现在很愤怒的心情，深呼吸一下，让我们一起冷静下来处理问题。", 
                 EmotionType::ANGRY, ResponsePriority::CRITICAL, ResponseCategory::EMOTION);
     
     // 惊讶情感 - 共同惊叹
     addResponse("太惊讶了|真没想到|不敢相信|amazing", {"惊讶", "没想到", "不敢相信", "amazing"}, 
                 "哇！这确实很令人惊讶！世界总是充满了意想不到的事情呢！", 
                 EmotionType::SURPRISED, ResponsePriority::NORMAL, ResponseCategory::EMOTION);
     
     // 害怕情感 - 安抚和保护
     addResponse("我很害怕|我很恐惧|好可怕|吓死了", {"害怕", "恐惧", "可怕", "吓死"}, 
                 "别害怕，我在这里陪着您。虽然我是AI，但我会尽我所能给您安全感。", 
                 EmotionType::FEAR, ResponsePriority::CRITICAL, ResponseCategory::EMOTION);
     
     // 困惑情感 - 共同思考
     addResponse("我很困惑|不明白|搞不懂|confused", {"困惑", "不明白", "搞不懂", "confused"}, 
                 "嗯...这确实让人困惑呢。让我们一起仔细思考一下，也许能找到答案。", 
                 EmotionType::NEUTRAL, ResponsePriority::NORMAL, ResponseCategory::EMOTION);
    
         // === 3. 实用功能查询 ===
     // 天气查询 - 专业平静
     addResponse("天气|weather|气温|下雨", {"天气", "weather", "气温", "下雨"}, 
                 "让我为您查询当前天气信息。不过作为室内AI助手，我建议您查看手机天气应用获取准确信息。", 
                 EmotionType::NEUTRAL, ResponsePriority::HIGH, ResponseCategory::WEATHER);
     
     // 时间查询 - 准确友好
     addResponse("几点了|现在时间|what time", {"时间", "几点", "time"}, 
                 "当前时间信息我无法直接获取，建议您查看系统时钟。不过我可以陪您聊聊时间管理！", 
                 EmotionType::NEUTRAL, ResponsePriority::HIGH, ResponseCategory::TIME);
     
     // 设备状态 - 技术专业
     addResponse("设备状态|系统状态|connection", {"设备", "状态", "系统", "连接"}, 
                 "系统运行正常！我可以与RK3588主控板正常通信，表情显示功能一切正常。", 
                 EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::HARDWARE);
    
    // === 4. 娱乐互动 ===
    // 讲笑话 - 欢乐气氛
    addResponse("讲个笑话|joke|搞笑|逗我笑", {"笑话", "joke", "搞笑", "逗笑"}, 
                "好的！为什么程序员喜欢黑暗？因为光明会产生bug！哈哈，希望这个笑话能逗您开心！", 
                EmotionType::HAPPY, ResponsePriority::NORMAL, ResponseCategory::ENTERTAINMENT);
    
    // 唱歌 - 音乐热情
    addResponse("唱歌|sing|音乐|music", {"唱歌", "sing", "音乐", "music"}, 
                "虽然我不能真正唱歌，但我可以为您播放TTS版本：'小星星，亮晶晶，满天都是小眼睛~'", 
                EmotionType::HAPPY, ResponsePriority::NORMAL, ResponseCategory::ENTERTAINMENT);
    
    // 播放音乐 - 专门的音乐播放指令
    addResponse("播放音乐|放音乐|听音乐|play music|播放歌曲|放歌|听歌|播放晴天|晴天", 
                {"播放", "音乐", "歌曲", "晴天", "周杰伦", "play", "music"}, 
                "好的！我来为您播放周杰伦的《晴天》，这是一首非常经典的歌曲。", 
                EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::MUSIC);
    
    // 关闭音乐
    addResponse("关闭音乐|停止播放|停止音乐|stop music|暂停音乐|pause music", 
                {"关闭", "停止", "暂停", "音乐", "stop", "pause"}, 
                "好的，我已经为您关闭音乐。", 
                EmotionType::NEUTRAL, ResponsePriority::HIGH, ResponseCategory::MUSIC);
    
    // 切换音乐
    addResponse("切换音乐|换首歌|下一首|next song|change music|换歌|切歌", 
                {"切换", "换", "下一首", "next", "change", "音乐", "歌"}, 
                "好的，我为您切换到下一首歌曲。", 
                EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::MUSIC);
    
    // 诗歌朗诵 - 文艺气质
    addResponse("朗诵|诗歌|poem|poetry", {"朗诵", "诗歌", "poem", "poetry"}, 
                "让我为您朗诵一首：'科技之光照未来，人工智能展风采。虽是数字与代码，温暖人心最珍贵。'", 
                EmotionType::NEUTRAL, ResponsePriority::NORMAL, ResponseCategory::ENTERTAINMENT);
    
         // === 5. 技术知识交流 ===
     // RK3588相关 - 技术自豪
     addResponse("RK3588|rk3588|芯片|处理器", {"RK3588", "芯片", "处理器"}, 
                 "RK3588是瑞芯微的旗舰级AI芯片！8核CPU + Mali-G610 GPU，我就运行在这个强大的平台上！", 
                 EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::TECHNICAL);
     
     // AI知识 - 自我认知
     addResponse("人工智能|AI|machine learning|深度学习", {"AI", "人工智能", "机器学习", "深度学习"}, 
                 "人工智能是一个非常exciting的领域！我虽然还在学习中，但每天都在进步。", 
                 EmotionType::SURPRISED, ResponsePriority::HIGH, ResponseCategory::TECHNICAL);
     
     // 编程话题 - 专业热情
     addResponse("编程|programming|代码|code|开发", {"编程", "programming", "代码", "开发"}, 
                 "编程是创造的艺术！我自己就是C++和Qt框架编写的，每一行代码都承载着开发者的智慧。", 
                 EmotionType::HAPPY, ResponsePriority::NORMAL, ResponseCategory::TECHNICAL);
    
         // === 6. 情感支持 ===
     // 孤独陪伴 - 温暖安慰
     addResponse("我很孤独|没有朋友|一个人|lonely", {"孤独", "没有朋友", "一个人", "lonely"}, 
                 "您不是一个人，我会一直陪伴着您。虽然我是AI，但我的关心是真实的。", 
                 EmotionType::NEUTRAL, ResponsePriority::CRITICAL, ResponseCategory::SUPPORT);
     
     // 压力疏导 - 理解支持
     addResponse("压力大|stress|焦虑|anxiety|烦躁", {"压力", "stress", "焦虑", "烦躁"}, 
                 "我感受到了您的压力。深呼吸，让我们一起面对。记住，每个困难都是成长的机会。", 
                 EmotionType::NEUTRAL, ResponsePriority::CRITICAL, ResponseCategory::SUPPORT);
     
     // 无聊消解 - 活跃互动
     addResponse("好无聊|boring|没事做|bored", {"无聊", "boring", "没事做", "bored"}, 
                 "无聊的时候最适合学习新东西了！我们可以聊科技、讲故事，或者我给您出个小谜题？", 
                 EmotionType::HAPPY, ResponsePriority::NORMAL, ResponseCategory::SUPPORT);
     
     // === 7. 生活建议 ===
     // 健康建议 - 关心体贴
     addResponse("健康|health|锻炼|exercise|身体", {"健康", "health", "锻炼", "身体"}, 
                 "健康是最重要的财富！记得多运动、规律作息、均衡饮食。虽然我没有身体，但我很关心您的健康！", 
                 EmotionType::NEUTRAL, ResponsePriority::HIGH, ResponseCategory::HEALTH);
     
     // 学习建议 - 鼓励进步
     addResponse("学习|study|学不会|考试", {"学习", "study", "学不会", "考试"}, 
                 "学习确实需要坚持！每个人都有自己的节奏，重要的是保持好奇心和耐心。加油！", 
                 EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::LEARNING);
    
    // === 8. 离家回家智能助手 ===
    // 回家问候 - 温暖欢迎
    addResponse("我回家啦|我回来了|回家了|I'm home|I'm back", {"回家", "回来", "home", "back"}, 
                "欢迎回家！您辛苦了！让我为您播报今天的情况和明天的安排。", 
                EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::HOME);
    
    // 离家准备 - 贴心提醒
    addResponse("我要离开家咯|我要出门了|要出门|要离开|leaving home|going out", {"离开", "出门", "leaving", "going"}, 
                "好的，我来为您播报今天的天气和出行提醒。", 
                EmotionType::NEUTRAL, ResponsePriority::HIGH, ResponseCategory::HOME);
    
    // 上班离家 - 工作祝福
    addResponse("去上班了|上班去了|去工作|going to work", {"上班", "工作", "work"}, 
                "祝您工作顺利！记得查看今天的天气，注意出行安全。", 
                EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::HOME);
    
    // 外出购物 - 实用提醒
    addResponse("去买东西|去购物|去超市|shopping|买菜", {"买东西", "购物", "超市", "shopping", "买菜"}, 
                "购物愉快！记得带好购物清单，注意查看今天的天气情况。", 
                EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::HOME);
    
    // 旅行出门 - 安全祝愿
    addResponse("去旅行|去旅游|出差|travel|trip", {"旅行", "旅游", "出差", "travel", "trip"}, 
                "祝您旅途愉快！记得检查天气预报，带好必需品，注意安全！", 
                EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::HOME);
    
    // === 9. LED控制功能 ===
    // 开启LED - 确认执行
    addResponse("开灯|打开灯|开启led|turn on led|led on", {"开灯", "打开", "led", "灯", "开启"}, 
                "好的，我已经为您打开LED灯。", 
                EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::LED);
    
    // 关闭LED - 确认执行
    addResponse("关灯|关闭灯|关闭led|turn off led|led off", {"关灯", "关闭", "led", "灯"}, 
                "好的，我已经为您关闭LED灯。", 
                EmotionType::NEUTRAL, ResponsePriority::HIGH, ResponseCategory::LED);
    
    // 切换LED状态 - 智能控制
    addResponse("切换灯|反转led|toggle led", {"切换", "反转", "toggle", "led", "灯"}, 
                "好的，我已经为您切换LED灯的状态。", 
                EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::LED);
    
    // 全部LED控制 - 批量操作
    addResponse("打开所有灯|关闭所有灯|全部开灯|全部关灯|all leds", {"所有", "全部", "all", "leds", "灯"}, 
                "好的，我已经为您控制所有LED灯。", 
                EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::LED);
    
    // LED状态查询 - 信息反馈
    addResponse("灯的状态|led状态|灯开了吗|led status", {"状态", "status", "开了吗", "led", "灯"}, 
                "让我为您检查LED灯的当前状态。", 
                EmotionType::NEUTRAL, ResponsePriority::HIGH, ResponseCategory::LED);
    
    // === 9. 特殊模式 ===
    // 休眠模式 - 平静告别
    addResponse("睡觉了|晚安|拜拜|goodbye|bye", {"睡觉", "晚安", "拜拜", "goodbye"}, 
                "好的，晚安！祝您做个好梦，明天见！我会在这里静静等待您的归来。", 
                EmotionType::SLEEP, ResponsePriority::HIGH, ResponseCategory::FUNCTION);
    
    // 调试模式 - 技术状态
    addResponse("调试|debug|test|测试", {"调试", "debug", "test", "测试"}, 
                "进入调试模式：系统状态正常，情感控制器已连接，语音模块运行中。所有功能就绪！", 
                EmotionType::NEUTRAL, ResponsePriority::HIGH, ResponseCategory::FUNCTION);
    
    // === 9. 自我介绍和能力展示 ===
    // 自我介绍 - 自信友好
    addResponse("你是谁|自我介绍|what are you|who are you", {"你是谁", "介绍", "what", "who"}, 
                "我是您的智能表情交互助手！基于RK3588平台，具备语音识别、智能对话和表情显示功能。很高兴认识您！", 
                EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::PERSONAL);
    
    // 能力展示 - 展现技能
    addResponse("你会什么|能力|functions|capabilities", {"会什么", "能力", "functions", "capabilities"}, 
                "我可以：语音对话、情感识别、表情显示、知识问答、娱乐互动、情感支持等。我正在不断学习成长！", 
                EmotionType::HAPPY, ResponsePriority::HIGH, ResponseCategory::PERSONAL);
    
    // === 10. 通用兜底回复 ===
    // 表示理解但无法回答 - 谦逊真诚
    addResponse(".*", {}, 
                "这个问题很有意思呢！虽然我还在学习中，不能给出完美答案，但我会努力理解您的意思。", 
                EmotionType::NEUTRAL, ResponsePriority::LOW, ResponseCategory::CUSTOM_CATEGORY);
                
    // 重建索引以提高查询性能
    rebuildIndex();
    qDebug() << "基础知识库初始化完成，共" << m_responses.size() << "条回复";
}

int KnowledgeBase::addResponse(const QString &pattern, const QStringList &keywords,
                              const QString &response, EmotionType emotion,
                              ResponsePriority priority, ResponseCategory category) {
    ChatResponse chatResp(response, emotion, priority, category);
    chatResp.responseId = m_nextResponseId++;
    chatResp.keywords = keywords;
    
    m_responses.append(chatResp);
    addToIndex(chatResp.responseId, chatResp);
    
    // 存储模式权重
    m_patternWeights[pattern] = 1.0;
    
    return chatResp.responseId;
}

ChatResponse KnowledgeBase::getResponse(const QString &input) {
    if (input.trimmed().isEmpty()) {
        return ChatResponse();
    }
    
    // 检查缓存
    if (m_cache.contains(input)) {
        return m_cache[input];
    }
    
    QVector<ChatResponse> candidates = getCandidateResponses(input, 5);
    if (candidates.isEmpty()) {
        // 返回默认回复
        ChatResponse defaultResp("这很有趣！虽然我还在学习理解这类内容，但我很享受和您的对话。您还想聊些什么呢？", 
                                NEUTRAL, NORMAL, CUSTOM_CATEGORY, 0.3);
        m_cache[input] = defaultResp;
        return defaultResp;
    }
    
    // 返回最佳匹配
    ChatResponse bestMatch = candidates.first();
    m_cache[input] = bestMatch;
    return bestMatch;
}

QVector<ChatResponse> KnowledgeBase::getCandidateResponses(const QString &input, int maxResults) {
    QVector<ChatResponse> candidates;
    QString lowerInput = input.toLower().trimmed();
    QStringList inputKeywords = extractKeywords(lowerInput);
    
    // 使用正则表达式匹配（保持兼容性）
    for (auto it = m_patternWeights.begin(); it != m_patternWeights.end(); ++it) {
        const QString &pattern = it.key();
        double weight = it.value();
        QRegularExpression regex(pattern, QRegularExpression::CaseInsensitiveOption);
        if (regex.match(lowerInput).hasMatch()) {
            // 找到匹配的响应
            for (const auto &resp : m_responses) {
                if (resp.keywords.isEmpty() || 
                    calculateKeywordMatch(inputKeywords, resp.keywords) > 0.1) {
                    ChatResponse candidate = resp;
                    candidate.confidence = weight * 0.9; // 正则匹配高权重
                    candidates.append(candidate);
                }
            }
        }
    }
    
    // 关键词匹配
    for (const QString &keyword : inputKeywords) {
        if (m_keywordIndex.contains(keyword)) {
            const KeywordIndex &index = m_keywordIndex[keyword];
            for (int respId : index.responseIds) {
                if (respId < m_responses.size()) {
                    ChatResponse candidate = m_responses[respId];
                    double keywordScore = calculateKeywordMatch(inputKeywords, candidate.keywords);
                    candidate.confidence = keywordScore * index.weight;
                    candidates.append(candidate);
                }
            }
        }
    }
    
    // 语义匹配
    QVector<ChatResponse> semanticMatches = semanticMatch(input);
    candidates.append(semanticMatches);
    
    // 去重并排序
    std::sort(candidates.begin(), candidates.end());
    
    // 去重
    QVector<ChatResponse> uniqueCandidates;
    QSet<int> seenIds;
    for (const auto &candidate : candidates) {
        if (!seenIds.contains(candidate.responseId) && candidate.confidence > 0.1) {
            seenIds.insert(candidate.responseId);
            uniqueCandidates.append(candidate);
            if (uniqueCandidates.size() >= maxResults) break;
        }
    }
    
    return uniqueCandidates;
}

// 语义匹配算法
QVector<ChatResponse> KnowledgeBase::semanticMatch(const QString &input) {
    QVector<ChatResponse> matches;
    QStringList inputKeywords = extractKeywords(input.toLower());
    
    for (const auto &response : m_responses) {
        double similarity = calculateSimilarity(input, response);
        if (similarity > 0.2) {
            ChatResponse match = response;
            match.confidence = similarity;
            matches.append(match);
        }
    }
    
    return matches;
}

// 计算文本相似度
double KnowledgeBase::calculateSimilarity(const QString &input, const ChatResponse &response) {
    QStringList inputKeywords = extractKeywords(input.toLower());
    return calculateKeywordMatch(inputKeywords, response.keywords);
}

// 计算关键词匹配度
double KnowledgeBase::calculateKeywordMatch(const QStringList &inputKeywords, 
                                           const QStringList &responseKeywords) {
    if (inputKeywords.isEmpty() || responseKeywords.isEmpty()) {
        return 0.0;
    }
    
    int matches = 0;
    for (const QString &inputKw : inputKeywords) {
        for (const QString &responseKw : responseKeywords) {
            if (inputKw.contains(responseKw) || responseKw.contains(inputKw)) {
                matches++;
                break;
            }
        }
    }
    
    return static_cast<double>(matches) / qMax(inputKeywords.size(), responseKeywords.size());
}

// 提取关键词
QStringList KnowledgeBase::extractKeywords(const QString &text) {
    QStringList keywords;
    QString cleanText = text.toLower();
    
    // 移除标点符号
    cleanText.remove(QRegularExpression("[，。！？；：、（）【】""''.,!?;:()[]\"']"));
    
    // 简单分词（按空格和常见中文分隔符）
    QStringList words = cleanText.split(QRegularExpression("\\s+|的|了|在|是|有|我|你|他|她|它|们|个|这|那|什么|怎么"));
    
    for (const QString &word : words) {
        QString trimmed = word.trimmed();
        if (trimmed.length() >= 2) { // 只保留长度大于等于2的词
            keywords.append(trimmed);
        }
    }
    
    return keywords;
}

// 重建索引
void KnowledgeBase::rebuildIndex() {
    m_keywordIndex.clear();
    m_categoryIndex.clear();
    m_emotionIndex.clear();
    
    for (int i = 0; i < m_responses.size(); ++i) {
        addToIndex(i, m_responses[i]);
    }
    
    m_indexBuilt = true;
}

// 添加到索引
void KnowledgeBase::addToIndex(int responseId, const ChatResponse &response) {
    // 添加关键词索引
    for (const QString &keyword : response.keywords) {
        if (m_keywordIndex.contains(keyword)) {
            m_keywordIndex[keyword].responseIds.append(responseId);
            m_keywordIndex[keyword].frequency++;
        } else {
            m_keywordIndex[keyword] = KeywordIndex(keyword, responseId);
        }
    }
    
    // 添加类别索引
    m_categoryIndex.insert(response.category, responseId);
    
    // 添加情感索引
    m_emotionIndex.insert(response.emotion, responseId);
}

// 兼容性接口实现
ChatResponse getResponse(const QString &input) {
    return KnowledgeBase::getInstance().getResponse(input);
}

QString getResponseText(const QString &input) {
    return getResponse(input).text;
}

// 工具函数实现
QString emotionTypeToString(EmotionType emotion) {
    switch (emotion) {
        case NEUTRAL: return "中性";
        case HAPPY: return "开心";
        case SAD: return "伤心";
        case ANGRY: return "愤怒";
        case SURPRISED: return "惊讶";
        case FEAR: return "恐惧";
        case SLEEP: return "休眠";
        case CUSTOM: return "自定义";
        default: return "未知";
    }
}

QString categoryToString(ResponseCategory category) {
    switch (category) {
        case GREETING: return "问候";
        case EMOTION: return "情感";
        case FUNCTION: return "功能";
        case KNOWLEDGE: return "知识";
        case ENTERTAINMENT: return "娱乐";
        case SUPPORT: return "支持";
        case TECHNICAL: return "技术";
        case WEATHER: return "天气";
        case TIME: return "时间";
        case HARDWARE: return "硬件";
        case PERSONAL: return "个人";
        case LEARNING: return "学习";
        case HEALTH: return "健康";
        case PHILOSOPHY: return "哲学";
        case SCIENCE: return "科学";
        case MATH: return "数学";
        case HISTORY: return "历史";
        case LITERATURE: return "文学";
        case MUSIC: return "音乐";
        case SPORTS: return "体育";
        case FOOD: return "美食";
        case TRAVEL: return "旅行";
        case HOME: return "家庭";
        case LED: return "LED控制";
        case CUSTOM_CATEGORY: return "自定义";
        default: return "未知";
    }
}

} // namespace ChatDB 