#ifndef CONFIG_H
#define CONFIG_H

// 系统配置文件

// 硬件配置
#define RK3588_PLATFORM 1
#define HI3861_COUNT 2
#define ST7789_RESOLUTION_WIDTH 480
#define ST7789_RESOLUTION_HEIGHT 270

// 显示屏配置
#define DISPLAY_1_SPI_BUS 0
#define DISPLAY_2_SPI_BUS 1
#define SPI_SPEED 24000000  // 24MHz

// GPIO 配置
#define EMOTION_LED_COUNT 12
#define SENSOR_COUNT 4

// 语音配置
#define SPEECH_SAMPLE_RATE 16000
#define SPEECH_CHANNELS 1
#define SPEECH_BITS_PER_SAMPLE 16

// 科大讯飞配置
#define XUNFEI_APP_ID "your_app_id"
#define XUNFEI_API_KEY "your_api_key"
#define XUNFEI_SECRET_KEY "your_secret_key"

// 网络配置
#define DEFAULT_TIMEOUT_MS 5000
#define MAX_RETRY_COUNT 3

// 调试配置
#define DEBUG_MODE 1
#define LOG_LEVEL 2  // 0=Error, 1=Warning, 2=Info, 3=Debug

// 系统限制
#define MAX_AUDIO_BUFFER_SIZE 4096
#define MAX_TEXT_LENGTH 1024
#define MAX_EMOTION_FRAMES 100

// 文件路径
#define EMOTION_DATA_PATH "/opt/qt_cpp/emotions/"
#define AUDIO_TEMP_PATH "/tmp/audio/"
#define LOG_FILE_PATH "/var/log/qt_cpp.log"

#endif // CONFIG_H 