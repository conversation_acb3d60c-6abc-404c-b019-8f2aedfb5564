#ifndef CHATDATABASE_H
#define CHATDATABASE_H

#include <QString>
#include <QHash>
#include <QMultiHash>
#include <QVector>
#include <QSet>
#include <QStringList>

namespace ChatDB {
    // 情感类型枚举（与EmotionController保持一致）
    enum EmotionType {
        NEUTRAL = 0,     // 中性
        HAPPY = 1,       // 开心
        SAD = 2,         // 伤心
        ANGRY = 3,       // 愤怒
        SURPRISED = 4,   // 惊讶
        FEAR = 5,        // 恐惧
        SLEEP = 6,       // 休眠
        CUSTOM = 7       // 自定义
    };
    
    // 响应优先级
    enum ResponsePriority {
        LOW = 0,
        NORMAL = 1,
        HIGH = 2,
        CRITICAL = 3
    };
    
    // 响应类别
    enum ResponseCategory {
        GREETING = 0,           // 问候
        EMOTION = 1,           // 情感
        FUNCTION = 2,          // 功能
        KNOWLEDGE = 3,         // 知识
        ENTERTAINMENT = 4,     // 娱乐
        SUPPORT = 5,           // 支持
        TECHNICAL = 6,         // 技术
        WEATHER = 7,           // 天气
        TIME = 8,              // 时间
        HARDWARE = 9,          // 硬件
        PERSONAL = 10,         // 个人
        LEARNING = 11,         // 学习
        HEALTH = 12,           // 健康
        PHILOSOPHY = 13,       // 哲学
        SCIENCE = 14,          // 科学
        MATH = 15,             // 数学
        HISTORY = 16,          // 历史
        LITERATURE = 17,       // 文学
        MUSIC = 18,            // 音乐
        SPORTS = 19,           // 体育
        FOOD = 20,             // 美食
        TRAVEL = 21,           // 旅行
        HOME = 22,             // 家庭/离家回家
        LED = 23,              // LED控制
        CUSTOM_CATEGORY = 99   // 自定义类别
    };
    
    // 增强的聊天响应结构体
    struct ChatResponse {
        QString text;                    // 回复文本
        EmotionType emotion;            // 对应的情感类型
        ResponsePriority priority;       // 响应优先级
        ResponseCategory category;       // 响应类别
        QStringList keywords;           // 关键词列表
        double confidence;              // 匹配置信度
        int responseId;                 // 响应ID
        
        ChatResponse() 
            : emotion(NEUTRAL), priority(NORMAL), category(CUSTOM_CATEGORY)
            , confidence(0.0), responseId(-1) {}
            
        ChatResponse(const QString &t, EmotionType e, 
                    ResponsePriority p = NORMAL, 
                    ResponseCategory c = CUSTOM_CATEGORY,
                    double conf = 1.0)
            : text(t), emotion(e), priority(p), category(c)
            , confidence(conf), responseId(-1) {}
        
        // 检查是否为有效回复
        bool isValid() const { return !text.isEmpty() && confidence > 0.0; }
        
        // 比较函数，用于排序（优先级高的在前）
        bool operator<(const ChatResponse &other) const {
            if (priority != other.priority) {
                return priority > other.priority; // 优先级高的在前
            }
            return confidence > other.confidence; // 置信度高的在前
        }
    };
    
    // 关键词索引结构
    struct KeywordIndex {
        QString keyword;                // 关键词
        QVector<int> responseIds;       // 对应的响应ID列表
        int frequency;                  // 词频
        double weight;                  // 权重
        
        KeywordIndex() : frequency(0), weight(1.0) {}
        KeywordIndex(const QString &kw, int respId, double w = 1.0)
            : keyword(kw), frequency(1), weight(w) {
            responseIds.append(respId);
        }
    };
    
    // 高性能知识库管理类
    class KnowledgeBase {
    public:
        static KnowledgeBase& getInstance();
        
        // 初始化知识库
        void initialize();
        
        // 获取响应（主要接口）
        ChatResponse getResponse(const QString &input);
        
        // 获取多个候选响应
        QVector<ChatResponse> getCandidateResponses(const QString &input, int maxResults = 5);
        
        // 添加新的响应规则
        int addResponse(const QString &pattern, const QStringList &keywords,
                       const QString &response, EmotionType emotion,
                       ResponsePriority priority = NORMAL, 
                       ResponseCategory category = CUSTOM_CATEGORY);
        
        // 更新响应
        bool updateResponse(int responseId, const QString &newResponse);
        
        // 删除响应
        bool removeResponse(int responseId);
        
        // 学习功能：从用户反馈中学习
        void learnFromFeedback(const QString &input, const QString &expectedResponse, bool positive);
        
        // 获取统计信息
        struct Statistics {
            int totalResponses;
            int totalKeywords;
            double avgConfidence;
            QHash<ResponseCategory, int> categoryCount;
            QHash<EmotionType, int> emotionCount;
        };
        Statistics getStatistics() const;
        
        // 导出/导入知识库
        bool exportToFile(const QString &filename) const;
        bool importFromFile(const QString &filename);
        
    private:
        KnowledgeBase() = default;
        ~KnowledgeBase() = default;
        KnowledgeBase(const KnowledgeBase&) = delete;
        KnowledgeBase& operator=(const KnowledgeBase&) = delete;
        
        // 内部数据结构
        QVector<ChatResponse> m_responses;          // 所有响应
        QHash<QString, KeywordIndex> m_keywordIndex; // 关键词索引
        QMultiHash<ResponseCategory, int> m_categoryIndex; // 类别索引
        QMultiHash<EmotionType, int> m_emotionIndex;       // 情感索引
        QHash<QString, double> m_patternWeights;    // 模式权重
        
        // 索引管理
        void rebuildIndex();
        void addToIndex(int responseId, const ChatResponse &response);
        void removeFromIndex(int responseId);
        
        // 初始化基础知识库
        void initializeBasicKnowledge();
        
        // 文本处理
        QStringList extractKeywords(const QString &text);
        QStringList segmentText(const QString &text);
        double calculateSimilarity(const QString &input, const ChatResponse &response);
        double calculateKeywordMatch(const QStringList &inputKeywords, const QStringList &responseKeywords);
        
        // 智能匹配算法
        QVector<ChatResponse> fuzzyMatch(const QString &input);
        QVector<ChatResponse> semanticMatch(const QString &input);
        QVector<ChatResponse> contextMatch(const QString &input);
        
        // 内部状态
        int m_nextResponseId;
        bool m_indexBuilt;
        mutable QHash<QString, ChatResponse> m_cache; // 响应缓存
    };
    
    // 兼容性接口
    ChatResponse getResponse(const QString &input);
    QString getResponseText(const QString &input);
    
    // 工具函数
    QString emotionTypeToString(EmotionType emotion);
    EmotionType stringToEmotionType(const QString &emotionStr);
    QString categoryToString(ResponseCategory category);
    ResponseCategory stringToCategory(const QString &categoryStr);
}

#endif // CHATDATABASE_H 