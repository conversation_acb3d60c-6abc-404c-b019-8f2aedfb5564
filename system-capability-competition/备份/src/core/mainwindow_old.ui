<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>768</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>RK3588智能表情交互系统</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QMainWindow {
    background-color: #2b2b2b;
    color: white;
}

QGroupBox {
    font-weight: bold;
    border: 2px solid #cccccc;
    border-radius: 5px;
    margin-top: 1ex;
    padding-top: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
}

QPushButton {
    background-color: #404040;
    border: 1px solid #606060;
    border-radius: 4px;
    padding: 8px;
    color: white;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #505050;
}

QPushButton:pressed {
    background-color: #303030;
}

QTextEdit {
    background-color: #1e1e1e;
    border: 1px solid #606060;
    border-radius: 4px;
    color: white;
    font-family: "Consolas", "Monaco", monospace;
}

QLabel {
    color: white;
}

QProgressBar {
    border: 1px solid #606060;
    border-radius: 4px;
    text-align: center;
    background-color: #1e1e1e;
}

QProgressBar::chunk {
    background-color: #4CAF50;
    border-radius: 3px;
}

QSlider::groove:horizontal {
    border: 1px solid #606060;
    height: 8px;
    background: #1e1e1e;
    margin: 2px 0;
    border-radius: 4px;
}

QSlider::handle:horizontal {
    background: #4CAF50;
    border: 1px solid #606060;
    width: 18px;
    margin: -2px 0;
    border-radius: 9px;
}

QComboBox {
    background-color: #404040;
    border: 1px solid #606060;
    border-radius: 4px;
    padding: 4px;
    color: white;
}

QCheckBox {
    color: white;
}

QCheckBox::indicator {
    width: 13px;
    height: 13px;
}

QCheckBox::indicator:unchecked {
    background-color: #1e1e1e;
    border: 1px solid #606060;
}

QCheckBox::indicator:checked {
    background-color: #4CAF50;
    border: 1px solid #606060;
}

QStatusBar {
    background-color: #1e1e1e;
    border-top: 1px solid #606060;
    color: white;
}</string>
  </property>
  <widget class="QWidget" name="centralwidget"/>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1024</width>
     <height>22</height>
    </rect>
   </property>
   <widget class="QMenu" name="menu_file">
    <property name="title">
     <string>文件(&amp;F)</string>
    </property>
    <addaction name="action_exit"/>
   </widget>
   <widget class="QMenu" name="menu_tools">
    <property name="title">
     <string>工具(&amp;T)</string>
    </property>
    <addaction name="action_settings"/>
    <addaction name="action_calibrate"/>
    <addaction name="separator"/>
    <addaction name="action_test_speech"/>
    <addaction name="action_test_displays"/>
   </widget>
   <widget class="QMenu" name="menu_help">
    <property name="title">
     <string>帮助(&amp;H)</string>
    </property>
    <addaction name="action_about"/>
   </widget>
   <addaction name="menu_file"/>
   <addaction name="menu_tools"/>
   <addaction name="menu_help"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="action_exit">
   <property name="text">
    <string>退出(&amp;X)</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Q</string>
   </property>
  </action>
  <action name="action_settings">
   <property name="text">
    <string>设置(&amp;S)</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+P</string>
   </property>
  </action>
  <action name="action_calibrate">
   <property name="text">
    <string>校准传感器(&amp;C)</string>
   </property>
  </action>
  <action name="action_test_speech">
   <property name="text">
    <string>测试语音(&amp;V)</string>
   </property>
  </action>
  <action name="action_test_displays">
   <property name="text">
    <string>测试显示屏(&amp;D)</string>
   </property>
  </action>
  <action name="action_about">
   <property name="text">
    <string>关于(&amp;A)</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui> 