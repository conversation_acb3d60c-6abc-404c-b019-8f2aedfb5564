#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QDebug>
#include <QTextStream>
#include <QTimer>
#include <iostream>
#include <string>
#include "mainwindow.h"
#include "../hardware/hi3861_client.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // 设置应用程序信息
    app.setApplicationName("RK3588智能表情交互系统");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("系统能力大赛团队");

    // 设置样式
    app.setStyle(QStyleFactory::create("Fusion"));

    // 设置深色主题
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::WindowText, Qt::white);
    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
    darkPalette.setColor(QPalette::Text, Qt::white);
    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ButtonText, Qt::white);
    darkPalette.setColor(QPalette::BrightText, Qt::red);
    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::HighlightedText, Qt::black);
    app.setPalette(darkPalette);

    // IP地址选择
    std::cout << "==================================" << std::endl;
    std::cout << " RK3588智能表情交互系统" << std::endl;
    std::cout << "==================================" << std::endl;
    std::cout << "Hi3861设备IP地址配置:" << std::endl;
    std::cout << "默认IP地址:" << std::endl;
    std::cout << "  左眼: **************" << std::endl;
    std::cout << "  右眼: **************" << std::endl;
    std::cout << "----------------------------------" << std::endl;
    std::cout << "请选择IP配置方式:" << std::endl;
    std::cout << "1. 使用默认IP地址" << std::endl;
    std::cout << "2. 手动输入IP地址" << std::endl;
    std::cout << "请输入选择 (1或2): ";
    
    std::string choice;
    std::getline(std::cin, choice);
    
    QString leftEyeIP = "**************";  // 默认左眼IP
    QString rightEyeIP = "**************"; // 默认右眼IP
    
    if (choice == "2") {
        std::cout << "请输入左眼设备IP地址 (回车使用默认 **************): ";
        std::string leftInput;
        std::getline(std::cin, leftInput);
        if (!leftInput.empty()) {
            leftEyeIP = QString::fromStdString(leftInput);
        }
        
        std::cout << "请输入右眼设备IP地址 (回车使用默认 **************): ";
        std::string rightInput;
        std::getline(std::cin, rightInput);
        if (!rightInput.empty()) {
            rightEyeIP = QString::fromStdString(rightInput);
        }
        
        std::cout << "设置完成!" << std::endl;
        std::cout << "左眼IP: " << leftEyeIP.toStdString() << std::endl;
        std::cout << "右眼IP: " << rightEyeIP.toStdString() << std::endl;
    } else {
        std::cout << "使用默认IP地址配置" << std::endl;
    }
    std::cout << "==================================" << std::endl;

    // 创建并显示主窗口
    MainWindow window;
    
    // 如果用户选择了自定义IP，更新IP地址
    if (choice == "2") {
        // 需要等待Hi3861设备初始化完成后再更新IP
        QTimer::singleShot(1000, [&window, leftEyeIP, rightEyeIP]() {
            window.updateHi3861IpAddress(Hi3861Client::LEFT_EYE, leftEyeIP);
            window.updateHi3861IpAddress(Hi3861Client::RIGHT_EYE, rightEyeIP);
        });
    }
    
    window.show();

    qDebug() << "RK3588智能表情交互系统启动完成";
    qDebug() << "左眼IP:" << leftEyeIP;
    qDebug() << "右眼IP:" << rightEyeIP;

    return app.exec();
} 