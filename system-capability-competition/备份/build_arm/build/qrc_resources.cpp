/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 5.15.8
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

static const unsigned char qt_resource_data[] = {
  // /home/<USER>/桌面/系统能力大赛/qt_cpp/src/ui/styles/modern_style.qss
  0x0,0x0,0x7,0x6b,
  0x0,
  0x0,0x1d,0xdf,0x78,0x9c,0xb5,0x58,0x7b,0x6f,0xd3,0x56,0x14,0xff,0xbf,0x9f,0xe2,
  0x8a,0xa,0xa9,0x45,0x71,0xb8,0xd7,0xaf,0x38,0xae,0x26,0x6d,0x48,0x93,0x36,0xd,
  0xa4,0x31,0x26,0x21,0xfe,0x42,0xd7,0xf6,0x75,0x62,0xe1,0xd8,0x99,0xed,0xd0,0x76,
  0x8,0x69,0x13,0x63,0x40,0xc5,0x86,0xc4,0xc6,0x34,0xa6,0x31,0x28,0x7b,0xa8,0x9a,
  0x34,0xb6,0x69,0xda,0x3,0xca,0xf8,0x34,0x4d,0xda,0x7e,0x8b,0x5d,0x3f,0x73,0xfd,
  0x4c,0x28,0x59,0xd2,0x58,0x69,0x7c,0x7d,0xcf,0x39,0xbf,0xf3,0x3b,0xaf,0x7b,0xf2,
  0x4,0x78,0xed,0x68,0xaf,0x25,0x0,0xc0,0x7b,0xef,0x8,0x92,0xa2,0x80,0xc9,0xfd,
  0x67,0x7,0xd7,0xfe,0xdd,0x7b,0xf6,0xc3,0xde,0xb3,0xbb,0xfb,0x7f,0xec,0xee,0xef,
  0x3e,0x4,0x1c,0xd8,0xff,0xfc,0xb7,0xbd,0xdd,0xef,0xc7,0xb7,0xbf,0x9a,0x3c,0xfa,
  0x7b,0xfc,0xfc,0xce,0xc1,0xf6,0x4e,0xf8,0xc4,0x11,0x85,0x81,0x13,0x27,0x97,0x96,
  0x4e,0xd6,0xea,0xa,0xc6,0xd7,0x77,0xc6,0xbf,0x7f,0x14,0x4b,0xaa,0x5d,0x44,0xf7,
  0x38,0x7b,0x6,0x5b,0xce,0x79,0xcb,0x31,0xdc,0x75,0x70,0x25,0xd4,0x7,0x68,0x58,
  0xbf,0xd4,0xf3,0xdc,0x91,0x63,0xa8,0xe0,0x3,0xdb,0x72,0x8,0xf6,0x7a,0x1e,0x36,
  0x2c,0xe2,0x4,0x2b,0x1b,0x48,0x85,0x2d,0xb0,0x19,0x5d,0x37,0xf8,0xe8,0x3b,0xaf,
  0xa2,0x56,0xf4,0x5c,0xf8,0xf2,0x3,0x77,0xa8,0x42,0xb0,0x6c,0x2a,0x26,0x36,0xf5,
  0x56,0xfc,0x3f,0x2,0xcb,0x84,0x27,0x8a,0x9,0x57,0xd7,0xa2,0x85,0xba,0x6b,0xbb,
  0x9e,0xa,0x96,0x5,0x41,0x44,0x92,0x14,0xff,0x66,0xba,0x4e,0xc0,0x99,0x78,0x60,
  0xd9,0x9b,0x2a,0x38,0x76,0xc6,0xd2,0x3d,0xd7,0x77,0xcd,0x0,0x5c,0xc0,0x6f,0x11,
  0xeb,0x58,0xb,0x1c,0x3b,0x67,0xd,0xe2,0x6f,0x6f,0x78,0x16,0xb6,0xe9,0xce,0xd8,
  0xf1,0x39,0x9f,0x78,0x96,0xb9,0xb6,0x74,0x75,0x69,0xe9,0xec,0x79,0xcb,0xe8,0x91,
  0xa0,0x64,0x2,0x97,0x8,0xb,0x3c,0xba,0x7e,0x88,0x3d,0x6a,0x44,0xb5,0x12,0x57,
  0x1b,0xd1,0x9c,0x3c,0xba,0x71,0xf8,0xf8,0xeb,0xc9,0xa3,0x3b,0xb3,0x1,0x5d,0xb6,
  0xb1,0x46,0xec,0x8b,0x7d,0x82,0xd,0xe2,0x5d,0xc,0xac,0xc0,0x26,0x89,0x56,0x91,
  0x8d,0xbe,0xf5,0x21,0x51,0x81,0x2c,0xe,0x37,0x18,0xc3,0xd7,0x89,0xd5,0xeb,0x7,
  0x2a,0xe8,0x42,0x98,0xd3,0x6e,0xbd,0x6f,0x5,0x64,0xed,0x65,0xbd,0x82,0x22,0xaf,
  0xc0,0xb2,0x57,0x64,0x41,0x96,0x4d,0x94,0x78,0x5,0xb6,0x25,0xb0,0xac,0x68,0x92,
  0x6e,0xca,0x53,0x3f,0x41,0x59,0x93,0xd,0x31,0xf1,0xd3,0x10,0x1b,0x86,0xe5,0xf4,
  0x54,0x20,0xc2,0xe1,0x6,0xe0,0x61,0xaa,0xb2,0xe6,0x7a,0xd4,0x34,0x2e,0x94,0x3e,
  0xf2,0x55,0x50,0xfc,0x5d,0x73,0x83,0xc0,0x1d,0xa8,0x40,0xa2,0xf,0xf9,0xae,0x6d,
  0x19,0xc0,0xeb,0x69,0x78,0x85,0x97,0xa4,0x56,0xfa,0x81,0x6d,0x21,0x91,0x11,0x90,
  0xd,0x8a,0x49,0x1f,0x53,0xfa,0xa9,0x80,0xf,0xc5,0xd0,0xf,0x5,0x27,0x7e,0x6,
  0xb6,0xa2,0x77,0xb4,0xba,0xd9,0x41,0xe3,0xcf,0xb6,0xf7,0x6f,0xdd,0x18,0x3f,0x79,
  0x3a,0xbe,0xbf,0x33,0xdb,0x47,0x6d,0x9d,0xa2,0x4e,0x91,0xe3,0x74,0xec,0x19,0x15,
  0xb4,0xcf,0x14,0x6,0xd3,0xb,0x6c,0x77,0xa5,0xd5,0x4a,0x0,0x8a,0xc8,0xa8,0x0,
  0x15,0x4c,0x87,0x2,0xdd,0x4,0x85,0x17,0x3e,0xda,0x29,0xdd,0x68,0x40,0x9d,0x68,
  0x39,0x74,0xbd,0x94,0xee,0x90,0x61,0xce,0x4b,0xd3,0x4d,0x37,0x32,0x84,0x20,0x40,
  0x91,0x2f,0xa4,0x29,0x42,0x20,0xf9,0x6b,0xa3,0x99,0x20,0x1d,0x6e,0xff,0x79,0xf8,
  0xe0,0x71,0xca,0xe5,0x99,0x20,0xd,0x71,0x8f,0x70,0x35,0x4,0x16,0x95,0x4a,0x2,
  0x6b,0xae,0x6d,0xfc,0xaf,0xc,0x96,0x3b,0x84,0xe0,0x29,0x5f,0x3b,0xb2,0xa8,0x61,
  0xbe,0xc8,0x57,0xa1,0x8e,0xaa,0x48,0x4e,0x6f,0xc4,0xc8,0x67,0x5c,0x9d,0xba,0x30,
  0x47,0xc8,0xd0,0x8f,0x28,0x21,0x65,0x8e,0x90,0xfc,0x4c,0xac,0xf7,0xfe,0xd9,0x3d,
  0xf8,0xe9,0xe3,0xc9,0xed,0x5b,0x87,0x77,0x9f,0xcc,0x91,0x85,0xdf,0x1d,0xf9,0xfd,
  0x53,0x23,0xaa,0x8c,0xb3,0xc0,0x2c,0x2c,0x68,0xa,0xcf,0x46,0x37,0x32,0x44,0x62,
  0x28,0xab,0x79,0xae,0x3a,0xae,0x43,0x66,0x80,0x95,0x23,0x25,0x10,0x32,0x66,0x96,
  0x7d,0x5c,0x43,0x6,0x86,0x38,0x42,0x46,0x9c,0x1,0xc5,0xbf,0x9f,0x2c,0x55,0x8e,
  0x80,0x7f,0x39,0x36,0xc2,0xbc,0x81,0xb2,0xd0,0x90,0xba,0x2d,0x80,0x4,0xa,0xe,
  0x2f,0xca,0x61,0x7c,0x24,0x49,0x84,0x81,0x5a,0xed,0xbb,0x97,0x89,0xb7,0x40,0xc0,
  0x79,0x49,0x16,0x88,0xc6,0x0,0x4e,0x44,0x88,0xcd,0x34,0xd5,0x85,0x25,0xc8,0x74,
  0xbd,0x41,0x52,0x8d,0x6c,0x1c,0x90,0xb,0x2b,0x1c,0xb5,0xad,0xd2,0x18,0x39,0xc9,
  0xb9,0xd5,0xc6,0x88,0x65,0x63,0x86,0x1e,0xf1,0x7d,0x52,0x95,0xce,0x8e,0x6a,0x4e,
  0xcc,0x17,0xd6,0x1c,0x1,0x2b,0xb8,0xd1,0x1c,0x58,0x63,0x4d,0xe8,0x41,0x54,0x6b,
  0x4d,0x85,0x6b,0xc,0xcb,0xc7,0x9a,0x5d,0x69,0xce,0x72,0x57,0xc4,0x94,0xda,0xf9,
  0x3a,0xae,0x6b,0x86,0x44,0x50,0x59,0x74,0x4c,0xef,0xe6,0x58,0xdd,0xbf,0xf5,0x74,
  0xf2,0x64,0x6b,0xbc,0xf5,0x90,0xf6,0x6c,0x71,0xc4,0x36,0xe6,0x45,0x2d,0x70,0x38,
  0x7f,0xa4,0xeb,0x14,0xee,0x45,0x82,0xd,0xb5,0xae,0x82,0x98,0x52,0x2c,0x75,0x65,
  0xb9,0x3b,0x7,0xcf,0x11,0x85,0x10,0x29,0xb4,0xae,0x20,0xbe,0xcb,0x80,0xc9,0xaa,
  0xb9,0x70,0xa2,0xc7,0xca,0x31,0xca,0x8a,0x1d,0x45,0xea,0xcc,0xc1,0xe3,0xa2,0xb2,
  0x22,0xa3,0xac,0x81,0x9d,0xde,0x42,0xb5,0x24,0xa6,0x48,0x5f,0x53,0x2d,0xd,0x9d,
  0x97,0x79,0x79,0xe,0x48,0x79,0x81,0x2a,0x27,0x2b,0xf1,0x27,0x8f,0x68,0xac,0xe4,
  0xc2,0x1,0x8d,0x55,0x9b,0xaa,0xaa,0x75,0x91,0x8e,0xf4,0x39,0x0,0x2d,0xa8,0xca,
  0xe2,0xb9,0x8e,0x3d,0x87,0xa6,0xed,0x45,0xb6,0xf5,0x52,0x97,0x40,0x26,0xbf,0x19,
  0xdd,0x4e,0x7,0xce,0x5,0xa8,0x18,0xba,0x5c,0xa2,0x2a,0x22,0x54,0x44,0x34,0x51,
  0x73,0xf1,0x90,0x46,0xca,0x31,0x90,0x8a,0x92,0x0,0xab,0x3,0xaa,0x0,0x69,0x41,
  0x59,0x71,0x66,0xb1,0xa7,0x2d,0xd5,0xfe,0x2f,0x2f,0xe6,0x28,0xf3,0xa7,0xc3,0xd9,
  0x20,0xb1,0x31,0xcd,0x5c,0x62,0x47,0x92,0xe4,0x6e,0xb9,0x50,0x66,0xe5,0x77,0x8e,
  0x69,0x26,0xab,0xd0,0x51,0x75,0xd,0x91,0xf5,0x3,0x1c,0x8c,0x7c,0xce,0x66,0x4,
  0xb2,0x9b,0xf3,0xa5,0xda,0x1e,0x39,0xab,0xb6,0xc9,0x47,0x7c,0x59,0x9b,0xb4,0x4f,
  0x16,0x51,0x98,0xc8,0xc3,0x3e,0x59,0x8c,0x62,0xba,0xd8,0x61,0x4c,0xbb,0xe1,0x64,
  0xe,0x2c,0x34,0xbf,0x21,0xf0,0x30,0xd6,0xda,0x72,0x4c,0x37,0xa7,0x73,0xa,0x12,
  0x34,0x3b,0xb2,0x5c,0xd1,0x48,0x26,0xfd,0xb5,0x18,0x36,0xe9,0x91,0x1a,0x68,0x86,
  0x6,0x48,0xd4,0x14,0x2c,0xc7,0xd2,0x12,0xe6,0x55,0xa,0xc4,0x48,0xe6,0x61,0xa7,
  0x4e,0xa0,0x14,0xa,0x14,0x69,0x43,0x8f,0xba,0xb3,0x4c,0x8e,0x63,0x26,0x16,0x48,
  0x3c,0xcf,0xf5,0x2a,0xc5,0x69,0x4,0xf1,0x82,0xde,0x28,0x2e,0x4c,0xf,0xd1,0xa5,
  0x19,0xe1,0x28,0xe7,0xcd,0x1c,0x97,0x9e,0xdd,0x3b,0xbc,0xb6,0x33,0xfe,0xf5,0xf9,
  0xc1,0xcd,0x9f,0xe9,0x54,0xdb,0x38,0xcf,0x3a,0xf8,0x72,0xcd,0x70,0xfd,0xa,0xc5,
  0x8e,0xf0,0x5d,0x81,0x49,0x24,0xd0,0x44,0x1d,0x1e,0xe7,0xc7,0xab,0xf0,0x16,0x10,
  0xa6,0x96,0xb1,0xc7,0x5,0x6c,0xef,0x88,0x64,0x38,0xed,0xea,0x37,0x8a,0x3f,0x53,
  0x1c,0x18,0xb,0x9a,0xfa,0xec,0xda,0xe0,0x2a,0x77,0xca,0xa9,0xd7,0xd8,0x46,0x24,
  0x6a,0x5c,0xb1,0x6d,0xf5,0x28,0xa5,0x75,0xfa,0x2c,0xf1,0x8a,0xcd,0x33,0x4c,0x7a,
  0xa0,0x72,0xa8,0xf3,0x33,0x86,0xa9,0xda,0x60,0x4c,0x63,0x48,0x89,0xa7,0x66,0xa6,
  0x33,0xb3,0x2,0x8b,0x76,0x51,0x0,0xdb,0x76,0x98,0x6e,0x7d,0x40,0xb0,0x4f,0x1a,
  0xd0,0xa8,0xcd,0xbe,0x95,0xfd,0x5a,0xda,0x7e,0xa7,0x38,0xb0,0x61,0xcd,0xf4,0x85,
  0xbe,0x8e,0x6d,0xb2,0x82,0xda,0x50,0x5a,0x6d,0x12,0x5d,0xdf,0xb8,0xd6,0xb6,0xbe,
  0x95,0x82,0xe8,0x84,0xae,0x34,0xa,0xd2,0xfb,0x44,0xbf,0x94,0x9,0x7a,0xa5,0x9,
  0xf5,0xd5,0x66,0x2e,0x7e,0xca,0x6a,0x19,0x62,0xc9,0xc4,0x2f,0x3f,0xcd,0xcc,0x2e,
  0x4a,0x7,0x2f,0xbe,0x18,0x5f,0xff,0x71,0xb2,0xfd,0xe9,0x1c,0x75,0xe9,0x7d,0x4a,
  0xde,0x37,0xd,0x2b,0x68,0x81,0xb3,0xa7,0xa9,0xdd,0xe1,0xd7,0xa,0x77,0xb0,0x50,
  0x95,0x2d,0x61,0x49,0x50,0x4b,0xd8,0x5c,0xb9,0x29,0x7,0x82,0x58,0x18,0x2d,0x73,
  0x41,0xef,0x13,0x9b,0xe8,0x21,0xad,0xb9,0x72,0xd0,0x2e,0x6b,0xa6,0xa1,0x99,0x31,
  0xc3,0x33,0x6b,0x54,0xd3,0xd5,0x47,0x3e,0x63,0x53,0xfc,0x43,0x6a,0x59,0xd9,0x84,
  0xd8,0x79,0xb1,0x34,0x77,0x14,0x84,0x14,0xc8,0xf,0xc8,0xac,0x7f,0xc2,0xb7,0x50,
  0xe7,0x9e,0xd9,0x87,0x31,0x93,0xdd,0x6f,0xc6,0x5b,0x3b,0x93,0x7,0xdb,0x73,0xb8,
  0xe7,0x9c,0xee,0xb9,0xb6,0x7d,0xa,0x7b,0x2a,0xd,0xd2,0xc0,0xa2,0x6c,0xaf,0x3d,
  0xb4,0xaa,0x2b,0xc6,0xeb,0x96,0x11,0xf4,0x73,0x75,0x3c,0xef,0x22,0xb9,0x98,0x52,
  0xe2,0x8a,0xcc,0x88,0x56,0xfb,0xd8,0x31,0x6c,0xd2,0xa4,0x42,0x2e,0x21,0xd6,0xa,
  0x60,0x72,0xb7,0x90,0xe6,0xe8,0x6,0x39,0xb5,0xa9,0x69,0x59,0x16,0x3b,0xa2,0xa2,
  0x95,0x9e,0xa7,0x14,0xe3,0x22,0xd7,0xa5,0x3b,0x50,0x6,0x30,0xb7,0xfd,0x91,0x96,
  0xbf,0x9d,0x6c,0x9d,0xea,0x4,0xe7,0x1b,0x17,0xf7,0x77,0x3f,0xd9,0xdb,0xfd,0x6b,
  0x8e,0x63,0x34,0x32,0x70,0x23,0xd2,0xd2,0x89,0x76,0x68,0xe3,0xcd,0xaa,0x2a,0x1a,
  0x62,0x84,0x6d,0x2e,0x4b,0x36,0xba,0xe5,0xe9,0x36,0x69,0x81,0x2c,0xbb,0xd4,0x1e,
  0x48,0x2,0x78,0xbc,0xb4,0xac,0x48,0x81,0xee,0x2a,0x2d,0x3b,0xf0,0x78,0x21,0x7,
  0x9,0x73,0x45,0xee,0xb4,0x21,0x64,0xdd,0xc6,0xc3,0xfc,0x79,0x4d,0x45,0xd9,0x63,
  0xa2,0x1a,0x4d,0x37,0xa9,0x38,0xa2,0x4b,0xf9,0xc6,0xc3,0xac,0x75,0x25,0x8e,0x4f,
  0xdb,0xa4,0x97,0x3b,0x9b,0xad,0xcf,0xb0,0x4d,0x79,0xa9,0xfa,0x88,0xab,0xee,0x70,
  0xb6,0x22,0x3f,0x33,0x27,0x53,0xd9,0x41,0x6c,0x5a,0xee,0xda,0x7a,0x1f,0x7,0x9c,
  0x36,0xd2,0x34,0x9b,0x70,0x23,0xff,0x28,0xc3,0x4d,0xed,0x41,0x68,0xb1,0xcc,0xc4,
  0x47,0xfb,0xf9,0xb2,0x5c,0x4a,0xd7,0x39,0xaf,0xc6,0x17,0x25,0xd7,0xf4,0xd7,0xd,
  0x3,0x6c,0x9b,0x21,0x97,0x1f,0x2b,0xb5,0x32,0x45,0xe3,0xb1,0x35,0x77,0xbe,0xea,
  0x16,0x5a,0xb,0xb6,0x0,0xcc,0x18,0x28,0x6a,0x8d,0x4c,0x2d,0x9d,0xdf,0xc8,0xc,
  0x1b,0xb9,0xd1,0xc8,0xa6,0xe,0x7b,0x6b,0x67,0xff,0xcb,0xdd,0xc9,0xbd,0x9b,0x93,
  0xef,0xbe,0x6d,0x4a,0xf,0xaf,0x5f,0x22,0x9b,0xa6,0x87,0x7,0xc4,0x7,0xc3,0x91,
  0xed,0xa7,0x47,0xed,0xf0,0x38,0xb8,0x52,0xd1,0x4a,0xad,0xae,0x81,0xab,0xd1,0x7d,
  0xa9,0x66,0x41,0x44,0xbe,0x64,0x4d,0x18,0xf4,0xf5,0xbb,0x84,0x3e,0x8a,0x24,0x72,
  0xd8,0xb1,0x6,0x38,0x4c,0x51,0x89,0xec,0xec,0x7f,0x35,0x51,0x89,0x8f,0xdb,0x47,
  0x8e,0x66,0x0,0x5a,0x15,0x1,0x1d,0xd2,0x2c,0x27,0xa2,0x16,0xdd,0x83,0xd1,0xdf,
  0xc4,0x6,0x79,0x3b,0xdd,0xc4,0xf4,0xdc,0x1,0x15,0xee,0xe,0xb1,0x6e,0x5,0x9b,
  0x61,0x56,0xad,0x39,0x31,0xe4,0xa3,0x23,0xc3,0x44,0xe5,0xc0,0x65,0x9f,0x41,0x75,
  0xcf,0xc0,0xcc,0x82,0x50,0x26,0xd5,0xab,0xac,0x79,0xa2,0xc,0x6c,0x4b,0x89,0xf2,
  0x54,0x73,0xaa,0x30,0xf8,0xf,0xbc,0xd7,0xbc,0x77,
  
};

static const unsigned char qt_resource_name[] = {
  // src
  0x0,0x3,
  0x0,0x0,0x7a,0x83,
  0x0,0x73,
  0x0,0x72,0x0,0x63,
    // ui
  0x0,0x2,
  0x0,0x0,0x7,0xb9,
  0x0,0x75,
  0x0,0x69,
    // styles
  0x0,0x6,
  0x7,0xac,0x2,0xc3,
  0x0,0x73,
  0x0,0x74,0x0,0x79,0x0,0x6c,0x0,0x65,0x0,0x73,
    // modern_style.qss
  0x0,0x10,
  0x9,0x1d,0x9b,0x63,
  0x0,0x6d,
  0x0,0x6f,0x0,0x64,0x0,0x65,0x0,0x72,0x0,0x6e,0x0,0x5f,0x0,0x73,0x0,0x74,0x0,0x79,0x0,0x6c,0x0,0x65,0x0,0x2e,0x0,0x71,0x0,0x73,0x0,0x73,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/src
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x2,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/src/ui
  0x0,0x0,0x0,0xc,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x3,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/src/ui/styles
  0x0,0x0,0x0,0x16,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x4,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/src/ui/styles/modern_style.qss
  0x0,0x0,0x0,0x28,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0xa9,0xd8,0x8d,0x98,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#ifdef QT_NAMESPACE
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZlib()
{
    extern const unsigned char qt_resourceFeatureZlib;
    return qt_resourceFeatureZlib;
}
#else
unsigned char qResourceFeatureZlib();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZlib());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)(); }
   } dummy;
}
