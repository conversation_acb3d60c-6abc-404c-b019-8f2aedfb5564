/****************************************************************************
** Meta object code from reading C++ file 'speechmanager.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.8)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../src/speech/speechmanager.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'speechmanager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.8. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_SpeechManager_t {
    QByteArrayData data[38];
    char stringdata0[575];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_SpeechManager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_SpeechManager_t qt_meta_stringdata_SpeechManager = {
    {
QT_MOC_LITERAL(0, 0, 13), // "SpeechManager"
QT_MOC_LITERAL(1, 14, 16), // "speechRecognized"
QT_MOC_LITERAL(2, 31, 0), // ""
QT_MOC_LITERAL(3, 32, 4), // "text"
QT_MOC_LITERAL(4, 37, 11), // "speechError"
QT_MOC_LITERAL(5, 49, 5), // "error"
QT_MOC_LITERAL(6, 55, 17), // "synthesisFinished"
QT_MOC_LITERAL(7, 73, 11), // "ttsFinished"
QT_MOC_LITERAL(8, 85, 19), // "ttsPlaybackFinished"
QT_MOC_LITERAL(9, 105, 16), // "wakeWordDetected"
QT_MOC_LITERAL(10, 122, 12), // "stateChanged"
QT_MOC_LITERAL(11, 135, 11), // "SpeechState"
QT_MOC_LITERAL(12, 147, 5), // "state"
QT_MOC_LITERAL(13, 153, 17), // "audioLevelChanged"
QT_MOC_LITERAL(14, 171, 5), // "level"
QT_MOC_LITERAL(15, 177, 14), // "startListening"
QT_MOC_LITERAL(16, 192, 13), // "stopListening"
QT_MOC_LITERAL(17, 206, 14), // "synthesizeText"
QT_MOC_LITERAL(18, 221, 9), // "setEngine"
QT_MOC_LITERAL(19, 231, 12), // "SpeechEngine"
QT_MOC_LITERAL(20, 244, 6), // "engine"
QT_MOC_LITERAL(21, 251, 9), // "setVolume"
QT_MOC_LITERAL(22, 261, 6), // "volume"
QT_MOC_LITERAL(23, 268, 8), // "setSpeed"
QT_MOC_LITERAL(24, 277, 5), // "speed"
QT_MOC_LITERAL(25, 283, 22), // "onAdvancedSpeechResult"
QT_MOC_LITERAL(26, 306, 21), // "onAdvancedSpeechError"
QT_MOC_LITERAL(27, 328, 21), // "onAdvancedTTSFinished"
QT_MOC_LITERAL(28, 350, 24), // "onBaiduRecognitionResult"
QT_MOC_LITERAL(29, 375, 23), // "onBaiduRecognitionError"
QT_MOC_LITERAL(30, 399, 32), // "onBaiduOfficialRecognitionResult"
QT_MOC_LITERAL(31, 432, 31), // "onBaiduOfficialRecognitionError"
QT_MOC_LITERAL(32, 464, 17), // "onBaiduTTSStarted"
QT_MOC_LITERAL(33, 482, 18), // "onBaiduTTSFinished"
QT_MOC_LITERAL(34, 501, 15), // "onBaiduTTSError"
QT_MOC_LITERAL(35, 517, 16), // "onAudioDataReady"
QT_MOC_LITERAL(36, 534, 16), // "updateAudioLevel"
QT_MOC_LITERAL(37, 551, 23) // "processVoiceRecognition"

    },
    "SpeechManager\0speechRecognized\0\0text\0"
    "speechError\0error\0synthesisFinished\0"
    "ttsFinished\0ttsPlaybackFinished\0"
    "wakeWordDetected\0stateChanged\0SpeechState\0"
    "state\0audioLevelChanged\0level\0"
    "startListening\0stopListening\0"
    "synthesizeText\0setEngine\0SpeechEngine\0"
    "engine\0setVolume\0volume\0setSpeed\0speed\0"
    "onAdvancedSpeechResult\0onAdvancedSpeechError\0"
    "onAdvancedTTSFinished\0onBaiduRecognitionResult\0"
    "onBaiduRecognitionError\0"
    "onBaiduOfficialRecognitionResult\0"
    "onBaiduOfficialRecognitionError\0"
    "onBaiduTTSStarted\0onBaiduTTSFinished\0"
    "onBaiduTTSError\0onAudioDataReady\0"
    "updateAudioLevel\0processVoiceRecognition"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_SpeechManager[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      27,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       8,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,  149,    2, 0x06 /* Public */,
       4,    1,  152,    2, 0x06 /* Public */,
       6,    0,  155,    2, 0x06 /* Public */,
       7,    0,  156,    2, 0x06 /* Public */,
       8,    0,  157,    2, 0x06 /* Public */,
       9,    0,  158,    2, 0x06 /* Public */,
      10,    1,  159,    2, 0x06 /* Public */,
      13,    1,  162,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      15,    0,  165,    2, 0x0a /* Public */,
      16,    0,  166,    2, 0x0a /* Public */,
      17,    1,  167,    2, 0x0a /* Public */,
      18,    1,  170,    2, 0x0a /* Public */,
      21,    1,  173,    2, 0x0a /* Public */,
      23,    1,  176,    2, 0x0a /* Public */,
      25,    1,  179,    2, 0x08 /* Private */,
      26,    1,  182,    2, 0x08 /* Private */,
      27,    0,  185,    2, 0x08 /* Private */,
      28,    1,  186,    2, 0x08 /* Private */,
      29,    1,  189,    2, 0x08 /* Private */,
      30,    1,  192,    2, 0x08 /* Private */,
      31,    1,  195,    2, 0x08 /* Private */,
      32,    0,  198,    2, 0x08 /* Private */,
      33,    0,  199,    2, 0x08 /* Private */,
      34,    1,  200,    2, 0x08 /* Private */,
      35,    0,  203,    2, 0x08 /* Private */,
      36,    0,  204,    2, 0x08 /* Private */,
      37,    0,  205,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 11,   12,
    QMetaType::Void, QMetaType::Double,   14,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, 0x80000000 | 19,   20,
    QMetaType::Void, QMetaType::Int,   22,
    QMetaType::Void, QMetaType::Int,   24,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void SpeechManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<SpeechManager *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->speechRecognized((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 1: _t->speechError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->synthesisFinished(); break;
        case 3: _t->ttsFinished(); break;
        case 4: _t->ttsPlaybackFinished(); break;
        case 5: _t->wakeWordDetected(); break;
        case 6: _t->stateChanged((*reinterpret_cast< SpeechState(*)>(_a[1]))); break;
        case 7: _t->audioLevelChanged((*reinterpret_cast< double(*)>(_a[1]))); break;
        case 8: _t->startListening(); break;
        case 9: _t->stopListening(); break;
        case 10: _t->synthesizeText((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 11: _t->setEngine((*reinterpret_cast< SpeechEngine(*)>(_a[1]))); break;
        case 12: _t->setVolume((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 13: _t->setSpeed((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 14: _t->onAdvancedSpeechResult((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 15: _t->onAdvancedSpeechError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 16: _t->onAdvancedTTSFinished(); break;
        case 17: _t->onBaiduRecognitionResult((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 18: _t->onBaiduRecognitionError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 19: _t->onBaiduOfficialRecognitionResult((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 20: _t->onBaiduOfficialRecognitionError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 21: _t->onBaiduTTSStarted(); break;
        case 22: _t->onBaiduTTSFinished(); break;
        case 23: _t->onBaiduTTSError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 24: _t->onAudioDataReady(); break;
        case 25: _t->updateAudioLevel(); break;
        case 26: _t->processVoiceRecognition(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (SpeechManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SpeechManager::speechRecognized)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (SpeechManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SpeechManager::speechError)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (SpeechManager::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SpeechManager::synthesisFinished)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (SpeechManager::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SpeechManager::ttsFinished)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (SpeechManager::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SpeechManager::ttsPlaybackFinished)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (SpeechManager::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SpeechManager::wakeWordDetected)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (SpeechManager::*)(SpeechState );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SpeechManager::stateChanged)) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (SpeechManager::*)(double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SpeechManager::audioLevelChanged)) {
                *result = 7;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject SpeechManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_SpeechManager.data,
    qt_meta_data_SpeechManager,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *SpeechManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SpeechManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_SpeechManager.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int SpeechManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 27)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 27;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 27)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 27;
    }
    return _id;
}

// SIGNAL 0
void SpeechManager::speechRecognized(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void SpeechManager::speechError(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void SpeechManager::synthesisFinished()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void SpeechManager::ttsFinished()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void SpeechManager::ttsPlaybackFinished()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void SpeechManager::wakeWordDetected()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void SpeechManager::stateChanged(SpeechState _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void SpeechManager::audioLevelChanged(double _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
