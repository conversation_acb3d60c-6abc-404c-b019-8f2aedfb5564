/****************************************************************************
** Meta object code from reading C++ file 'mainwindow.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.8)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../src/core/mainwindow.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'mainwindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.8. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_MainWindow_t {
    QByteArrayData data[127];
    char stringdata0[2115];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_MainWindow_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_MainWindow_t qt_meta_stringdata_MainWindow = {
    {
QT_MOC_LITERAL(0, 0, 10), // "MainWindow"
QT_MOC_LITERAL(1, 11, 18), // "onNavSpeechClicked"
QT_MOC_LITERAL(2, 30, 0), // ""
QT_MOC_LITERAL(3, 31, 19), // "onNavEmotionClicked"
QT_MOC_LITERAL(4, 51, 18), // "onNavSensorClicked"
QT_MOC_LITERAL(5, 70, 18), // "onNavSystemClicked"
QT_MOC_LITERAL(6, 89, 16), // "onStartListening"
QT_MOC_LITERAL(7, 106, 15), // "onStopListening"
QT_MOC_LITERAL(8, 122, 9), // "onTestTTS"
QT_MOC_LITERAL(9, 132, 14), // "onSpeechResult"
QT_MOC_LITERAL(10, 147, 4), // "text"
QT_MOC_LITERAL(11, 152, 13), // "onSpeechError"
QT_MOC_LITERAL(12, 166, 5), // "error"
QT_MOC_LITERAL(13, 172, 13), // "onTTSFinished"
QT_MOC_LITERAL(14, 186, 21), // "onTTSPlaybackFinished"
QT_MOC_LITERAL(15, 208, 18), // "onWakeWordDetected"
QT_MOC_LITERAL(16, 227, 19), // "onAudioLevelChanged"
QT_MOC_LITERAL(17, 247, 5), // "level"
QT_MOC_LITERAL(18, 253, 17), // "onSetHappyEmotion"
QT_MOC_LITERAL(19, 271, 15), // "onSetSadEmotion"
QT_MOC_LITERAL(20, 287, 17), // "onSetAngryEmotion"
QT_MOC_LITERAL(21, 305, 21), // "onSetSurprisedEmotion"
QT_MOC_LITERAL(22, 327, 16), // "onSetFearEmotion"
QT_MOC_LITERAL(23, 344, 19), // "onSetNeutralEmotion"
QT_MOC_LITERAL(24, 364, 22), // "onTestDeviceConnection"
QT_MOC_LITERAL(25, 387, 18), // "onReconnectDevices"
QT_MOC_LITERAL(26, 406, 31), // "onDeviceConnectionStatusChanged"
QT_MOC_LITERAL(27, 438, 7), // "leftEye"
QT_MOC_LITERAL(28, 446, 8), // "rightEye"
QT_MOC_LITERAL(29, 455, 18), // "onDeviceTestResult"
QT_MOC_LITERAL(30, 474, 11), // "deviceIndex"
QT_MOC_LITERAL(31, 486, 7), // "success"
QT_MOC_LITERAL(32, 494, 16), // "onEmotionChanged"
QT_MOC_LITERAL(33, 511, 7), // "emotion"
QT_MOC_LITERAL(34, 519, 9), // "intensity"
QT_MOC_LITERAL(35, 529, 19), // "onSensorDataUpdated"
QT_MOC_LITERAL(36, 549, 20), // "onTemperatureChanged"
QT_MOC_LITERAL(37, 570, 11), // "temperature"
QT_MOC_LITERAL(38, 582, 17), // "onHumidityChanged"
QT_MOC_LITERAL(39, 600, 8), // "humidity"
QT_MOC_LITERAL(40, 609, 14), // "onLightChanged"
QT_MOC_LITERAL(41, 624, 5), // "light"
QT_MOC_LITERAL(42, 630, 18), // "onCalibrateClicked"
QT_MOC_LITERAL(43, 649, 22), // "onRefreshSensorClicked"
QT_MOC_LITERAL(44, 672, 20), // "onWeatherDataUpdated"
QT_MOC_LITERAL(45, 693, 13), // "onCityChanged"
QT_MOC_LITERAL(46, 707, 19), // "onCitySliderChanged"
QT_MOC_LITERAL(47, 727, 5), // "value"
QT_MOC_LITERAL(48, 733, 14), // "onWeatherError"
QT_MOC_LITERAL(49, 748, 13), // "onCameraStart"
QT_MOC_LITERAL(50, 762, 12), // "onCameraStop"
QT_MOC_LITERAL(51, 775, 16), // "onCameraSnapshot"
QT_MOC_LITERAL(52, 792, 18), // "onCameraFrameReady"
QT_MOC_LITERAL(53, 811, 13), // "onCameraError"
QT_MOC_LITERAL(54, 825, 11), // "onReconnect"
QT_MOC_LITERAL(55, 837, 19), // "onDebugLevelChanged"
QT_MOC_LITERAL(56, 857, 5), // "index"
QT_MOC_LITERAL(57, 863, 25), // "onDeviceConnectionChanged"
QT_MOC_LITERAL(58, 889, 9), // "connected"
QT_MOC_LITERAL(59, 899, 17), // "onSettingsClicked"
QT_MOC_LITERAL(60, 917, 14), // "onStartHotspot"
QT_MOC_LITERAL(61, 932, 13), // "onStopHotspot"
QT_MOC_LITERAL(62, 946, 22), // "onHotspotConfigChanged"
QT_MOC_LITERAL(63, 969, 21), // "onHotspotStateChanged"
QT_MOC_LITERAL(64, 991, 9), // "isRunning"
QT_MOC_LITERAL(65, 1001, 24), // "onHotspotDeviceConnected"
QT_MOC_LITERAL(66, 1026, 27), // "onHotspotDeviceDisconnected"
QT_MOC_LITERAL(67, 1054, 10), // "macAddress"
QT_MOC_LITERAL(68, 1065, 14), // "onHotspotError"
QT_MOC_LITERAL(69, 1080, 14), // "onShowSettings"
QT_MOC_LITERAL(70, 1095, 18), // "onCalibrateDevices"
QT_MOC_LITERAL(71, 1114, 14), // "onTestDisplays"
QT_MOC_LITERAL(72, 1129, 11), // "onShowAbout"
QT_MOC_LITERAL(73, 1141, 19), // "updateStatusDisplay"
QT_MOC_LITERAL(74, 1161, 20), // "updateEmotionDisplay"
QT_MOC_LITERAL(75, 1182, 17), // "updateWaveDisplay"
QT_MOC_LITERAL(76, 1200, 28), // "on_testSpeakerButton_clicked"
QT_MOC_LITERAL(77, 1229, 31), // "on_testMicrophoneButton_clicked"
QT_MOC_LITERAL(78, 1261, 27), // "on_testCameraButton_clicked"
QT_MOC_LITERAL(79, 1289, 13), // "stopRecording"
QT_MOC_LITERAL(80, 1303, 13), // "onIdleTimeout"
QT_MOC_LITERAL(81, 1317, 11), // "onPlayMusic"
QT_MOC_LITERAL(82, 1329, 11), // "onStopMusic"
QT_MOC_LITERAL(83, 1341, 19), // "onMusicStateChanged"
QT_MOC_LITERAL(84, 1361, 22), // "onMusicPositionChanged"
QT_MOC_LITERAL(85, 1384, 8), // "position"
QT_MOC_LITERAL(86, 1393, 22), // "onMusicDurationChanged"
QT_MOC_LITERAL(87, 1416, 8), // "duration"
QT_MOC_LITERAL(88, 1425, 17), // "onLedStateChanged"
QT_MOC_LITERAL(89, 1443, 8), // "ledIndex"
QT_MOC_LITERAL(90, 1452, 4), // "isOn"
QT_MOC_LITERAL(91, 1457, 18), // "onLedControlResult"
QT_MOC_LITERAL(92, 1476, 7), // "message"
QT_MOC_LITERAL(93, 1484, 21), // "onAllLedsStateChanged"
QT_MOC_LITERAL(94, 1506, 5), // "allOn"
QT_MOC_LITERAL(95, 1512, 11), // "onToggleLed"
QT_MOC_LITERAL(96, 1524, 15), // "onTurnOnAllLeds"
QT_MOC_LITERAL(97, 1540, 16), // "onTurnOffAllLeds"
QT_MOC_LITERAL(98, 1557, 17), // "onHi3861Connected"
QT_MOC_LITERAL(99, 1575, 20), // "onHi3861Disconnected"
QT_MOC_LITERAL(100, 1596, 23), // "onHi3861ConnectionError"
QT_MOC_LITERAL(101, 1620, 19), // "onHi3861EmotionSent"
QT_MOC_LITERAL(102, 1640, 25), // "Hi3861Client::EmotionType"
QT_MOC_LITERAL(103, 1666, 26), // "onHi3861DeviceInfoReceived"
QT_MOC_LITERAL(104, 1693, 10), // "deviceType"
QT_MOC_LITERAL(105, 1704, 6), // "status"
QT_MOC_LITERAL(106, 1711, 16), // "onConnectLeftEye"
QT_MOC_LITERAL(107, 1728, 17), // "onConnectRightEye"
QT_MOC_LITERAL(108, 1746, 24), // "onShowKeyboardForLeftEye"
QT_MOC_LITERAL(109, 1771, 25), // "onShowKeyboardForRightEye"
QT_MOC_LITERAL(110, 1797, 24), // "handleLeavingHomeCommand"
QT_MOC_LITERAL(111, 1822, 23), // "handleComingHomeCommand"
QT_MOC_LITERAL(112, 1846, 21), // "generateWeatherAdvice"
QT_MOC_LITERAL(113, 1868, 27), // "WeatherManager::WeatherData"
QT_MOC_LITERAL(114, 1896, 7), // "weather"
QT_MOC_LITERAL(115, 1904, 23), // "generateTimeBasedAdvice"
QT_MOC_LITERAL(116, 1928, 24), // "generateHomeStatusReport"
QT_MOC_LITERAL(117, 1953, 22), // "generateTomorrowAdvice"
QT_MOC_LITERAL(118, 1976, 16), // "handleLedCommand"
QT_MOC_LITERAL(119, 1993, 16), // "toggleLedByVoice"
QT_MOC_LITERAL(120, 2010, 20), // "turnOnAllLedsByVoice"
QT_MOC_LITERAL(121, 2031, 21), // "turnOffAllLedsByVoice"
QT_MOC_LITERAL(122, 2053, 14), // "queryLedStatus"
QT_MOC_LITERAL(123, 2068, 13), // "keyPressEvent"
QT_MOC_LITERAL(124, 2082, 10), // "QKeyEvent*"
QT_MOC_LITERAL(125, 2093, 5), // "event"
QT_MOC_LITERAL(126, 2099, 15) // "keyReleaseEvent"

    },
    "MainWindow\0onNavSpeechClicked\0\0"
    "onNavEmotionClicked\0onNavSensorClicked\0"
    "onNavSystemClicked\0onStartListening\0"
    "onStopListening\0onTestTTS\0onSpeechResult\0"
    "text\0onSpeechError\0error\0onTTSFinished\0"
    "onTTSPlaybackFinished\0onWakeWordDetected\0"
    "onAudioLevelChanged\0level\0onSetHappyEmotion\0"
    "onSetSadEmotion\0onSetAngryEmotion\0"
    "onSetSurprisedEmotion\0onSetFearEmotion\0"
    "onSetNeutralEmotion\0onTestDeviceConnection\0"
    "onReconnectDevices\0onDeviceConnectionStatusChanged\0"
    "leftEye\0rightEye\0onDeviceTestResult\0"
    "deviceIndex\0success\0onEmotionChanged\0"
    "emotion\0intensity\0onSensorDataUpdated\0"
    "onTemperatureChanged\0temperature\0"
    "onHumidityChanged\0humidity\0onLightChanged\0"
    "light\0onCalibrateClicked\0"
    "onRefreshSensorClicked\0onWeatherDataUpdated\0"
    "onCityChanged\0onCitySliderChanged\0"
    "value\0onWeatherError\0onCameraStart\0"
    "onCameraStop\0onCameraSnapshot\0"
    "onCameraFrameReady\0onCameraError\0"
    "onReconnect\0onDebugLevelChanged\0index\0"
    "onDeviceConnectionChanged\0connected\0"
    "onSettingsClicked\0onStartHotspot\0"
    "onStopHotspot\0onHotspotConfigChanged\0"
    "onHotspotStateChanged\0isRunning\0"
    "onHotspotDeviceConnected\0"
    "onHotspotDeviceDisconnected\0macAddress\0"
    "onHotspotError\0onShowSettings\0"
    "onCalibrateDevices\0onTestDisplays\0"
    "onShowAbout\0updateStatusDisplay\0"
    "updateEmotionDisplay\0updateWaveDisplay\0"
    "on_testSpeakerButton_clicked\0"
    "on_testMicrophoneButton_clicked\0"
    "on_testCameraButton_clicked\0stopRecording\0"
    "onIdleTimeout\0onPlayMusic\0onStopMusic\0"
    "onMusicStateChanged\0onMusicPositionChanged\0"
    "position\0onMusicDurationChanged\0"
    "duration\0onLedStateChanged\0ledIndex\0"
    "isOn\0onLedControlResult\0message\0"
    "onAllLedsStateChanged\0allOn\0onToggleLed\0"
    "onTurnOnAllLeds\0onTurnOffAllLeds\0"
    "onHi3861Connected\0onHi3861Disconnected\0"
    "onHi3861ConnectionError\0onHi3861EmotionSent\0"
    "Hi3861Client::EmotionType\0"
    "onHi3861DeviceInfoReceived\0deviceType\0"
    "status\0onConnectLeftEye\0onConnectRightEye\0"
    "onShowKeyboardForLeftEye\0"
    "onShowKeyboardForRightEye\0"
    "handleLeavingHomeCommand\0"
    "handleComingHomeCommand\0generateWeatherAdvice\0"
    "WeatherManager::WeatherData\0weather\0"
    "generateTimeBasedAdvice\0"
    "generateHomeStatusReport\0"
    "generateTomorrowAdvice\0handleLedCommand\0"
    "toggleLedByVoice\0turnOnAllLedsByVoice\0"
    "turnOffAllLedsByVoice\0queryLedStatus\0"
    "keyPressEvent\0QKeyEvent*\0event\0"
    "keyReleaseEvent"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_MainWindow[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      96,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,  494,    2, 0x08 /* Private */,
       3,    0,  495,    2, 0x08 /* Private */,
       4,    0,  496,    2, 0x08 /* Private */,
       5,    0,  497,    2, 0x08 /* Private */,
       6,    0,  498,    2, 0x08 /* Private */,
       7,    0,  499,    2, 0x08 /* Private */,
       8,    0,  500,    2, 0x08 /* Private */,
       9,    1,  501,    2, 0x08 /* Private */,
      11,    1,  504,    2, 0x08 /* Private */,
      13,    0,  507,    2, 0x08 /* Private */,
      14,    0,  508,    2, 0x08 /* Private */,
      15,    0,  509,    2, 0x08 /* Private */,
      16,    1,  510,    2, 0x08 /* Private */,
      18,    0,  513,    2, 0x08 /* Private */,
      19,    0,  514,    2, 0x08 /* Private */,
      20,    0,  515,    2, 0x08 /* Private */,
      21,    0,  516,    2, 0x08 /* Private */,
      22,    0,  517,    2, 0x08 /* Private */,
      23,    0,  518,    2, 0x08 /* Private */,
      24,    0,  519,    2, 0x08 /* Private */,
      25,    0,  520,    2, 0x08 /* Private */,
      26,    2,  521,    2, 0x08 /* Private */,
      29,    2,  526,    2, 0x08 /* Private */,
      32,    2,  531,    2, 0x08 /* Private */,
      35,    0,  536,    2, 0x08 /* Private */,
      36,    1,  537,    2, 0x08 /* Private */,
      38,    1,  540,    2, 0x08 /* Private */,
      40,    1,  543,    2, 0x08 /* Private */,
      42,    0,  546,    2, 0x08 /* Private */,
      43,    0,  547,    2, 0x08 /* Private */,
      44,    0,  548,    2, 0x08 /* Private */,
      45,    0,  549,    2, 0x08 /* Private */,
      46,    1,  550,    2, 0x08 /* Private */,
      48,    1,  553,    2, 0x08 /* Private */,
      49,    0,  556,    2, 0x08 /* Private */,
      50,    0,  557,    2, 0x08 /* Private */,
      51,    0,  558,    2, 0x08 /* Private */,
      52,    0,  559,    2, 0x08 /* Private */,
      53,    1,  560,    2, 0x08 /* Private */,
      54,    0,  563,    2, 0x08 /* Private */,
      55,    1,  564,    2, 0x08 /* Private */,
      57,    1,  567,    2, 0x08 /* Private */,
      59,    0,  570,    2, 0x08 /* Private */,
      60,    0,  571,    2, 0x08 /* Private */,
      61,    0,  572,    2, 0x08 /* Private */,
      62,    0,  573,    2, 0x08 /* Private */,
      63,    1,  574,    2, 0x08 /* Private */,
      65,    0,  577,    2, 0x08 /* Private */,
      66,    1,  578,    2, 0x08 /* Private */,
      68,    1,  581,    2, 0x08 /* Private */,
      69,    0,  584,    2, 0x08 /* Private */,
      70,    0,  585,    2, 0x08 /* Private */,
      71,    0,  586,    2, 0x08 /* Private */,
      72,    0,  587,    2, 0x08 /* Private */,
      73,    0,  588,    2, 0x08 /* Private */,
      74,    0,  589,    2, 0x08 /* Private */,
      75,    0,  590,    2, 0x08 /* Private */,
      76,    0,  591,    2, 0x08 /* Private */,
      77,    0,  592,    2, 0x08 /* Private */,
      78,    0,  593,    2, 0x08 /* Private */,
      79,    0,  594,    2, 0x08 /* Private */,
      80,    0,  595,    2, 0x08 /* Private */,
      81,    0,  596,    2, 0x08 /* Private */,
      82,    0,  597,    2, 0x08 /* Private */,
      83,    0,  598,    2, 0x08 /* Private */,
      84,    1,  599,    2, 0x08 /* Private */,
      86,    1,  602,    2, 0x08 /* Private */,
      88,    2,  605,    2, 0x08 /* Private */,
      91,    2,  610,    2, 0x08 /* Private */,
      93,    1,  615,    2, 0x08 /* Private */,
      95,    0,  618,    2, 0x08 /* Private */,
      96,    0,  619,    2, 0x08 /* Private */,
      97,    0,  620,    2, 0x08 /* Private */,
      98,    0,  621,    2, 0x08 /* Private */,
      99,    0,  622,    2, 0x08 /* Private */,
     100,    1,  623,    2, 0x08 /* Private */,
     101,    2,  626,    2, 0x08 /* Private */,
     103,    2,  631,    2, 0x08 /* Private */,
     106,    0,  636,    2, 0x08 /* Private */,
     107,    0,  637,    2, 0x08 /* Private */,
     108,    0,  638,    2, 0x08 /* Private */,
     109,    0,  639,    2, 0x08 /* Private */,
     110,    1,  640,    2, 0x08 /* Private */,
     111,    1,  643,    2, 0x08 /* Private */,
     112,    1,  646,    2, 0x08 /* Private */,
     115,    0,  649,    2, 0x08 /* Private */,
     116,    0,  650,    2, 0x08 /* Private */,
     117,    0,  651,    2, 0x08 /* Private */,
     118,    1,  652,    2, 0x08 /* Private */,
     119,    1,  655,    2, 0x08 /* Private */,
     119,    0,  658,    2, 0x28 /* Private | MethodCloned */,
     120,    0,  659,    2, 0x08 /* Private */,
     121,    0,  660,    2, 0x08 /* Private */,
     122,    0,  661,    2, 0x08 /* Private */,
     123,    1,  662,    2, 0x08 /* Private */,
     126,    1,  665,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   10,
    QMetaType::Void, QMetaType::QString,   12,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Double,   17,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool, QMetaType::Bool,   27,   28,
    QMetaType::Void, QMetaType::Int, QMetaType::Bool,   30,   31,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   33,   34,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Double,   37,
    QMetaType::Void, QMetaType::Double,   39,
    QMetaType::Void, QMetaType::Int,   41,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   47,
    QMetaType::Void, QMetaType::QString,   12,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   12,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   56,
    QMetaType::Void, QMetaType::Bool,   58,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,   64,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   67,
    QMetaType::Void, QMetaType::QString,   12,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::LongLong,   85,
    QMetaType::Void, QMetaType::LongLong,   87,
    QMetaType::Void, QMetaType::Int, QMetaType::Bool,   89,   90,
    QMetaType::Void, QMetaType::Bool, QMetaType::QString,   31,   92,
    QMetaType::Void, QMetaType::Bool,   94,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   12,
    QMetaType::Void, 0x80000000 | 102, QMetaType::Int,   33,   34,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,  104,  105,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   10,
    QMetaType::Void, QMetaType::QString,   10,
    QMetaType::QString, 0x80000000 | 113,  114,
    QMetaType::QString,
    QMetaType::QString,
    QMetaType::QString,
    QMetaType::Void, QMetaType::QString,   10,
    QMetaType::Void, QMetaType::Int,   89,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 124,  125,
    QMetaType::Void, 0x80000000 | 124,  125,

       0        // eod
};

void MainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<MainWindow *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->onNavSpeechClicked(); break;
        case 1: _t->onNavEmotionClicked(); break;
        case 2: _t->onNavSensorClicked(); break;
        case 3: _t->onNavSystemClicked(); break;
        case 4: _t->onStartListening(); break;
        case 5: _t->onStopListening(); break;
        case 6: _t->onTestTTS(); break;
        case 7: _t->onSpeechResult((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 8: _t->onSpeechError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 9: _t->onTTSFinished(); break;
        case 10: _t->onTTSPlaybackFinished(); break;
        case 11: _t->onWakeWordDetected(); break;
        case 12: _t->onAudioLevelChanged((*reinterpret_cast< double(*)>(_a[1]))); break;
        case 13: _t->onSetHappyEmotion(); break;
        case 14: _t->onSetSadEmotion(); break;
        case 15: _t->onSetAngryEmotion(); break;
        case 16: _t->onSetSurprisedEmotion(); break;
        case 17: _t->onSetFearEmotion(); break;
        case 18: _t->onSetNeutralEmotion(); break;
        case 19: _t->onTestDeviceConnection(); break;
        case 20: _t->onReconnectDevices(); break;
        case 21: _t->onDeviceConnectionStatusChanged((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 22: _t->onDeviceTestResult((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 23: _t->onEmotionChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 24: _t->onSensorDataUpdated(); break;
        case 25: _t->onTemperatureChanged((*reinterpret_cast< double(*)>(_a[1]))); break;
        case 26: _t->onHumidityChanged((*reinterpret_cast< double(*)>(_a[1]))); break;
        case 27: _t->onLightChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 28: _t->onCalibrateClicked(); break;
        case 29: _t->onRefreshSensorClicked(); break;
        case 30: _t->onWeatherDataUpdated(); break;
        case 31: _t->onCityChanged(); break;
        case 32: _t->onCitySliderChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 33: _t->onWeatherError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 34: _t->onCameraStart(); break;
        case 35: _t->onCameraStop(); break;
        case 36: _t->onCameraSnapshot(); break;
        case 37: _t->onCameraFrameReady(); break;
        case 38: _t->onCameraError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 39: _t->onReconnect(); break;
        case 40: _t->onDebugLevelChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 41: _t->onDeviceConnectionChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 42: _t->onSettingsClicked(); break;
        case 43: _t->onStartHotspot(); break;
        case 44: _t->onStopHotspot(); break;
        case 45: _t->onHotspotConfigChanged(); break;
        case 46: _t->onHotspotStateChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 47: _t->onHotspotDeviceConnected(); break;
        case 48: _t->onHotspotDeviceDisconnected((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 49: _t->onHotspotError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 50: _t->onShowSettings(); break;
        case 51: _t->onCalibrateDevices(); break;
        case 52: _t->onTestDisplays(); break;
        case 53: _t->onShowAbout(); break;
        case 54: _t->updateStatusDisplay(); break;
        case 55: _t->updateEmotionDisplay(); break;
        case 56: _t->updateWaveDisplay(); break;
        case 57: _t->on_testSpeakerButton_clicked(); break;
        case 58: _t->on_testMicrophoneButton_clicked(); break;
        case 59: _t->on_testCameraButton_clicked(); break;
        case 60: _t->stopRecording(); break;
        case 61: _t->onIdleTimeout(); break;
        case 62: _t->onPlayMusic(); break;
        case 63: _t->onStopMusic(); break;
        case 64: _t->onMusicStateChanged(); break;
        case 65: _t->onMusicPositionChanged((*reinterpret_cast< qint64(*)>(_a[1]))); break;
        case 66: _t->onMusicDurationChanged((*reinterpret_cast< qint64(*)>(_a[1]))); break;
        case 67: _t->onLedStateChanged((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 68: _t->onLedControlResult((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 69: _t->onAllLedsStateChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 70: _t->onToggleLed(); break;
        case 71: _t->onTurnOnAllLeds(); break;
        case 72: _t->onTurnOffAllLeds(); break;
        case 73: _t->onHi3861Connected(); break;
        case 74: _t->onHi3861Disconnected(); break;
        case 75: _t->onHi3861ConnectionError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 76: _t->onHi3861EmotionSent((*reinterpret_cast< Hi3861Client::EmotionType(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 77: _t->onHi3861DeviceInfoReceived((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 78: _t->onConnectLeftEye(); break;
        case 79: _t->onConnectRightEye(); break;
        case 80: _t->onShowKeyboardForLeftEye(); break;
        case 81: _t->onShowKeyboardForRightEye(); break;
        case 82: _t->handleLeavingHomeCommand((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 83: _t->handleComingHomeCommand((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 84: { QString _r = _t->generateWeatherAdvice((*reinterpret_cast< const WeatherManager::WeatherData(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 85: { QString _r = _t->generateTimeBasedAdvice();
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 86: { QString _r = _t->generateHomeStatusReport();
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 87: { QString _r = _t->generateTomorrowAdvice();
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 88: _t->handleLedCommand((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 89: _t->toggleLedByVoice((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 90: _t->toggleLedByVoice(); break;
        case 91: _t->turnOnAllLedsByVoice(); break;
        case 92: _t->turnOffAllLedsByVoice(); break;
        case 93: _t->queryLedStatus(); break;
        case 94: _t->keyPressEvent((*reinterpret_cast< QKeyEvent*(*)>(_a[1]))); break;
        case 95: _t->keyReleaseEvent((*reinterpret_cast< QKeyEvent*(*)>(_a[1]))); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject MainWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_MainWindow.data,
    qt_meta_data_MainWindow,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *MainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_MainWindow.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int MainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 96)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 96;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 96)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 96;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
