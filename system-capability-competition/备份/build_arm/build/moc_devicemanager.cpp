/****************************************************************************
** Meta object code from reading C++ file 'devicemanager.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.8)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../src/core/devicemanager.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'devicemanager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.8. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_DeviceManager_t {
    QByteArrayData data[18];
    char stringdata0[239];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_DeviceManager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_DeviceManager_t qt_meta_stringdata_DeviceManager = {
    {
QT_MOC_LITERAL(0, 0, 13), // "DeviceManager"
QT_MOC_LITERAL(1, 14, 19), // "deviceStatusChanged"
QT_MOC_LITERAL(2, 34, 0), // ""
QT_MOC_LITERAL(3, 35, 10), // "DeviceType"
QT_MOC_LITERAL(4, 46, 4), // "type"
QT_MOC_LITERAL(5, 51, 12), // "DeviceStatus"
QT_MOC_LITERAL(6, 64, 6), // "status"
QT_MOC_LITERAL(7, 71, 23), // "deviceConnectionChanged"
QT_MOC_LITERAL(8, 95, 9), // "connected"
QT_MOC_LITERAL(9, 105, 15), // "allDevicesReady"
QT_MOC_LITERAL(10, 121, 11), // "deviceError"
QT_MOC_LITERAL(11, 133, 5), // "error"
QT_MOC_LITERAL(12, 139, 17), // "initializeDevices"
QT_MOC_LITERAL(13, 157, 17), // "checkDeviceStatus"
QT_MOC_LITERAL(14, 175, 15), // "reconnectDevice"
QT_MOC_LITERAL(15, 191, 16), // "reconnectDevices"
QT_MOC_LITERAL(16, 208, 11), // "resetDevice"
QT_MOC_LITERAL(17, 220, 18) // "performStatusCheck"

    },
    "DeviceManager\0deviceStatusChanged\0\0"
    "DeviceType\0type\0DeviceStatus\0status\0"
    "deviceConnectionChanged\0connected\0"
    "allDevicesReady\0deviceError\0error\0"
    "initializeDevices\0checkDeviceStatus\0"
    "reconnectDevice\0reconnectDevices\0"
    "resetDevice\0performStatusCheck"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_DeviceManager[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      10,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   64,    2, 0x06 /* Public */,
       7,    1,   69,    2, 0x06 /* Public */,
       9,    0,   72,    2, 0x06 /* Public */,
      10,    2,   73,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      12,    0,   78,    2, 0x0a /* Public */,
      13,    0,   79,    2, 0x0a /* Public */,
      14,    1,   80,    2, 0x0a /* Public */,
      15,    0,   83,    2, 0x0a /* Public */,
      16,    1,   84,    2, 0x0a /* Public */,
      17,    0,   87,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3, 0x80000000 | 5,    4,    6,
    QMetaType::Void, QMetaType::Bool,    8,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 3, QMetaType::QString,    4,   11,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void,

       0        // eod
};

void DeviceManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<DeviceManager *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->deviceStatusChanged((*reinterpret_cast< DeviceType(*)>(_a[1])),(*reinterpret_cast< DeviceStatus(*)>(_a[2]))); break;
        case 1: _t->deviceConnectionChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 2: _t->allDevicesReady(); break;
        case 3: _t->deviceError((*reinterpret_cast< DeviceType(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 4: _t->initializeDevices(); break;
        case 5: _t->checkDeviceStatus(); break;
        case 6: _t->reconnectDevice((*reinterpret_cast< DeviceType(*)>(_a[1]))); break;
        case 7: _t->reconnectDevices(); break;
        case 8: _t->resetDevice((*reinterpret_cast< DeviceType(*)>(_a[1]))); break;
        case 9: _t->performStatusCheck(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (DeviceManager::*)(DeviceType , DeviceStatus );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DeviceManager::deviceStatusChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (DeviceManager::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DeviceManager::deviceConnectionChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (DeviceManager::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DeviceManager::allDevicesReady)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (DeviceManager::*)(DeviceType , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&DeviceManager::deviceError)) {
                *result = 3;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject DeviceManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_DeviceManager.data,
    qt_meta_data_DeviceManager,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *DeviceManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *DeviceManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_DeviceManager.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int DeviceManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 10;
    }
    return _id;
}

// SIGNAL 0
void DeviceManager::deviceStatusChanged(DeviceType _t1, DeviceStatus _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void DeviceManager::deviceConnectionChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void DeviceManager::allDevicesReady()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void DeviceManager::deviceError(DeviceType _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
