# Qt交叉编译配置
QMAKE_CC = aarch64-buildroot-linux-gnu-gcc
QMAKE_CXX = aarch64-buildroot-linux-gnu-g++
QMAKE_AR = aarch64-buildroot-linux-gnu-ar cqs
QMAKE_OBJCOPY = aarch64-buildroot-linux-gnu-objcopy
QMAKE_NM = aarch64-buildroot-linux-gnu-nm -P
QMAKE_STRIP = aarch64-buildroot-linux-gnu-strip

QMAKE_CFLAGS += --sysroot=/opt/atk-dlrk3588-toolchain/aarch64-buildroot-linux-gnu/sysroot
QMAKE_CXXFLAGS += --sysroot=/opt/atk-dlrk3588-toolchain/aarch64-buildroot-linux-gnu/sysroot
QMAKE_LFLAGS += --sysroot=/opt/atk-dlrk3588-toolchain/aarch64-buildroot-linux-gnu/sysroot

# 目标平台
QMAKE_TARGET.arch = aarch64
target.path = /opt/qt_app
INSTALLS += target
