# Hi3861开发板Socket通信实现文档

## 项目概述

本文档描述了Hi3861开发板作为智能表情交互系统的左/右眼显示设备，通过Socket通信接收PC端控制命令并显示相应表情的实现方案。

### 系统架构
```
PC控制端(Qt应用) <--TCP Socket--> Hi3861开发板(左眼/右眼)
```

### 通信协议
- **传输协议**：TCP Socket
- **默认端口**：8889
- **数据格式**：ASCII字符串
- **网络**：局域网WiFi连接

## 功能需求

### 核心功能
1. **WiFi连接**：连接到指定WiFi网络并获取IP地址
2. **TCP服务器**：监听8889端口，接受PC端连接
3. **命令解析**：解析并执行PC端发送的表情控制命令
4. **表情显示**：根据命令控制LED/LCD显示相应表情

### 扩展功能
1. **设备发现**：响应PC端的设备扫描请求
2. **心跳机制**：定期响应PC端心跳包，维持连接
3. **状态上报**：向PC端报告设备运行状态

## 技术实现

### 1. WiFi网络连接

#### 初始化WiFi连接
```c
#include "hi_wifi_api.h"
#include "lwip/netifapi.h"

// WiFi配置结构
typedef struct {
    char ssid[32];          // WiFi名称
    char password[64];      // WiFi密码
    char device_name[16];   // 设备名称 "LEFT_EYE" 或 "RIGHT_EYE"
} wifi_config_t;

// WiFi连接函数
int wifi_connect(wifi_config_t* config) {
    hi_wifi_sta_config sta_config = {0};
    
    // 设置WiFi参数
    strcpy(sta_config.ssid, config->ssid);
    strcpy(sta_config.key, config->password);
    sta_config.auth_mode = HI_WIFI_SECURITY_WPA2PSK;
    
    // 启动WiFi连接
    hi_s32 ret = hi_wifi_sta_start(&sta_config);
    if (ret != HI_ERR_SUCCESS) {
        printf("WiFi连接失败: %d\n", ret);
        return -1;
    }
    
    // 等待获取IP地址
    hi_ip_info_s ip_info = {0};
    int retry_count = 0;
    while (retry_count < 30) {  // 最多等待30秒
        if (hi_wifi_sta_get_ip_info(&ip_info) == HI_ERR_SUCCESS) {
            printf("获取IP成功: %s\n", inet_ntoa(ip_info.ip_addr));
            return 0;
        }
        hi_sleep(1000);  // 等待1秒
        retry_count++;
    }
    
    printf("获取IP地址超时\n");
    return -1;
}
```

### 2. TCP Socket服务器

#### 创建TCP服务器
```c
#include "lwip/sockets.h"
#include "lwip/inet.h"

#define SERVER_PORT 8889
#define MAX_CLIENTS 1
#define BUFFER_SIZE 256

typedef struct {
    int server_fd;
    int client_fd;
    int is_connected;
    char device_type[16];  // "LEFT_EYE" 或 "RIGHT_EYE"
} socket_server_t;

// 创建TCP服务器
int create_tcp_server(socket_server_t* server) {
    struct sockaddr_in server_addr;
    int opt = 1;
    
    // 创建socket
    server->server_fd = socket(AF_INET, SOCK_STREAM, 0);
    if (server->server_fd < 0) {
        printf("创建socket失败\n");
        return -1;
    }
    
    // 设置socket选项
    setsockopt(server->server_fd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
    
    // 绑定地址和端口
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = INADDR_ANY;
    server_addr.sin_port = htons(SERVER_PORT);
    
    if (bind(server->server_fd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        printf("绑定端口失败\n");
        close(server->server_fd);
        return -1;
    }
    
    // 开始监听
    if (listen(server->server_fd, MAX_CLIENTS) < 0) {
        printf("监听失败\n");
        close(server->server_fd);
        return -1;
    }
    
    printf("TCP服务器启动成功，监听端口: %d\n", SERVER_PORT);
    return 0;
}

// 等待客户端连接
int wait_for_connection(socket_server_t* server) {
    struct sockaddr_in client_addr;
    socklen_t client_len = sizeof(client_addr);
    
    printf("等待PC端连接...\n");
    server->client_fd = accept(server->server_fd, (struct sockaddr*)&client_addr, &client_len);
    
    if (server->client_fd < 0) {
        printf("接受连接失败\n");
        return -1;
    }
    
    server->is_connected = 1;
    printf("PC端已连接: %s:%d\n", inet_ntoa(client_addr.sin_addr), ntohs(client_addr.sin_port));
    return 0;
}
```

### 3. 通信协议定义

#### 命令格式
```c
// 表情控制命令格式: "EMOTION:表情类型:强度值"
// 示例: "EMOTION:happy:100"
//       "EMOTION:sad:80"
//       "EMOTION:angry:90"

// 心跳命令: "HEARTBEAT"
// 响应: "HEARTBEAT_OK"

// 设备信息查询: "DEVICE_INFO"
// 响应: "DEVICE_INFO:LEFT_EYE:ONLINE" 或 "DEVICE_INFO:RIGHT_EYE:ONLINE"

typedef enum {
    EMOTION_NEUTRAL = 0,
    EMOTION_HAPPY,
    EMOTION_SAD,
    EMOTION_ANGRY,
    EMOTION_SURPRISED,
    EMOTION_FEAR,
    EMOTION_DISGUSTED
} emotion_type_t;

typedef struct {
    emotion_type_t type;
    int intensity;  // 0-100
} emotion_command_t;
```

#### 命令解析函数
```c
// 解析接收到的命令
int parse_command(const char* buffer, emotion_command_t* cmd) {
    char command[32], emotion[32];
    int intensity = 100;
    
    // 解析表情命令
    if (sscanf(buffer, "%31[^:]:%31[^:]:%d", command, emotion, &intensity) >= 2) {
        if (strcmp(command, "EMOTION") == 0) {
            // 转换表情类型
            if (strcmp(emotion, "happy") == 0) {
                cmd->type = EMOTION_HAPPY;
            } else if (strcmp(emotion, "sad") == 0) {
                cmd->type = EMOTION_SAD;
            } else if (strcmp(emotion, "angry") == 0) {
                cmd->type = EMOTION_ANGRY;
            } else if (strcmp(emotion, "surprised") == 0) {
                cmd->type = EMOTION_SURPRISED;
            } else if (strcmp(emotion, "fear") == 0) {
                cmd->type = EMOTION_FEAR;
            } else if (strcmp(emotion, "disgusted") == 0) {
                cmd->type = EMOTION_DISGUSTED;
            } else {
                cmd->type = EMOTION_NEUTRAL;
            }
            
            cmd->intensity = (intensity > 100) ? 100 : ((intensity < 0) ? 0 : intensity);
            return 1;  // 表情命令
        }
    }
    
    // 检查心跳命令
    if (strcmp(buffer, "HEARTBEAT") == 0) {
        return 2;  // 心跳命令
    }
    
    // 检查设备信息查询
    if (strcmp(buffer, "DEVICE_INFO") == 0) {
        return 3;  // 设备信息查询
    }
    
    return 0;  // 未知命令
}
```

### 4. 数据接收和处理

#### 主要消息处理循环
```c
// 处理接收到的数据
void handle_socket_communication(socket_server_t* server) {
    char buffer[BUFFER_SIZE];
    emotion_command_t emotion_cmd;
    
    while (server->is_connected) {
        // 接收数据
        int len = recv(server->client_fd, buffer, BUFFER_SIZE - 1, 0);
        
        if (len > 0) {
            buffer[len] = '\0';
            printf("收到命令: %s\n", buffer);
            
            // 解析命令
            int cmd_type = parse_command(buffer, &emotion_cmd);
            
            switch (cmd_type) {
                case 1:  // 表情命令
                    execute_emotion_command(&emotion_cmd);
                    send_response(server->client_fd, "EMOTION_OK");
                    break;
                    
                case 2:  // 心跳命令
                    send_response(server->client_fd, "HEARTBEAT_OK");
                    break;
                    
                case 3:  // 设备信息查询
                    send_device_info(server);
                    break;
                    
                default:
                    printf("未知命令: %s\n", buffer);
                    send_response(server->client_fd, "ERROR:UNKNOWN_COMMAND");
                    break;
            }
        } else if (len == 0) {
            // 连接断开
            printf("PC端断开连接\n");
            server->is_connected = 0;
            close(server->client_fd);
            break;
        } else {
            // 接收错误
            printf("接收数据错误\n");
            break;
        }
    }
}

// 发送响应
void send_response(int client_fd, const char* response) {
    send(client_fd, response, strlen(response), 0);
    printf("发送响应: %s\n", response);
}

// 发送设备信息
void send_device_info(socket_server_t* server) {
    char info[64];
    snprintf(info, sizeof(info), "DEVICE_INFO:%s:ONLINE", server->device_type);
    send_response(server->client_fd, info);
}
```

### 5. 表情显示控制

#### LED控制示例
```c
#include "hi_gpio.h"

// LED引脚定义（根据实际硬件调整）
#define LED_RED_PIN     HI_GPIO_IDX_9
#define LED_GREEN_PIN   HI_GPIO_IDX_10
#define LED_BLUE_PIN    HI_GPIO_IDX_11

// 初始化GPIO
void init_emotion_display() {
    hi_gpio_init();
    
    // 设置LED引脚为输出模式
    hi_gpio_set_dir(LED_RED_PIN, HI_GPIO_DIR_OUT);
    hi_gpio_set_dir(LED_GREEN_PIN, HI_GPIO_DIR_OUT);
    hi_gpio_set_dir(LED_BLUE_PIN, HI_GPIO_DIR_OUT);
    
    // 初始状态关闭所有LED
    hi_gpio_set_output_val(LED_RED_PIN, HI_GPIO_VALUE_0);
    hi_gpio_set_output_val(LED_GREEN_PIN, HI_GPIO_VALUE_0);
    hi_gpio_set_output_val(LED_BLUE_PIN, HI_GPIO_VALUE_0);
}

// 执行表情命令
void execute_emotion_command(emotion_command_t* cmd) {
    printf("执行表情: %d, 强度: %d\n", cmd->type, cmd->intensity);
    
    // 先关闭所有LED
    hi_gpio_set_output_val(LED_RED_PIN, HI_GPIO_VALUE_0);
    hi_gpio_set_output_val(LED_GREEN_PIN, HI_GPIO_VALUE_0);
    hi_gpio_set_output_val(LED_BLUE_PIN, HI_GPIO_VALUE_0);
    
    switch (cmd->type) {
        case EMOTION_HAPPY:
            // 开心 - 绿色LED
            hi_gpio_set_output_val(LED_GREEN_PIN, HI_GPIO_VALUE_1);
            break;
            
        case EMOTION_SAD:
            // 伤心 - 蓝色LED
            hi_gpio_set_output_val(LED_BLUE_PIN, HI_GPIO_VALUE_1);
            break;
            
        case EMOTION_ANGRY:
            // 愤怒 - 红色LED
            hi_gpio_set_output_val(LED_RED_PIN, HI_GPIO_VALUE_1);
            break;
            
        case EMOTION_SURPRISED:
            // 惊讶 - 红色+绿色LED（黄色）
            hi_gpio_set_output_val(LED_RED_PIN, HI_GPIO_VALUE_1);
            hi_gpio_set_output_val(LED_GREEN_PIN, HI_GPIO_VALUE_1);
            break;
            
        case EMOTION_FEAR:
            // 恐惧 - 闪烁红色LED
            for (int i = 0; i < 5; i++) {
                hi_gpio_set_output_val(LED_RED_PIN, HI_GPIO_VALUE_1);
                hi_sleep(200);
                hi_gpio_set_output_val(LED_RED_PIN, HI_GPIO_VALUE_0);
                hi_sleep(200);
            }
            break;
            
        case EMOTION_NEUTRAL:
        default:
            // 中性 - 所有LED关闭
            break;
    }
}
```

### 6. 设备发现功能（可选）

#### UDP广播响应
```c
#define DISCOVERY_PORT 8888

// 创建UDP服务器用于设备发现
int create_discovery_server(socket_server_t* server) {
    int udp_fd;
    struct sockaddr_in udp_addr;
    
    udp_fd = socket(AF_INET, SOCK_DGRAM, 0);
    if (udp_fd < 0) {
        printf("创建UDP socket失败\n");
        return -1;
    }
    
    memset(&udp_addr, 0, sizeof(udp_addr));
    udp_addr.sin_family = AF_INET;
    udp_addr.sin_addr.s_addr = INADDR_ANY;
    udp_addr.sin_port = htons(DISCOVERY_PORT);
    
    if (bind(udp_fd, (struct sockaddr*)&udp_addr, sizeof(udp_addr)) < 0) {
        printf("UDP绑定失败\n");
        close(udp_fd);
        return -1;
    }
    
    printf("设备发现服务启动，监听端口: %d\n", DISCOVERY_PORT);
    return udp_fd;
}

// 处理设备发现请求
void handle_discovery_request(int udp_fd, socket_server_t* server) {
    char buffer[128];
    struct sockaddr_in client_addr;
    socklen_t addr_len = sizeof(client_addr);
    
    int len = recvfrom(udp_fd, buffer, sizeof(buffer) - 1, 0, 
                       (struct sockaddr*)&client_addr, &addr_len);
    
    if (len > 0) {
        buffer[len] = '\0';
        printf("收到发现请求: %s\n", buffer);
        
        if (strcmp(buffer, "DISCOVER_DEVICES") == 0) {
            // 响应设备信息
            char response[64];
            snprintf(response, sizeof(response), "EYE_DEVICE:%s", server->device_type);
            
            sendto(udp_fd, response, strlen(response), 0, 
                   (struct sockaddr*)&client_addr, addr_len);
            
            printf("发送发现响应: %s\n", response);
        }
    }
}
```

### 7. 主程序框架

#### 完整的main函数
```c
#include "hi_task.h"

// 全局变量
static socket_server_t g_server;
static wifi_config_t g_wifi_config = {
    .ssid = "YOUR_WIFI_SSID",        // 修改为实际WiFi名称
    .password = "YOUR_WIFI_PASSWORD", // 修改为实际WiFi密码
    .device_name = "LEFT_EYE"        // 或 "RIGHT_EYE"
};

// 主任务函数
void* main_task(void* param) {
    printf("启动Hi3861表情显示设备\n");
    printf("设备类型: %s\n", g_wifi_config.device_name);
    
    // 初始化设备类型
    strcpy(g_server.device_type, g_wifi_config.device_name);
    g_server.is_connected = 0;
    
    // 初始化表情显示
    init_emotion_display();
    
    // 连接WiFi
    if (wifi_connect(&g_wifi_config) != 0) {
        printf("WiFi连接失败，退出程序\n");
        return NULL;
    }
    
    // 创建TCP服务器
    if (create_tcp_server(&g_server) != 0) {
        printf("创建TCP服务器失败，退出程序\n");
        return NULL;
    }
    
    // 主循环
    while (1) {
        // 等待PC端连接
        if (wait_for_connection(&g_server) == 0) {
            // 处理通信
            handle_socket_communication(&g_server);
        }
        
        // 连接断开后等待重新连接
        printf("等待重新连接...\n");
        hi_sleep(1000);
    }
    
    return NULL;
}

// 程序入口
void app_main(void) {
    hi_u32 ret;
    hi_task_attr attr = {0};
    
    // 设置任务属性
    attr.stack_size = 4096;
    attr.task_prio = 25;
    attr.task_name = "main_task";
    
    // 创建主任务
    ret = hi_task_create(&attr, main_task, NULL);
    if (ret != HI_ERR_SUCCESS) {
        printf("创建主任务失败: %d\n", ret);
    }
}
```

## 配置说明

### 网络配置
- **WiFi SSID**：修改 `g_wifi_config.ssid` 为实际WiFi网络名称
- **WiFi密码**：修改 `g_wifi_config.password` 为实际WiFi密码
- **设备类型**：修改 `g_wifi_config.device_name` 为 "LEFT_EYE" 或 "RIGHT_EYE"

### 硬件配置
- **LED引脚**：根据实际硬件连接修改 `LED_RED_PIN`、`LED_GREEN_PIN`、`LED_BLUE_PIN`
- **端口号**：默认使用8889端口，如需修改请同时更新PC端配置

### 编译配置
在 `build.gn` 文件中添加网络相关库：
```gn
deps = [
    "//base/hiviewdfx/hilog_lite/frameworks/featured:hilog_shared",
    "//foundation/communication/wifi_lite/interfaces/wifiservice:wifiservice",
    "//third_party/lwip:lwip",
]
```

## 测试验证

### 功能测试步骤
1. **网络连接测试**：确认设备能正常连接WiFi并获取IP地址
2. **Socket连接测试**：使用PC端程序连接到设备
3. **命令响应测试**：发送表情命令验证LED显示效果
4. **心跳测试**：验证心跳机制工作正常
5. **断线重连测试**：测试网络断开后的重连功能

### 调试信息
程序运行时会输出详细的调试信息，包括：
- WiFi连接状态和IP地址
- TCP服务器启动状态
- 客户端连接信息
- 接收到的命令和响应
- 表情执行状态

## 注意事项

1. **内存管理**：注意及时释放不再使用的socket资源
2. **错误处理**：添加完善的错误处理和恢复机制
3. **线程安全**：如使用多线程，注意共享资源的同步
4. **功耗优化**：考虑在无连接时进入低功耗模式
5. **安全性**：可以添加简单的认证机制防止未授权连接

## 扩展功能建议

1. **OTA升级**：支持通过网络更新固件
2. **配置管理**：支持通过网络修改WiFi配置
3. **日志记录**：记录运行日志用于故障排查
4. **多表情支持**：扩展更多表情类型和显示效果
5. **状态指示**：添加设备状态LED指示

---

**文档版本**：v1.0  
**创建日期**：2024年6月26日  
**适用平台**：Hi3861开发板  
**依赖库**：WiFi库、LwIP网络库 