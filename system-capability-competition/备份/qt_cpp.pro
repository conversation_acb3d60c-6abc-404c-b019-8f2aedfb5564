QT += core widgets network multimedia multimediawidgets

CONFIG += c++17

TARGET = qt_cpp
TEMPLATE = app

# 设置包含路径
INCLUDEPATH += \
    src/core \
    src/speech \
    src/emotion \
    src/sensor \
    src/hardware \
    src/ui \
    src/include \
    build/src/ui \
    ../aip-cpp-sdk-4.16.7 \
    ../aip-cpp-sdk-4.16.7/base

# 使用条件配置jsoncpp路径
contains(QMAKE_CC, aarch64-buildroot-linux-gnu-gcc) {
    # 交叉编译时使用sysroot中的jsoncpp
    INCLUDEPATH += /opt/atk-dlrk3588-toolchain/aarch64-buildroot-linux-gnu/sysroot/usr/include/jsoncpp
} else {
    # 本地编译时使用系统jsoncpp
    INCLUDEPATH += /usr/include/jsoncpp
}

# 核心模块源文件
SOURCES += \
    src/core/main.cpp \
    src/core/mainwindow.cpp \
    src/core/devicemanager.cpp \
    src/core/chatdatabase.cpp

# UI模块源文件
SOURCES += \
    src/ui/stylemanager.cpp \
    src/ui/iconmanager.cpp

# 语音模块源文件
SOURCES += \
    src/speech/speechmanager.cpp \
    src/speech/advanced_speech_engine.cpp \
    src/speech/baidu_speech_client.cpp \
    src/speech/baidu_official_client.cpp \
    src/speech/baidu_tts_client.cpp

# 表情模块源文件
SOURCES += \
    src/emotion/emotioncontroller.cpp

# 传感器模块源文件
SOURCES += \
    src/sensor/sensormanager.cpp \
    src/sensor/weathermanager.cpp

# 硬件模块源文件
SOURCES += \
    src/hardware/networkcontroller.cpp \
    src/hardware/cameramanager.cpp \
    src/hardware/gpio_control.c \
    src/hardware/wifihotspotcontroller.cpp \
    src/hardware/ledcontroller.cpp \
    src/hardware/hi3861_client.cpp

# 核心模块头文件
HEADERS += \
    src/core/mainwindow.h \
    src/core/devicemanager.h \
    src/core/config.h \
    src/core/chatdatabase.h

# UI模块头文件
HEADERS += \
    src/ui/stylemanager.h \
    src/ui/iconmanager.h

# 语音模块头文件
HEADERS += \
    src/speech/speechmanager.h \
    src/speech/advanced_speech_engine.h \
    src/speech/baidu_speech_client.h \
    src/speech/baidu_official_client.h \
    src/speech/baidu_tts_client.h

# 表情模块头文件
HEADERS += \
    src/emotion/emotioncontroller.h

# 传感器模块头文件  
HEADERS += \
    src/sensor/sensormanager.h \
    src/sensor/weathermanager.h

# 硬件模块头文件
HEADERS += \
    src/hardware/networkcontroller.h \
    src/hardware/cameramanager.h \
    src/hardware/wifihotspotcontroller.h \
    src/hardware/ledcontroller.h \
    src/hardware/hi3861_client.h \
    src/include/rk3588_gpio_pins.h \
    src/include/hi3861_gpio_pins.h

# UI 文件
FORMS += \
    src/ui/mainwindow.ui

# 资源文件
RESOURCES += \
    resources.qrc

# 只使用百度API，不依赖科大讯飞SDK
message("使用百度API，无科大讯飞依赖")
    DEFINES += NO_XUNFEI_SDK

# 系统库
LIBS += -lasound -lrt -ldl -lpthread -lstdc++ -lcurl -ljsoncpp -lssl -lcrypto

# 构建输出目录
OBJECTS_DIR = build
MOC_DIR = build
UI_DIR = build/src/ui
RCC_DIR = build

# 部署配置
target.path = /opt/qt_cpp
INSTALLS += target

# 编译选项
QMAKE_CXXFLAGS += -std=c++17
QMAKE_CFLAGS += -std=c99

# 定义宏
DEFINES += QT_DEPRECATED_WARNINGS 