<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1850</width>
    <height>1080</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1850</width>
    <height>1080</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>1850</width>
    <height>1080</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>RK3588智能交互系统</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QMainWindow {
    background-color: #f5f5f5;
    color: #333;
}

/* 主要内容区域 */
QWidget#contentWidget {
    background-color: white;
}

/* 标题标签 */
QLabel.title-label {
    font-size: 42px;
    font-weight: bold;
    color: #2c3e50;
    padding: 10px;
    margin-bottom: 15px;
}

/* 通用标签 */
QLabel {
    color: #333;
    font-size: 36px;
    background-color: transparent;
    padding: 5px;
}

/* 通用按钮 */
QPushButton {
    background-color: #3498db;
    border: none;
    border-radius: 12px;
    padding: 20px 30px;
    color: white;
    font-weight: bold;
    font-size: 38px;
    min-height: 100px;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed {
    background-color: #1f639a;
}

QPushButton:disabled {
    background-color: #bdc3c7;
    color: #7f8c8d;
}

/* 左侧导航栏 */
QWidget#navWidget {
    background-color: #2c3e50;
    border-right: 2px solid #34495e;
    min-width: 200px;
    max-width: 200px;
}

/* 左侧导航按钮 */
#navWidget QPushButton {
    background-color: transparent;
    border: none;
    color: #ecf0f1;
    text-align: center;
    padding: 20px 10px;
    font-size: 24px;
    font-weight: bold;
    border-right: 6px solid transparent; /* 未选中时透明边框 */
    min-height: 80px;
}

#navWidget QPushButton:hover {
    background-color: #34495e;
}

#navWidget QPushButton:pressed {
    background-color: #3498db;
}

#navWidget QPushButton:checked {
    color: white;
    background-color: #3498db;
    border-right: 6px solid #e74c3c; /* 选中时高亮边框 */
}
</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="horizontalLayout_main">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QWidget" name="navWidget">
      <property name="minimumSize">
       <size>
        <width>200</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>200</width>
        <height>16777215</height>
       </size>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_nav">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QLabel" name="label_header_title">
         <property name="text">
          <string>🧠 RK3588\n智能交互系统</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="styleSheet">
          <string notr="true">QLabel {
    font-size: 32px;
    font-weight: 900;
    color: white;
    padding: 20px 10px;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #667eea, stop:0.3 #764ba2, stop:0.7 #f093fb, stop:1 #f5576c);
    border-bottom: 3px solid #e74c3c;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_nav_speech">
         <property name="text">
          <string>🎤\n语音</string>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
         <property name="checked">
          <bool>true</bool>
         </property>
         <property name="styleSheet">
          <string notr="true">/* Style is inherited from #navWidget QPushButton */</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_nav_emotion">
         <property name="text">
          <string>😊\n表情</string>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
         <property name="styleSheet">
          <string notr="true">/* Style is inherited from #navWidget QPushButton */</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_nav_sensor">
         <property name="text">
          <string>🌤️\n天气</string>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
         <property name="styleSheet">
          <string notr="true">/* Style is inherited from #navWidget QPushButton */</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_nav_camera">
         <property name="text">
          <string>📷\n摄像头</string>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
         <property name="styleSheet">
          <string notr="true">/* Style is inherited from #navWidget QPushButton */</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_nav_system">
         <property name="text">
          <string>⚙️\n系统</string>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
         <property name="styleSheet">
          <string notr="true">/* Style is inherited from #navWidget QPushButton */</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_nav">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <layout class="QVBoxLayout" name="verticalLayout_content">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QStackedWidget" name="stackedWidget_content">
        <property name="currentIndex">
         <number>0</number>
        </property>

      <widget class="QWidget" name="page_speech">
       <layout class="QVBoxLayout" name="verticalLayout_speech_page">
        <property name="spacing">
         <number>3</number>
        </property>
        <property name="leftMargin">
         <number>5</number>
        </property>
        <property name="topMargin">
         <number>15</number>
        </property>
        <property name="rightMargin">
         <number>5</number>
        </property>
        <property name="bottomMargin">
         <number>5</number>
        </property>

        <item>
         <widget class="QGroupBox" name="groupBox_music_player">
          <property name="title">
           <string>🎵 音乐播放器</string>
          </property>
          <property name="visible">
           <bool>false</bool>
          </property>
          <property name="styleSheet">
           <string notr="true">QGroupBox {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    border: 3px solid #74b9ff;
    border-radius: 15px;
    margin-top: 10px;
    padding-top: 10px;
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 5px 10px;
    background: white;
    border-radius: 5px;
}</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_music_player">
           <item>
            <widget class="QLabel" name="label_current_song">
             <property name="text">
              <string>🎵 当前播放: 暂无</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
             <property name="styleSheet">
              <string notr="true">QLabel {
    font-size: 18px;
    color: #2c3e50;
    padding: 10px;
    background: rgba(116, 185, 255, 0.1);
    border-radius: 10px;
}</string>
             </property>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_music_controls">
             <item>
              <widget class="QPushButton" name="pushButton_pause_music">
               <property name="text">
                <string>⏸️ 暂停</string>
               </property>
               <property name="minimumSize">
                <size>
                 <width>100</width>
                 <height>50</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #fdcb6e, stop:1 #e17055);
    border: none;
    border-radius: 25px;
    color: white;
    font-size: 16px;
    font-weight: bold;
}
QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e17055, stop:1 #d63031);
}</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_stop_music">
               <property name="text">
                <string>⏹️ 停止</string>
               </property>
               <property name="minimumSize">
                <size>
                 <width>100</width>
                 <height>50</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e17055, stop:1 #d63031);
    border: none;
    border-radius: 25px;
    color: white;
    font-size: 16px;
    font-weight: bold;
}
QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #d63031, stop:1 #c92a2a);
}</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QTextEdit" name="textEdit_conversation">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>900</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>1350</height>
           </size>
          </property>
          <property name="readOnly">
           <bool>true</bool>
          </property>
          <property name="styleSheet">
           <string notr="true">QTextEdit {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1e3c72, stop:1 #2a5298);
    border: 3px solid #3498db;
    border-radius: 20px;
    color: #ffffff;
    font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
    font-size: 24px;
    padding: 20px;
    selection-background-color: #e74c3c;
    selection-color: white;
    line-height: 1.6;
}

QTextEdit:focus {
    border-color: #e74c3c;
    box-shadow: 0 0 20px rgba(231, 76, 60, 0.5);
    outline: none;
}

QScrollBar:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #34495e, stop:1 #2c3e50);
    width: 16px;
    border-radius: 8px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #3498db, stop:1 #2980b9);
    border-radius: 8px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #e74c3c, stop:1 #c0392b);
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
    background: none;
}</string>
          </property>
         </widget>
        </item>

        <item>
         <widget class="QLabel" name="label_conversation_status">
          <property name="text">
           <string>状态: 空闲</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>50</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QLabel {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
    border: 3px solid #9b59b6;
    border-radius: 25px;
    padding: 15px 30px;
    font-size: 20px;
    font-weight: bold;
    color: white;
    box-shadow: 0 6px 20px rgba(155, 89, 182, 0.3);
}</string>
          </property>
         </widget>
        </item>

        <item>
         <spacer name="verticalSpacer_speech">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>5</height>
           </size>
          </property>
         </spacer>
        </item>

        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_voice_controls">
          <property name="spacing">
           <number>12</number>
          </property>
          <item>
           <spacer name="horizontalSpacer_left">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="pushButton_start_conversation">
            <property name="text">
             <string>🎤 开始说话</string>
            </property>
            <property name="minimumSize">
             <size>
              <width>360</width>
              <height>75</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #667eea, stop:1 #764ba2);
    border: 3px solid #9b59b6;
    border-radius: 37px;
    color: white;
    font-size: 40px;
    font-weight: bold;
    padding: 45px 60px;
    box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4);
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f093fb, stop:1 #f5576c);
    border-color: #e74c3c;
    transform: scale(1.05);
    box-shadow: 0 12px 35px rgba(231, 76, 60, 0.6);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #4facfe, stop:1 #00f2fe);
    border-color: #3498db;
    transform: scale(0.95);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.8);
}

QPushButton:disabled {
    background: linear-gradient(135deg, #bdc3c7, #95a5a6);
    border-color: #7f8c8d;
    color: #ecf0f1;
    box-shadow: none;
}</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_right">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton_stop_reply">
          <property name="text">
           <string>⏹️ 停止回复</string>
          </property>
          <property name="visible">
           <bool>false</bool>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>135</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e17055, stop:1 #d63031);
    border: none;
    border-radius: 66px;
    color: white;
    font-size: 33px;
    font-weight: bold;
    padding: 36px 48px;
    margin: 0px 60px;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #d63031, stop:1 #c92a2a);
    transform: scale(1.02);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #c92a2a, stop:1 #b71c1c);
    transform: scale(0.98);
}</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="page_emotion">
       <layout class="QVBoxLayout" name="verticalLayout_emotion_page">
        <property name="spacing">
         <number>3</number>
        </property>
        <property name="leftMargin">
         <number>5</number>
        </property>
        <property name="topMargin">
         <number>5</number>
        </property>
        <property name="rightMargin">
         <number>5</number>
        </property>
        <property name="bottomMargin">
         <number>5</number>
        </property>
        <item>
         <widget class="QLabel" name="label_emotion_title">
          <property name="text">
           <string>😊 表情控制</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
          <property name="styleSheet">
           <string notr="true">QLabel {
    font-size: 42px;
    font-weight: bold;
    color: white;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #667eea, stop:1 #764ba2);
    padding: 36px;
    border-radius: 24px;
    margin-bottom: 24px;
}</string>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QGridLayout" name="gridLayout_emotions">
          <property name="spacing">
           <number>20</number>
          </property>
          <item row="0" column="0">
           <widget class="QPushButton" name="pushButton_happy">
            <property name="text">
             <string>😊 开心</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton {
    background-color: #f39c12;
    border-color: #e67e22;
    font-size: 38px;
    min-height: 120px;
}
QPushButton:hover { background-color: #e67e22; }
QPushButton:pressed { background-color: #d35400; }</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QPushButton" name="pushButton_sad">
            <property name="text">
             <string>😢 伤心</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton {
    background-color: #3498db;
    border-color: #2980b9;
    font-size: 38px;
    min-height: 120px;
}
QPushButton:hover { background-color: #2980b9; }
QPushButton:pressed { background-color: #1f639a; }</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QPushButton" name="pushButton_angry">
            <property name="text">
             <string>😠 哭</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton {
    background-color: #e74c3c;
    border-color: #c0392b;
    font-size: 38px;
    min-height: 120px;
}
QPushButton:hover { background-color: #c0392b; }
QPushButton:pressed { background-color: #a93226; }</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QPushButton" name="pushButton_surprised">
            <property name="text">
             <string>😲 惊讶</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton {
    background-color: #9b59b6;
    border-color: #8e44ad;
    font-size: 38px;
    min-height: 120px;
}
QPushButton:hover { background-color: #8e44ad; }
QPushButton:pressed { background-color: #7d3c98; }</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QPushButton" name="pushButton_fear">
            <property name="text">
             <string>😰 恐惧</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton {
    background-color: #663399;
    border-color: #552288;
    font-size: 38px;
    min-height: 120px;
}
QPushButton:hover { background-color: #552288; }
QPushButton:pressed { background-color: #441177; }</string>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QPushButton" name="pushButton_neutral">
            <property name="text">
             <string>😐 中性</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton {
    background-color: #95a5a6;
    border-color: #7f8c8d;
    font-size: 38px;
    min-height: 120px;
}
QPushButton:hover { background-color: #7f8c8d; }
QPushButton:pressed { background-color: #6c7b7d; }</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>

        <item>
         <widget class="QLabel" name="label_current_emotion">
          <property name="text">
           <string>当前表情: 中性</string>
          </property>
          <property name="styleSheet">
           <string notr="true">QLabel {
    color: #2c3e50;
    font-weight: bold;
    font-size: 38px;
    padding: 20px;
    margin-top: 20px;
    background-color: #e9ecef;
    border-radius: 12px;
}</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_device_status">
          <property name="text">
           <string>🔍 检测设备连接状态...</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
          <property name="styleSheet">
           <string notr="true">QLabel {
    color: #f39c12;
    font-weight: bold;
    font-size: 32px;
    padding: 15px;
    margin-top: 10px;
    background-color: #fef9e7;
    border: 2px solid #f39c12;
    border-radius: 10px;
}</string>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_display_status">
          <property name="spacing">
           <number>20</number>
          </property>
          <item>
           <widget class="QFrame" name="frame_left_eye">
            <property name="minimumSize">
             <size>
              <width>200</width>
              <height>150</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>250</width>
              <height>180</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QFrame {
    background-color: #95a5a6;
    border: 3px solid #7f8c8d;
    border-radius: 15px;
    margin: 5px;
}</string>
            </property>
            <property name="frameShape">
             <enum>QFrame::Box</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_left_eye">
             <property name="spacing">
              <number>8</number>
             </property>
             <property name="leftMargin">
              <number>10</number>
             </property>
             <property name="topMargin">
              <number>10</number>
             </property>
             <property name="rightMargin">
              <number>10</number>
             </property>
             <property name="bottomMargin">
              <number>10</number>
             </property>
             <item>
              <widget class="QLabel" name="label_left_eye_title">
               <property name="text">
                <string>👁️ 左眼显示屏</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="styleSheet">
                <string notr="true">QLabel {
    color: #2c3e50;
    font-weight: bold;
    font-size: 28px;
    background-color: transparent;
    border: none;
    margin: 0px;
    padding: 5px;
}</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_left_eye_ip">
               <property name="text">
                <string>**************</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="styleSheet">
                <string notr="true">QLabel {
    color: #34495e;
    font-size: 22px;
    background-color: transparent;
    border: none;
    margin: 0px;
    padding: 3px;
}</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_left_eye_status">
               <property name="text">
                <string>❌ 未连接</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="styleSheet">
                <string notr="true">QLabel {
    color: #e74c3c;
    font-weight: bold;
    font-size: 24px;
    background-color: transparent;
    border: none;
    margin: 0px;
    padding: 5px;
}</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QFrame" name="frame_right_eye">
            <property name="minimumSize">
             <size>
              <width>200</width>
              <height>150</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>250</width>
              <height>180</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QFrame {
    background-color: #95a5a6;
    border: 3px solid #7f8c8d;
    border-radius: 15px;
    margin: 5px;
}</string>
            </property>
            <property name="frameShape">
             <enum>QFrame::Box</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_right_eye">
             <property name="spacing">
              <number>8</number>
             </property>
             <property name="leftMargin">
              <number>10</number>
             </property>
             <property name="topMargin">
              <number>10</number>
             </property>
             <property name="rightMargin">
              <number>10</number>
             </property>
             <property name="bottomMargin">
              <number>10</number>
             </property>
             <item>
              <widget class="QLabel" name="label_right_eye_title">
               <property name="text">
                <string>👁️ 右眼显示屏</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="styleSheet">
                <string notr="true">QLabel {
    color: #2c3e50;
    font-weight: bold;
    font-size: 28px;
    background-color: transparent;
    border: none;
    margin: 0px;
    padding: 5px;
}</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_right_eye_ip">
               <property name="text">
                <string>**************</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="styleSheet">
                <string notr="true">QLabel {
    color: #34495e;
    font-size: 22px;
    background-color: transparent;
    border: none;
    margin: 0px;
    padding: 3px;
}</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_right_eye_status">
               <property name="text">
                <string>❌ 未连接</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="styleSheet">
                <string notr="true">QLabel {
    color: #e74c3c;
    font-weight: bold;
    font-size: 24px;
    background-color: transparent;
    border: none;
    margin: 0px;
    padding: 5px;
}</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>


        <item>
         <layout class="QGridLayout" name="gridLayout_hi3861_config">
          <property name="spacing">
           <number>10</number>
          </property>
          <item row="0" column="0">
           <widget class="QLabel" name="label_left_eye_ip">
            <property name="text">
             <string>左眼IP:</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
}</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <layout class="QHBoxLayout" name="horizontalLayout_left_eye_ip">
            <property name="spacing">
             <number>5</number>
            </property>
            <item>
             <widget class="QLineEdit" name="lineEdit_left_eye_ip">
              <property name="text">
               <string>**************</string>
              </property>
              <property name="styleSheet">
               <string notr="true">QLineEdit {
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    padding: 8px 12px;
    background-color: white;
    font-size: 18px;
    color: #2c3e50;
    min-height: 40px;
}
QLineEdit:focus {
    border-color: #3498db;
}</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_keyboard_left">
              <property name="text">
               <string>⌨️</string>
              </property>
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>60</width>
                <height>50</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton {
    background-color: #95a5a6;
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 16px;
    font-weight: bold;
    padding: 5px;
}
QPushButton:hover {
    background-color: #7f8c8d;
}
QPushButton:pressed {
    background-color: #5d6d7e;
}</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item row="0" column="2">
           <widget class="QPushButton" name="pushButton_connect_left_eye">
            <property name="text">
             <string>🔌 连接左眼</string>
            </property>
            <property name="minimumSize">
             <size>
              <width>150</width>
              <height>60</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>200</width>
              <height>80</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton {
    background-color: #3498db;
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 20px;
    font-weight: bold;
    padding: 12px 20px;
    min-height: 60px;
}
QPushButton:hover {
    background-color: #2980b9;
    transform: scale(1.05);
}
QPushButton:pressed {
    background-color: #21618c;
    transform: scale(0.95);
}</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_right_eye_ip">
            <property name="text">
             <string>右眼IP:</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
}</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <layout class="QHBoxLayout" name="horizontalLayout_right_eye_ip">
            <property name="spacing">
             <number>5</number>
            </property>
            <item>
             <widget class="QLineEdit" name="lineEdit_right_eye_ip">
              <property name="text">
               <string>**************</string>
              </property>
              <property name="styleSheet">
               <string notr="true">QLineEdit {
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    padding: 8px 12px;
    background-color: white;
    font-size: 18px;
    color: #2c3e50;
    min-height: 40px;
}
QLineEdit:focus {
    border-color: #3498db;
}</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_keyboard_right">
              <property name="text">
               <string>⌨️</string>
              </property>
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>60</width>
                <height>50</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton {
    background-color: #95a5a6;
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 16px;
    font-weight: bold;
    padding: 5px;
}
QPushButton:hover {
    background-color: #7f8c8d;
}
QPushButton:pressed {
    background-color: #5d6d7e;
}</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item row="1" column="2">
           <widget class="QPushButton" name="pushButton_connect_right_eye">
            <property name="text">
             <string>🔌 连接右眼</string>
            </property>
            <property name="minimumSize">
             <size>
              <width>150</width>
              <height>60</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>200</width>
              <height>80</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton {
    background-color: #e74c3c;
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 20px;
    font-weight: bold;
    padding: 12px 20px;
    min-height: 60px;
}
QPushButton:hover {
    background-color: #c0392b;
    transform: scale(1.05);
}
QPushButton:pressed {
    background-color: #a93226;
    transform: scale(0.95);
}</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_hi3861_status">
          <item>
           <widget class="QLabel" name="label_left_eye_status">
            <property name="text">
             <string>左眼: 未连接</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    color: #7f8c8d;
    font-size: 16px;
    padding: 5px;
}</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_right_eye_status">
            <property name="text">
             <string>右眼: 未连接</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    color: #7f8c8d;
    font-size: 16px;
    padding: 5px;
}</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_custom_data">
          <property name="title">
           <string>📡 自定义数据发送</string>
          </property>
          <property name="styleSheet">
           <string notr="true">QGroupBox {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    border: 2px solid #3498db;
    border-radius: 10px;
    margin-top: 10px;
    padding-top: 15px;
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 20px;
    padding: 0 10px 0 10px;
}</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_custom_data">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_custom_input">
             <item>
              <widget class="QLineEdit" name="lineEdit_custom_data">
               <property name="placeholderText">
                <string>输入要发送的数据...</string>
               </property>
               <property name="styleSheet">
                <string notr="true">QLineEdit {
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 20px;
    background-color: white;
    color: #2c3e50;
    min-height: 40px;
}
QLineEdit:focus {
    border-color: #3498db;
}</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_send_to_left">
               <property name="text">
                <string>➡️ 左眼</string>
               </property>
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>60</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton {
    background-color: #27ae60;
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 18px;
    font-weight: bold;
    padding: 10px;
}
QPushButton:hover {
    background-color: #229954;
}
QPushButton:pressed {
    background-color: #1e8449;
}</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_send_to_right">
               <property name="text">
                <string>➡️ 右眼</string>
               </property>
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>60</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton {
    background-color: #e74c3c;
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 18px;
    font-weight: bold;
    padding: 10px;
}
QPushButton:hover {
    background-color: #c0392b;
}
QPushButton:pressed {
    background-color: #a93226;
}</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_send_to_both">
               <property name="text">
                <string>➡️ 双眼</string>
               </property>
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>60</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton {
    background-color: #9b59b6;
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 18px;
    font-weight: bold;
    padding: 10px;
}
QPushButton:hover {
    background-color: #8e44ad;
}
QPushButton:pressed {
    background-color: #7d3c98;
}</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QLabel" name="label_send_status">
             <property name="text">
              <string>💡 提示：输入任意文本数据，点击按钮发送到对应设备</string>
             </property>
             <property name="styleSheet">
              <string notr="true">QLabel {
    color: #7f8c8d;
    font-size: 16px;
    padding: 5px;
    font-style: italic;
}</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <spacer name="verticalSpacer_emotion">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="page_sensor">
       <layout class="QVBoxLayout" name="verticalLayout_sensor_page">
        <property name="spacing">
         <number>2</number>
        </property>
        <property name="leftMargin">
         <number>5</number>
        </property>
        <property name="topMargin">
         <number>3</number>
        </property>
        <property name="rightMargin">
         <number>5</number>
        </property>
        <property name="bottomMargin">
         <number>3</number>
        </property>
        <item>
         <widget class="QLabel" name="label_sensor_title">
          <property name="text">
           <string>🌤️ 天气信息</string>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QLabel {
    font-size: 42px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f0f0f0;
    border-radius: 15px;
}</string>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_city_select">
          <property name="spacing">
           <number>15</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QLabel" name="label_city">
            <property name="text">
             <string>城市:</string>
            </property>
            <property name="minimumSize">
             <size>
              <width>120</width>
              <height>0</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="comboBox_city">
            <property name="maxVisibleItems">
             <number>5</number>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>80</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>80</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QComboBox {
    border: 3px solid #bdc3c7;
    border-radius: 10px;
    padding: 12px 20px;
    background-color: white;
    font-size: 36px;
    color: #000000;
}
QComboBox:hover { border-color: #3498db; }
QComboBox::drop-down { border: none; }
QComboBox::down-arrow { image: none; }
QComboBox QAbstractItemView {
    font-size: 36px;
    color: #000000;
    background-color: white;
}</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_city_slider">
          <property name="spacing">
           <number>10</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QLabel" name="label_city_slider">
            <property name="text">
             <string>拖拽选择城市:</string>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    color: #7f8c8d;
    font-size: 32px;
}</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QSlider" name="horizontalSlider_city">
            <property name="minimum">
             <number>0</number>
            </property>
            <property name="maximum">
             <number>14</number>
            </property>
            <property name="value">
             <number>12</number>
            </property>
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>60</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>60</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QSlider::groove:horizontal {
    border: 1px solid #bdc3c7;
    height: 30px;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
        stop:0 #3498db, stop:0.2 #2ecc71, stop:0.4 #f39c12, 
        stop:0.6 #e74c3c, stop:0.8 #9b59b6, stop:1 #34495e);
    margin: 2px 0;
    border-radius: 15px;
}
QSlider::handle:horizontal {
    background: white;
    border: 4px solid #2980b9;
    width: 50px;
    height: 50px;
    margin: -15px 0;
    border-radius: 25px;
}</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_city_display">
            <property name="text">
             <string>当前: 青岛</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    color: #2c3e50;
    font-size: 36px;
    font-weight: bold;
    background-color: #ecf0f1;
    border: 1px solid #bdc3c7;
    border-radius: 10px;
    padding: 15px;
}</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QGridLayout" name="gridLayout_sensor_data">
          <property name="spacing">
           <number>15</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item row="0" column="0">
           <widget class="QLabel" name="label_weather_title">
            <property name="text">
             <string>天气:</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="label_weather">
            <property name="text">
             <string>晴</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    color: #f39c12;
    font-weight: bold;
}</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_temp_title">
            <property name="text">
             <string>温度:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="label_temperature">
            <property name="text">
             <string>22°C</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    color: #e74c3c;
    font-weight: bold;
}</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="label_humidity_title">
            <property name="text">
             <string>湿度:</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QLabel" name="label_humidity">
            <property name="text">
             <string>45%</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    color: #3498db;
    font-weight: bold;
}</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="label_wind_title">
            <property name="text">
             <string>风力:</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QLabel" name="label_wind">
            <property name="text">
             <string>3级 东南风</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    color: #27ae60;
    font-weight: bold;
}</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>

        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_sensor_buttons">
          <property name="spacing">
           <number>20</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QPushButton" name="pushButton_refresh_weather">
            <property name="text">
             <string>刷新天气</string>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>100</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>120</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">/* Inherits from global QPushButton style */</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QGroupBox" name="hardwareTestGroupBox">
          <property name="title">
           <string>硬件测试</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_hardware_test">
           <item>
            <widget class="QPushButton" name="testSpeakerButton">
             <property name="text">
              <string>测试喇叭</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="testMicrophoneButton">
             <property name="text">
              <string>测试麦克风</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="testCameraButton">
             <property name="text">
              <string>测试摄像头</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <spacer name="verticalSpacer_sensor">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>5</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="page_camera">
       <layout class="QVBoxLayout" name="verticalLayout_camera_page">
        <property name="spacing">
         <number>15</number>
        </property>
        <property name="leftMargin">
         <number>15</number>
        </property>
        <property name="topMargin">
         <number>15</number>
        </property>
        <property name="rightMargin">
         <number>15</number>
        </property>
        <property name="bottomMargin">
         <number>15</number>
        </property>
        <item>
         <widget class="QLabel" name="label_camera_title">
          <property name="text">
           <string>📷 摄像头画面</string>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QLabel {
    font-size: 42px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f0f0f0;
    border-radius: 15px;
}</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_camera_display">
          <property name="text">
           <string>摄像头画面显示区域</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
          <property name="minimumSize">
           <size>
            <width>960</width>
            <height>540</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QLabel {
    background-color: #1e1e1e;
    border: 4px solid #3498db;
    border-radius: 15px;
    color: #ecf0f1;
    font-size: 38px;
    font-weight: bold;
}</string>
          </property>
          <property name="scaledContents">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_camera_info">
          <property name="spacing">
           <number>20</number>
          </property>
          <item>
           <widget class="QLabel" name="label_camera_status">
            <property name="text">
             <string>状态: 离线</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    color: #e74c3c;
    font-size: 36px;
    font-weight: bold;
}</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_camera_resolution">
            <property name="text">
             <string>分辨率: 640x480</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    color: #7f8c8d;
    font-size: 36px;
}</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_camera_fps">
            <property name="text">
             <string>帧率: 0 FPS</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    color: #7f8c8d;
    font-size: 36px;
}</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_camera_buttons">
          <property name="spacing">
           <number>15</number>
          </property>
          <item>
           <widget class="QPushButton" name="pushButton_camera_start">
            <property name="text">
             <string>启动</string>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>120</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>120</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton { background-color: #27ae60; }
QPushButton:hover { background-color: #229954; }
QPushButton:pressed { background-color: #1e8449; }</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButton_camera_stop">
            <property name="text">
             <string>停止</string>
            </property>
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>120</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>120</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton { background-color: #e74c3c; }
QPushButton:hover { background-color: #c0392b; }
QPushButton:pressed { background-color: #a93226; }</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButton_camera_snapshot">
            <property name="text">
             <string>拍照</string>
            </property>
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>120</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>120</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton { background-color: #f39c12; }
QPushButton:hover { background-color: #e67e22; }
QPushButton:pressed { background-color: #d68910; }</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="verticalSpacer_camera">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>5</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
      <!-- 系统页面 -->
      <widget class="QWidget" name="page_system">
       <layout class="QVBoxLayout" name="verticalLayout_system_page">
        <property name="spacing">
         <number>3</number>
        </property>
        <property name="leftMargin">
         <number>5</number>
        </property>
        <property name="topMargin">
         <number>5</number>
        </property>
        <property name="rightMargin">
         <number>5</number>
        </property>
        <property name="bottomMargin">
         <number>5</number>
        </property>
        <item>
         <widget class="QLabel" name="label_system_title">
          <property name="text">
           <string>⚙️ 系统信息</string>
          </property>
          <property name="styleSheet">
           <string notr="true">QLabel {
    font-size: 42px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f0f0f0;
    border-radius: 15px;
}</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_system_status">
          <property name="text">
           <string>系统运行中 | 连接: 否</string>
          </property>
          <property name="styleSheet">
           <string notr="true">QLabel {
    color: #27ae60;
    font-weight: bold;
    font-size: 36px;
}</string>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_debug">
          <property name="spacing">
           <number>15</number>
          </property>
          <item>
           <widget class="QLabel" name="label_debug_mode">
            <property name="text">
             <string>调试:</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="comboBox_debug_level">
            <property name="styleSheet">
             <string notr="true">QComboBox {
    border: 3px solid #bdc3c7;
    border-radius: 10px;
    padding: 12px 20px;
    background-color: white;
    font-size: 36px;
    color: #2c3e50;
    min-height: 80px;
}
QComboBox:hover { border-color: #3498db; }
QComboBox::drop-down { border: none; }
QComboBox::down-arrow { image: none; }
QComboBox QAbstractItemView {
    font-size: 36px;
}</string>
            </property>
            <item>
             <property name="text">
              <string>信息</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>警告</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>错误</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>调试</string>
             </property>
            </item>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QTextEdit" name="textEdit_logs">
          <property name="readOnly">
           <bool>true</bool>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>400</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>600</height>
           </size>
          </property>
          <property name="html">
           <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Ubuntu'; font-size:8pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; color:#27ae60;&quot;&gt;[2025-06-12 10:42:54] 系统启动完成&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_system_buttons">
          <property name="spacing">
           <number>15</number>
          </property>
          <item>
           <widget class="QPushButton" name="pushButton_reconnect">
            <property name="text">
             <string>重连</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButton_settings">
            <property name="text">
             <string>设置</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>


        <item>
         <spacer name="verticalSpacer_system">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>10</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="page_system">
       <layout class="QVBoxLayout" name="verticalLayout_system_page">
        <item>
         <widget class="QLabel" name="label_system_title">
          <property name="text">
           <string>⚙️ 系统信息</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QTextEdit" name="textEdit_system_log">
          <property name="html">
           <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Ubuntu'; font-size:8pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; color:#27ae60;&quot;&gt;[2025-06-12 10:42:54] 系统启动完成&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_system_buttons">
          <property name="spacing">
           <number>15</number>
          </property>
          <item>
           <widget class="QPushButton" name="pushButton_reconnect">
            <property name="text">
             <string>重连</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButton_settings">
            <property name="text">
             <string>设置</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="verticalSpacer_system">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>10</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui> 