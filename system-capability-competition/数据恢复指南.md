# 🚨 误删文件紧急恢复指南

## ⚠️ 紧急措施 - 立即执行！

1. **立即停止使用电脑** - 不要保存、下载、安装任何东西
2. **不要重启系统** - 避免系统写入新数据
3. **准备外部存储设备** - U盘或移动硬盘用于保存恢复的文件

## 🚀 快速开始

```bash
# 运行恢复脚本（需要管理员权限）
sudo bash file_recovery.sh
```

## 🛠️ 手动恢复方法

### 方法1: PhotoRec（推荐）
最通用，支持所有文件系统和文件类型

```bash
# 安装工具
sudo apt install testdisk

# 启动PhotoRec
sudo photorec
```

**操作步骤：**
1. 选择目标磁盘
2. 选择分区或"Whole disk"
3. 选择文件系统类型（通常选"Other"）
4. 选择保存位置（必须是不同的磁盘！）
5. 等待扫描完成

### 方法2: TestDisk
用于恢复分区和文件

```bash
sudo testdisk
```

**操作步骤：**
1. 选择"No Log"
2. 选择目标磁盘
3. 选择分区表类型
4. 选择"Advanced" -> "Undelete"
5. 浏览并恢复文件

### 方法3: extundelete（仅ext3/ext4）
专门用于Linux ext文件系统

```bash
# 安装工具
sudo apt install extundelete

# 查看分区
sudo fdisk -l

# 卸载分区（重要！）
sudo umount /dev/sda1

# 恢复所有文件
sudo extundelete /dev/sda1 --restore-all

# 恢复特定文件
sudo extundelete /dev/sda1 --restore-file 文件路径
```

## 📊 恢复成功率参考

| 时间 | 成功率 | 说明 |
|------|--------|------|
| 立即（5分钟内） | 95%+ | 数据几乎完整 |
| 1小时内 | 80-90% | 大部分文件可恢复 |
| 6小时内 | 60-80% | 部分文件可能损坏 |
| 1天内 | 30-60% | 取决于磁盘活动 |
| 超过1天 | 10-30% | 恢复概率较低 |

## 🎯 不同文件系统的最佳工具

- **ext2/ext3/ext4** (Linux): extundelete → PhotoRec
- **NTFS** (Windows): PhotoRec → TestDisk
- **FAT32/exFAT**: PhotoRec
- **未知文件系统**: PhotoRec（通用性最强）

## ⚡ 紧急恢复命令

### 快速PhotoRec恢复
```bash
sudo photorec /d ~/恢复文件 /dev/sda1
```

### 快速extundelete恢复
```bash
sudo umount /dev/sda1
sudo extundelete /dev/sda1 --restore-all
```

## 🔍 查看磁盘信息

```bash
# 查看所有磁盘分区
sudo fdisk -l

# 查看文件系统类型
lsblk -f

# 查看挂载点
df -h
```

## 💡 重要提示

1. **恢复文件保存位置：** 绝对不要保存到原磁盘！
2. **卸载分区：** 使用extundelete前必须先卸载分区
3. **时间紧迫：** 恢复越早开始，成功率越高
4. **专业服务：** 如果是重要数据，考虑专业数据恢复服务

## 🆘 如果简单方法失败

1. **停止所有操作** - 避免进一步损坏
2. **创建磁盘镜像** - 使用dd命令备份整个磁盘
3. **寻求专业帮助** - 联系数据恢复专家
4. **考虑商业软件** - R-Studio、PhotoRec GUI版本等

## 📞 紧急联系方式

如果恢复脚本无法运行或需要帮助，请：
1. 检查是否有管理员权限
2. 确保网络连接正常
3. 尝试手动安装工具：`sudo apt install testdisk extundelete`

---

**记住：时间就是数据！立即行动！** 🏃‍♂️💨 