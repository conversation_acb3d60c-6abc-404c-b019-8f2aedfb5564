# RK3588智能表情交互系统

> 基于RK3588平台的智能语音交互与表情控制系统，集成百度AI语音识别、WS63硬件控制等功能

## 📋 项目概述

RK3588智能表情交互系统是一个基于Qt5开发的多功能智能交互平台，支持语音识别、语音合成、表情控制、传感器监测、硬件管理等功能。系统同时支持PC端开发调试和ARM嵌入式设备部署。

### 🎯 主要特性

- **🎤 智能语音交互**: 集成百度语音识别和TTS语音合成
- **😊 表情控制系统**: 支持多种情感表情显示和WS63设备控制
- **🌤️ 天气服务**: 集成和风天气API，获取实时天气信息
- **📱 硬件设备管理**: LED控制、摄像头管理等
- **🎵 多媒体支持**: 音乐播放、录音功能
- **💾 智能数据库**: 聊天记录存储和情感分析
- **🖥️ 现代化UI**: 基于Qt5的美观用户界面
- **⚡ 跨平台部署**: 支持PC(x86_64)和ARM(RK3588)平台

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Qt主程序     │    │   百度AI SDK    │    │   WS63设备群    │
│   (主控制器)    │◄──►│   (语音服务)    │    │   (表情显示)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  硬件控制模块   │    │   天气服务模块  │    │   网络通信      │
│ (LED/摄像头等)  │    │ (和风天气API)   │    │  (WiFi/Socket)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 项目结构

```
系统能力大赛/
├── aip-cpp-sdk-4.16.7/           # 百度AI SDK
├── qt_cpp/                       # 主项目目录
│   ├── src/                      # 源代码
│   │   ├── core/                 # 核心模块
│   │   │   ├── main.cpp          # 程序入口
│   │   │   ├── mainwindow.cpp    # 主窗口
│   │   │   ├── devicemanager.cpp # 设备管理
│   │   │   └── chatdatabase.cpp  # 数据库管理
│   │   ├── speech/               # 语音模块
│   │   │   ├── speechmanager.cpp # 语音管理
│   │   │   ├── baidu_speech_client.cpp # 百度语音客户端
│   │   │   └── baidu_tts_client.cpp    # 百度TTS客户端
│   │   ├── emotion/              # 表情模块
│   │   ├── sensor/               # 天气服务模块
│   │   ├── hardware/             # 硬件控制模块
│   │   └── ui/                   # 用户界面模块
│   ├── build_qt.sh               # PC端构建脚本
│   ├── build_arm_qt.sh           # ARM交叉编译脚本
│   ├── qt_cpp.pro                # Qt项目文件
│   └── Hi3861_Socket通信实现文档.md # WS63通信协议
└── README.md                     # 项目说明文档
```

## 🔧 环境依赖

### PC端开发环境

- **操作系统**: Ubuntu 18.04+ / Debian 10+
- **Qt版本**: Qt 5.12+
- **编译器**: GCC 7.0+ (支持C++17)
- **依赖库**:
  ```bash
  # Ubuntu/Debian
  sudo apt update
  sudo apt install -y qt5-qmake qtbase5-dev qtmultimedia5-dev \
                      libqt5multimedia5-plugins build-essential \
                      libjsoncpp-dev libcurl4-openssl-dev \
                      libssl-dev libasound2-dev
  ```

### ARM嵌入式环境 (RK3588)

- **交叉编译工具链**: ATK-DLRK3588 Toolchain
- **目标设备**: 正点原子RK3588开发板
- **系统**: Ubuntu 20.04 (ARM64)

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <项目地址>
cd 系统能力大赛
```

### 2. PC端编译运行

```bash
cd qt_cpp
chmod +x build_qt.sh
./build_qt.sh
```

编译成功后运行：
```bash
./qt_cpp_pc
```

### 3. ARM交叉编译

```bash
# 首先安装ATK-DLRK3588交叉编译工具链
sudo chmod +x /path/to/atk-dlrk3588-toolchain-*.run
sudo /path/to/atk-dlrk3588-toolchain-*.run

# 编译ARM版本
cd qt_cpp
chmod +x build_arm_qt.sh
./build_arm_qt.sh
```

### 4. 部署到RK3588设备

```bash
# 确保RK3588设备已连接并启用USB调试
adb devices

# 将编译好的程序传输到RK3588设备
adb push build_arm/deploy/qt_cpp_arm_deploy.tar.gz /tmp/

# 连接到设备并部署
adb shell
cd /tmp && tar -xzf qt_cpp_arm_deploy.tar.gz
cd deploy && chmod +x run_app.sh
./run_app.sh
```

## ⚙️ 配置说明

### 百度AI配置

在 `src/core/config.h` 中配置百度AI密钥：

```cpp
// 百度AI配置
#define BAIDU_APP_ID    "your_app_id"
#define BAIDU_API_KEY   "your_api_key"
#define BAIDU_SECRET_KEY "your_secret_key"
```

### 和风天气API配置

在 `src/sensor/weathermanager.cpp` 中配置和风天气API：

```cpp
// 和风天气API配置
m_apiKey = "your_qweather_api_key";
m_apiUrl = "https://your_domain.qweatherapi.com/v7/weather/now";
```

### WS63设备配置

1. 配置WS63开发板WiFi连接
2. 设置左右眼设备IP地址
3. 确保网络连通性

详细配置请参考 `Hi3861_Socket通信实现文档.md`

### 硬件引脚配置

- GPIO控制引脚定义在 `src/include/rk3588_gpio_pins.h`
- WS63引脚定义在 `src/include/hi3861_gpio_pins.h`

## 📱 功能模块

### 🎤 语音交互模块

- **语音识别**: 基于百度语音识别API
- **语音合成**: 百度TTS语音合成
- **唤醒词检测**: 支持自定义唤醒词
- **语音指令**: 支持表情控制、设备操作等语音指令

### 😊 表情控制模块

- **表情类型**: 开心、悲伤、愤怒、惊讶、恐惧、中性
- **表情强度**: 0-100可调节
- **设备控制**: 支持WS63左右眼设备
- **实时同步**: 表情状态实时同步显示

### 🌤️ 天气服务模块

- **天气数据获取**: 基于和风天气API获取实时天气信息
- **多城市支持**: 支持多个城市天气查询
- **数据可视化**: 天气信息图表显示
- **自动更新**: 定时自动更新天气数据

### 🔧 硬件控制模块

- **LED控制**: 支持多路LED开关控制
- **摄像头管理**: 实时预览、拍照功能
- **GPIO控制**: 通用GPIO接口控制

## 🖥️ 用户界面

系统采用现代化的深色主题界面，包含以下页面：

1. **语音交互页面**: 语音识别、TTS测试、音频波形显示
2. **表情控制页面**: 表情选择、强度调节、设备状态
3. **天气信息页面**: 天气数据显示、多城市切换
4. **系统管理页面**: 设备连接、系统设置、调试信息

## 🐛 故障排除

### 常见问题

1. **编译错误**:
   - 检查Qt版本是否符合要求
   - 确认所有依赖库已安装
   - 验证C++17编译器支持

2. **语音功能异常**:
   - 检查百度AI密钥配置
   - 确认网络连接正常
   - 验证音频设备权限

3. **WS63设备连接失败**:
   - 检查设备网络连接
   - 确认IP地址配置正确
   - 验证端口8889未被占用

4. **ARM交叉编译问题**:
   - 确认工具链安装路径正确
   - 检查环境变量设置
   - 验证sysroot配置

### 调试模式

启动程序时添加调试参数：
```bash
./qt_cpp_pc --debug
```

查看详细日志：
```bash
export QT_LOGGING_RULES="*.debug=true"
./qt_cpp_pc
```






## 🙏 致谢

- 感谢百度AI开放平台提供语音服务支持
- 感谢正点原子提供RK3588开发板和工具链
- 感谢Qt开源社区提供优秀的跨平台框架

---

*🌟 如果这个项目对您有帮助，请给我们一个Star！* 