#!/bin/bash

echo "🚨 文件恢复助手 - Linux Shift+Delete 文件恢复"
echo "============================================="
echo ""

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请以root权限运行此脚本："
    echo "   sudo bash file_recovery.sh"
    exit 1
fi

echo "⚠️  重要提醒："
echo "   1. 立即停止使用电脑，避免覆盖数据"
echo "   2. 不要在被删除文件的磁盘上保存恢复的文件"
echo "   3. 准备一个外部存储设备保存恢复的文件"
echo ""

# 安装必要的工具
echo "📦 正在安装恢复工具..."
apt update > /dev/null 2>&1
apt install -y testdisk extundelete > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 恢复工具安装成功"
else
    echo "❌ 工具安装失败，请检查网络连接"
    exit 1
fi

echo ""
echo "💾 可用磁盘分区："
echo "=================="
lsblk -f | grep -E "(ext[2-4]|ntfs|vfat)"

echo ""
echo "🔍 请选择恢复方法："
echo "1) PhotoRec - 按文件类型恢复（推荐，支持所有文件系统）"
echo "2) TestDisk - 恢复整个分区或文件"
echo "3) extundelete - ext3/ext4专用恢复工具"
echo "4) 显示更多信息"

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo ""
        echo "🔄 启动PhotoRec文件恢复..."
        echo "操作指南："
        echo "- 选择包含删除文件的磁盘"
        echo "- 选择文件系统类型"
        echo "- 选择恢复文件保存位置（必须是不同的磁盘！）"
        echo "- 等待扫描完成"
        echo ""
        read -p "按Enter启动PhotoRec..."
        photorec
        ;;
    2)
        echo ""
        echo "🔄 启动TestDisk文件/分区恢复..."
        echo "操作指南："
        echo "- 选择要恢复的磁盘"
        echo "- 选择分区表类型"
        echo "- 使用Advanced -> Undelete恢复文件"
        echo ""
        read -p "按Enter启动TestDisk..."
        testdisk
        ;;
    3)
        echo ""
        echo "📋 extundelete 使用方法："
        echo "1. 确定删除文件的分区（如：/dev/sda1）"
        echo "2. 卸载该分区： sudo umount /dev/sda1"
        echo "3. 恢复所有文件： sudo extundelete /dev/sda1 --restore-all"
        echo "4. 恢复特定文件： sudo extundelete /dev/sda1 --restore-file 文件路径"
        echo ""
        echo "恢复的文件将保存在当前目录的 RECOVERED_FILES 文件夹中"
        echo ""
        read -p "请手动执行上述命令，按Enter返回菜单..."
        bash $0
        ;;
    4)
        echo ""
        echo "📖 详细恢复指南："
        echo "=================="
        echo ""
        echo "🎯 文件系统类型判断："
        echo "- ext2/ext3/ext4: Linux原生文件系统，使用extundelete"
        echo "- NTFS: Windows文件系统，使用PhotoRec或TestDisk"
        echo "- FAT32: 通用文件系统，使用PhotoRec"
        echo ""
        echo "⏰ 恢复成功率："
        echo "- 刚删除：90%以上"
        echo "- 1小时内：70-90%"
        echo "- 1天内：30-70%"
        echo "- 超过1天：取决于磁盘使用情况"
        echo ""
        echo "💡 最佳实践："
        echo "- 立即停止使用目标磁盘"
        echo "- 使用外部存储保存恢复的文件"
        echo "- 按文件类型恢复（PhotoRec）通常最有效"
        echo ""
        read -p "按Enter返回主菜单..."
        bash $0
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "✅ 恢复完成！请检查恢复的文件"
echo "�� 建议立即备份重要文件到安全位置" 