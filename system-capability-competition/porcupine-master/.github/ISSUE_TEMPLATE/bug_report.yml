name: Bug report
description: Bugs in Porcupine
title: "Porcupine Issue: "
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        **Before** reporting an issue, make sure to read the [documentation](https://picovoice.ai/docs/porcupine/) and search [existing issues](https://github.com/Picovoice/porcupine/issues).
  - type: checkboxes
    id: check
    attributes:
      label: Have you checked the docs and existing issues?
      description: Make sure you have checked all of the below before submitting an issue
      options:
        - label: I have read all of the relevant Picovoice Porcupine docs
          required: true
        - label: I have searched the existing issues for Porcupine
          required: true
  - type: dropdown
    id: sdk
    attributes:
      label: SDK
      options:
        - Android
        - C
        - .NET
        - Flutter
        - Go
        - iOS
        - Java
        - MCU
        - Node.js
        - Python
        - React Native
        - React
        - Rust
        - Unity
        - Web
    validations:
      required: true
  - type: input
    id: package
    attributes:
      label: "Porcupine package version"
      placeholder: "1.0.0"
    validations:
      required: true
  - type: input
    id: framework
    attributes:
      label: "Framework version"
      placeholder: "Python 3.7, .NET Core 3.1, etc."
    validations:
      required: true
  - type: dropdown
    id: platform
    attributes:
      label: Platform
      options:
        - Android
        - iOS
        - Web (WASM)
        - Linux (x86_64)
        - macOS (x86_64, arm64)
        - Windows (x86_64, arm64)
        - Raspberry Pi
        - ARM Cortex-M
    validations:
      required: true
  - type: input
    id: os
    attributes:
      label: "OS/Browser version"
      placeholder: "macOS 11.0, Android 8.0, etc."
    validations:
      required: true
  - type: textarea
    id: description
    attributes:
      label: Describe the bug
      description: A clear and concise description of what the bug is.
    validations:
      required: true
  - type: textarea
    id: reproduction
    attributes:
      label: Steps To Reproduce
      description: Steps to reproduce the behavior.
      placeholder: |
        1.
        2.
        3.
    validations:
      required: true
  - type: textarea
    id: expectation
    attributes:
      label: Expected Behavior
      description: A concise description of what you expected to happen.
    validations:
      required: true
