name: Python Demos

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '.github/workflows/python-demos.yml'
      - 'demo/python/**'
      - '!demo/python/README.md'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '.github/workflows/python-demos.yml'
      - 'demo/python/**'
      - '!demo/python/README.md'

defaults:
  run:
    working-directory: demo/python

jobs:
  build-github-hosted:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ['3.9', '3.10', '3.11', '3.12', '3.13']

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Pre-build dependencies
      run: python -m pip install --upgrade pip

    - name: Install dependencies
      run: pip install -r requirements.txt

    - name: Test
      run: python porcupine_demo_file.py --access_key ${{secrets.PV_VALID_ACCESS_KEY}} --wav_path ../../resources/audio_samples/multiple_keywords.wav --keywords picovoice

  build-self-hosted:
    runs-on: ${{ matrix.machine }}

    strategy:
      matrix:
        machine: [rpi3-32, rpi3-64, rpi4-32, rpi4-64, rpi5-64, pv-windows-arm64]

    steps:
    - uses: actions/checkout@v3

    - name: Install dependencies
      run: pip3 install -r requirements.txt

    - name: Test
      run: python3 porcupine_demo_file.py --access_key ${{secrets.PV_VALID_ACCESS_KEY}} --wav_path ../../resources/audio_samples/multiple_keywords.wav --keywords picovoice
