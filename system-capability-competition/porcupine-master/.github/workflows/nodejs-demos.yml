name: Node.js Demos

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - 'demo/nodejs/**'
      - '!demo/nodejs/README.md'
      - 'lib/node/**'
      - '.github/workflows/nodejs-demos.yml'

  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - 'demo/nodejs/**'
      - '!demo/nodejs/README.md'
      - 'lib/node/**'
      - '.github/workflows/nodejs-demos.yml'

defaults:
  run:
    working-directory: demo/nodejs

jobs:
  build-github-hosted:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        node-version: [18.x, 20.x, 22.x]

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}

    - name: Install dependencies
      run: yarn install

    - name: Test
      run: yarn file -a ${{secrets.PV_VALID_ACCESS_KEY}} -i ../../resources/audio_samples/multiple_keywords.wav -k picovoice

  build-self-hosted:
    runs-on: ${{ matrix.machine }}

    strategy:
      matrix:
        machine: [rpi3-32, rpi3-64, rpi4-32, rpi4-64, rpi5-64, pv-windows-arm64]

    steps:
    - uses: actions/checkout@v3

    - name: Install dependencies
      run: yarn install

    - name: Test
      run: yarn file -a ${{secrets.PV_VALID_ACCESS_KEY}} -i ../../resources/audio_samples/multiple_keywords.wav -k picovoice
