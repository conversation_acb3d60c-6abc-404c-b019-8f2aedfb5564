name: Flutter Codestyle

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '**/*.dart'
      - '.github/workflows/flutter-codestyle.yml'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '**/*.dart'
      - '.github/workflows/flutter-codestyle.yml'

jobs:
  check-flutter-codestyle:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Install Flutter 3.22.2
      uses: subosito/flutter-action@v2
      with:
        flutter-version: 3.22.2

    - name: Run Binding Analyzer
      run: flutter analyze --no-fatal-infos --no-fatal-warnings
      working-directory: binding/flutter

    - name: Run Demo Analyzer
      run: flutter analyze --no-fatal-infos --no-fatal-warnings
      working-directory: demo/flutter
