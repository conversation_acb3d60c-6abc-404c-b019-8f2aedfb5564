name: C Demos

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '!demo/c/README.md'
      - '.github/workflows/c-demos.yml'
      - 'demo/c/**'
      - 'lib/common/**'
      - 'lib/linux/**'
      - 'lib/mac/**'
      - 'lib/raspberry-pi/**'
      - 'lib/windows/**'
      - 'resources/.test/**'
      - 'resources/audio_samples/**'
      - 'resources/keyword_files*/linux/**'
      - 'resources/keyword_files*/mac/**'
      - 'resources/keyword_files*/raspberry-pi/**'
      - 'resources/keyword_files*/windows/**'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '!demo/c/README.md'
      - '.github/workflows/c-demos.yml'
      - 'demo/c/**'
      - 'lib/common/**'
      - 'lib/linux/**'
      - 'lib/mac/**'
      - 'lib/raspberry-pi/**'
      - 'lib/windows/**'
      - 'resources/.test/**'
      - 'resources/audio_samples/**'
      - 'resources/keyword_files*/linux/**'
      - 'resources/keyword_files*/mac/**'
      - 'resources/keyword_files*/raspberry-pi/**'
      - 'resources/keyword_files*/windows/**'


defaults:
  run:
    working-directory: demo/c

jobs:
  build-micdemo-github-hosted:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        include:
          - os: ubuntu-latest
            pv_recorder_platform: "linux"
          - os: windows-latest
            pv_recorder_platform: "windows-amd64"
          - os: macos-latest
            pv_recorder_platform: "mac-arm64"

    steps:
    - uses: actions/checkout@v3
      with:
        submodules: recursive

    - name: Create build directory
      run: cmake -B ./build -DPV_RECORDER_PLATFORM="${{ matrix.pv_recorder_platform }}"

    - name: Build micdemo
      run: cmake --build ./build --target porcupine_demo_mic

  build-micdemo-self-hosted:
    runs-on: ${{ matrix.machine }}

    strategy:
      matrix:
        machine: [rpi3-32, rpi3-64, rpi4-32, rpi4-64, rpi5-64, pv-windows-arm64]
        include:
          - machine: rpi3-32
            make_file: "Unix Makefiles"
            pv_recorder_platform: "raspberry-pi3"
          - machine: rpi3-64
            make_file: "Unix Makefiles"
            pv_recorder_platform: "raspberry-pi3-64"
          - machine: rpi4-32
            make_file: "Unix Makefiles"
            pv_recorder_platform: "raspberry-pi4"
          - machine: rpi4-64
            make_file: "Unix Makefiles"
            pv_recorder_platform: "raspberry-pi4-64"
          - machine: rpi5-64
            make_file: "Unix Makefiles"
            pv_recorder_platform: "raspberry-pi5-64"
          - machine: pv-windows-arm64
            make_file: "MinGW Makefiles"
            pv_recorder_platform: "windows-arm64"

    steps:
    - uses: actions/checkout@v3
      with:
        submodules: recursive

    - name: Create build directory
      run: cmake -G "${{ matrix.make_file }}" -B ./build -DPV_RECORDER_PLATFORM="${{ matrix.pv_recorder_platform }}"

    - name: Build micdemo
      run: cmake --build ./build --target porcupine_demo_mic

  build-filedemo-github-hosted:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        include:
        - os: ubuntu-latest
          platform: linux
          arch: x86_64
          pv_recorder_platform: "linux"
        - os: windows-latest
          platform: windows
          arch: amd64
          pv_recorder_platform: "windows-amd64"
        - os: macos-latest
          platform: mac
          arch: arm64
          pv_recorder_platform: "mac-arm64"

    steps:
    - uses: actions/checkout@v3
      with:
        submodules: recursive

    - name: Set up Python '3.10'
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'

    - name: Create build directory
      run: cmake -B ./build -DPV_RECORDER_PLATFORM="${{ matrix.pv_recorder_platform }}"

    - name: Build filedemo
      run: cmake --build ./build --target porcupine_demo_file

    - name: Install dependencies
      run: pip install -r test/requirements.txt

    - name: Test
      run: python test/test_porcupine_c.py ${{secrets.PV_VALID_ACCESS_KEY}} ${{ matrix.platform }} ${{ matrix.arch }}

  build-filedemo-self-hosted:
    runs-on: ${{ matrix.machine }}

    strategy:
      matrix:
        machine: [rpi3-32, rpi3-64, rpi4-32, rpi4-64, rpi5-64, pv-windows-arm64]
        include:
        - machine: rpi3-32
          platform: raspberry-pi
          arch: cortex-a53
          make_file: "Unix Makefiles"
          pv_recorder_platform: "raspberry-pi3"
        - machine: rpi3-64
          platform: raspberry-pi
          arch: cortex-a53-aarch64
          make_file: "Unix Makefiles"
          pv_recorder_platform: "raspberry-pi3-64"
        - machine: rpi4-32
          platform: raspberry-pi
          arch: cortex-a72
          make_file: "Unix Makefiles"
          pv_recorder_platform: "raspberry-pi4"
        - machine: rpi4-64
          platform: raspberry-pi
          arch: cortex-a72-aarch64
          make_file: "Unix Makefiles"
          pv_recorder_platform: "raspberry-pi4-64"
        - machine: rpi5-64
          platform: raspberry-pi
          arch: cortex-a76-aarch64
          make_file: "Unix Makefiles"
          pv_recorder_platform: "raspberry-pi5-64"
        - machine: pv-windows-arm64
          platform: windows
          arch: arm64
          make_file: "MinGW Makefiles"
          pv_recorder_platform: "windows-arm64"

    steps:
    - uses: actions/checkout@v3
      with:
        submodules: recursive

    - name: Create build directory
      run: cmake -G "${{ matrix.make_file }}" -B ./build -DPV_RECORDER_PLATFORM="${{ matrix.pv_recorder_platform }}"

    - name: Build filedemo
      run: cmake --build ./build --target porcupine_demo_file

    - name: Install dependencies
      run: pip install -r test/requirements.txt

    - name: Test
      run: python3 test/test_porcupine_c.py ${{secrets.PV_VALID_ACCESS_KEY}} ${{ matrix.platform }} ${{ matrix.arch }}
