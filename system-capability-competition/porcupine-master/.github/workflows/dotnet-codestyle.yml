name: .NET Codestyle

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - 'binding/dotnet/**/*.cs'
      - 'demo/dotnet/**/*.cs'
      - 'demo/dotnet-vui/**/*.cs'
      - '.github/workflows/dotnet-codestyle.yml'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - 'binding/dotnet/**/*.cs'
      - 'demo/dotnet/**/*.cs'
      - 'demo/dotnet-vui/**/*.cs'
      - '.github/workflows/dotnet-codestyle.yml'

jobs:
  check-dotnet-codestyle:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up .NET 8.0
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 8.0.x

    - name: Run Binding Codestyle
      run: dotnet format --verify-no-changes
      working-directory: binding/dotnet

    - name: Run Demo Codestyle
      run: dotnet format --verify-no-changes
      working-directory: demo/dotnet

    - name: Run Demo-vui Codestyle
      run: dotnet format --verify-no-changes
      working-directory: demo/dotnet-vui
