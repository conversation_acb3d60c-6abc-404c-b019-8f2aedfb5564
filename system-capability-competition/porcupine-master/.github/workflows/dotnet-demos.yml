name: .NET Demos

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - 'demo/dotnet/**'
      - 'demo/dotnet-vui/**'
      - '!demo/dotnet/README.md'
      - '!demo/dotnet-vui/README.md'
      - '.github/workflows/dotnet-demos.yml'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - 'demo/dotnet/**'
      - 'demo/dotnet-vui/**'
      - '!demo/dotnet/README.md'
      - '!demo/dotnet-vui/README.md'
      - '.github/workflows/dotnet-demos.yml'

defaults:
  run:
    working-directory: demo/dotnet/PorcupineDemo

jobs:
  build-github-hosted:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]

    steps:
    - uses: actions/checkout@v3

    - name: Set up .NET 8.0
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 8.0.x

    - name: Package restore
      run: dotnet restore

    - name: Dotnet build micdemo
      run: dotnet build -c MicDemo.Release

    - name: Dotnet build filedemo
      run: dotnet build -c FileDemo.Release

    - name: Run Dotnet filedemo
      run: dotnet run -c FileDemo.Release -- --input_audio_path ../../../resources/audio_samples/multiple_keywords.wav --access_key ${{secrets.PV_VALID_ACCESS_KEY}} --keywords picovoice

  build-self-hosted:
    runs-on: ${{ matrix.machine }}

    strategy:
      matrix:
        machine: [rpi3-32, rpi3-64, rpi4-32, rpi4-64, rpi5-64, pv-windows-arm64]

    steps:
    - uses: actions/checkout@v3

    - name: Package restore
      run: dotnet restore

    - name: Dotnet build micdemo
      run: dotnet build -c MicDemo.Release

    - name: Dotnet build filedemo
      run: dotnet build -c FileDemo.Release

    - name: Run Dotnet filedemo
      run: dotnet run -c FileDemo.Release -- --input_audio_path ../../../resources/audio_samples/multiple_keywords.wav --access_key ${{secrets.PV_VALID_ACCESS_KEY}} --keywords picovoice

  build-vui-demo:
    runs-on: ${{ matrix.os }}
    defaults:
      run:
        working-directory: demo/dotnet-vui/AvaloniaVUI

    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]

    steps:
    - uses: actions/checkout@v3

    - name: Set up .NET 8.0
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 8.0.x

    - name: Package restore
      run: dotnet restore

    - name: Dotnet build
      run: dotnet build

