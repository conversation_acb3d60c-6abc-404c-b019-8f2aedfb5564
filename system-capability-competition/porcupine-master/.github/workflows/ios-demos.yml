name: iOS Demos

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - 'demo/ios/**'
      - '.github/workflows/ios-demos.yml'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - 'demo/ios/**'
      - '.github/workflows/ios-demos.yml'

jobs:
  build-foreground-app:
    runs-on: macos-latest
    defaults:
      run:
        working-directory: demo/ios/ForegroundApp

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Build English
        run: xcrun xcodebuild build
          -configuration Debug
          -project ForegroundApp.xcodeproj
          -sdk iphoneos
          -scheme _enDemo
          -derivedDataPath ddp
          CODE_SIGNING_ALLOWED=NO

      - name: Build Other Language
        run: xcrun xcodebuild build
          -configuration Debug
          -project ForegroundApp.xcodeproj
          -sdk iphoneos
          -scheme deDemo
          -derivedDataPath ddp
          CODE_SIGNING_ALLOWED=NO

  build-background-service:
    runs-on: macos-latest
    defaults:
      run:
        working-directory: demo/ios/BackgroundService

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Build
        run: xcrun xcodebuild build
          -configuration Debug
          -project BackgroundService.xcodeproj
          -sdk iphoneos
          -scheme BackgroundService
          -derivedDataPath ddp
          CODE_SIGNING_ALLOWED=NO
