name: React Demos

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - 'demo/react/**'
      - '!demo/react/README.md'
      - '.github/workflows/react-demos.yml'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - 'demo/react/**'
      - '!demo/react/README.md'
      - '.github/workflows/react-demos.yml'

defaults:
  run:
    working-directory: demo/react

jobs:
  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [ ubuntu-latest, windows-latest, macos-latest ]
        node-version: [ 16.x, 18.x, 20.x ]

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}

    - name: Pre-build dependencies
      run: npm install yarn

    - name: Install dependencies
      run: yarn install

    - name: Build English
      run: yarn build en

    - name: Build Other Language
      run: yarn build ko
