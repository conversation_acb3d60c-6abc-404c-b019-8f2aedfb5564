name: React Native
on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - 'binding/react-native/**'
      - '!binding/react-native/README.md'
      - 'lib/common/**'
      - 'lib/android/**'
      - 'lib/ios/**'
      - 'resources/.test/test_data.json'
      - 'resources/audio_samples/**'
      - 'resources/keyword_files/android/**'
      - 'resources/keyword_files/ios/**'
      - '.github/workflows/react-native.yml'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - 'binding/react-native/**'
      - '!binding/react-native/README.md'
      - 'lib/common/**'
      - 'lib/android/**'
      - 'lib/ios/**'
      - 'resources/.test/test_data.json'
      - 'resources/audio_samples/**'
      - 'resources/keyword_files/android/**'
      - 'resources/keyword_files/ios/**'
      - '.github/workflows/react-native.yml'

defaults:
  run:
    working-directory: binding/react-native

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.x, 18.x, 20.x]

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}

    - name: Pre-build dependencies
      run: npm install yarn

    - name: Install dependencies
      run: yarn install

    - name: Build
      run: yarn prepare
