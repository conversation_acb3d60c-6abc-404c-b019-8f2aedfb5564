name: Node.js Performance

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '.github/workflows/nodejs-perf.yml'
      - 'lib/node/**'
      - 'lib/common/**'
      - 'resources/keyword_files/linux/**'
      - 'resources/keyword_files/mac/**'
      - 'resources/keyword_files/raspberry-pi/**'
      - 'resources/keyword_files/windows/**'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '.github/workflows/nodejs-perf.yml'
      - 'lib/node/**'
      - 'lib/common/**'
      - 'resources/keyword_files/linux/**'
      - 'resources/keyword_files/mac/**'
      - 'resources/keyword_files/raspberry-pi/**'
      - 'resources/keyword_files/windows/**'

defaults:
  run:
    working-directory: binding/nodejs

jobs:
  performance-github-hosted:
    runs-on: ${{ matrix.os }}

    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        include:
        - os: ubuntu-latest
          performance_threshold_sec: 0.5
        - os: windows-latest
          performance_threshold_sec: 0.5
        - os: macos-latest
          performance_threshold_sec: 0.75

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js '18.x'
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'

    - name: Pre-build dependencies
      run: npm install yarn

    - name: Install dependencies
      run: yarn install

    - name: Test
      run: yarn test test/perf.test.ts --access_key=${{secrets.PV_VALID_ACCESS_KEY}} --num_test_iterations=100 --performance_threshold_sec=${{matrix.performance_threshold_sec}}

  performance-self-hosted:
    runs-on: ${{ matrix.machine }}

    strategy:
      fail-fast: false
      matrix:
        machine: [rpi3-32, rpi3-64, rpi4-32, rpi4-64, rpi5-64]
        include:
        - machine: rpi3-32
          num_test_iterations: 50
          performance_threshold_sec: 2.0
        - machine: rpi3-64
          num_test_iterations: 50
          performance_threshold_sec: 2.5
        - machine: rpi4-32
          num_test_iterations: 50
          performance_threshold_sec: 1.5
        - machine: rpi4-64
          num_test_iterations: 50
          performance_threshold_sec: 1.5
        - machine: rpi5-64
          num_test_iterations: 50
          performance_threshold_sec: 0.5

    steps:
    - uses: actions/checkout@v3

    - name: Pre-build dependencies
      run: npm install --global yarn

    - name: Install dependencies
      run: yarn install

    - name: Machine state before
      working-directory: resources/scripts
      run: bash machine-state.sh

    - name: Test
      run: yarn test test/perf.test.ts --access_key=${{secrets.PV_VALID_ACCESS_KEY}} --num_test_iterations=${{matrix.num_test_iterations}} --performance_threshold_sec=${{matrix.performance_threshold_sec}}

    - name: Machine state after
      working-directory: resources/scripts
      run: bash machine-state.sh

  performance-windows-arm64:
    runs-on: ${{ matrix.machine }}

    strategy:
      fail-fast: false
      matrix:
        machine: [pv-windows-arm64]
        include:
        - machine: pv-windows-arm64
          num_test_iterations: 50
          performance_threshold_sec: 0.5

    steps:
    - uses: actions/checkout@v3

    - name: Pre-build dependencies
      run: npm install --global yarn

    - name: Install dependencies
      run: yarn install

    - name: Test
      run: yarn test test/perf.test.ts --access_key=${{secrets.PV_VALID_ACCESS_KEY}} --num_test_iterations=${{matrix.num_test_iterations}} --performance_threshold_sec=${{matrix.performance_threshold_sec}}
