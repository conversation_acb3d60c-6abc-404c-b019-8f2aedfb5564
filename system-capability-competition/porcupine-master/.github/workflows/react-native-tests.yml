name: React Native Tests
on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '.github/workflows/react-native-tests.yml'
      - 'binding/react-native/**'
      - '!binding/react-native/README.md'
      - 'lib/common/**'
      - 'lib/android/**'
      - 'lib/ios/**'
      - 'resources/.test/test_data.json'
      - 'resources/audio_samples/**'
      - 'resources/keyword_files*/android/**'
      - 'resources/keyword_files*/ios/**'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '.github/workflows/react-native-tests.yml'
      - 'binding/react-native/**'
      - '!binding/react-native/README.md'
      - 'lib/common/**'
      - 'lib/android/**'
      - 'lib/ios/**'
      - 'resources/.test/test_data.json'
      - 'resources/audio_samples/**'
      - 'resources/keyword_files*/android/**'
      - 'resources/keyword_files*/ios/**'

defaults:
  run:
    working-directory: binding/react-native/test-app/PorcupineTestApp

jobs:
  test-android:
    name: Run tests on Android
    runs-on: pv-android

    steps:
    - uses: actions/checkout@v3
      with:
        submodules: 'recursive'

    - name: Pre-build dependencies
      run: |
        yarn install
        ./copy_test_resources.sh

    - name: Inject AppID
      run: sed -i 's:{TESTING_ACCESS_KEY_HERE}:${{secrets.PV_VALID_ACCESS_KEY}}:' Tests.ts

    - name: Build tests
      run: detox build --configuration android.att.release

    - name: Run tests
      run: detox test --configuration android.att.release

  test-ios:
    name: Run tests on iOS
    runs-on: pv-ios

    steps:
    - uses: actions/checkout@v3
      with:
        submodules: 'recursive'

    - name: Pre-build dependencies
      run: |
        yarn install
        ./copy_test_resources.sh

    - name: Cocoapods install
      working-directory: binding/react-native/test-app/PorcupineTestApp/ios
      run: pod install --repo-update

    - name: Inject AppID
      run: sed -i '.bak' 's:{TESTING_ACCESS_KEY_HERE}:${{secrets.PV_VALID_ACCESS_KEY}}:' Tests.ts

    - name: Build tests
      run: detox build --configuration ios.sim.release

    - name: Run tests
      run: detox test --configuration ios.sim.release
