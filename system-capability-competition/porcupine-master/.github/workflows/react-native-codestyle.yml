name: React Native Codestyle

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '**/react-native/*.js'
      - '**/react-native/*.ts'
      - '**/react-native/*.jsx'
      - '**/react-native/*.tsx'
      - '.github/workflows/react-native-codestyle.yml'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '**/react-native/*.js'
      - '**/react-native/*.ts'
      - '**/react-native/*.jsx'
      - '**/react-native/*.tsx'
      - '.github/workflows/react-native-codestyle.yml'

jobs:
  check-react-native-codestyle:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js LTS
      uses: actions/setup-node@v3
      with:
        node-version: lts/*

    - name: Run Binding Linter
      run: yarn && yarn lint
      working-directory: binding/react-native

    - name: Run Demo Linter
      run: yarn && yarn lint
      working-directory: demo/react-native
