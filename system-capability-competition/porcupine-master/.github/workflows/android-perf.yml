name: Android Performance

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '.github/workflows/android-perf.yml'
      - 'binding/android/PorcupineTestApp/**/build.gradle'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '.github/workflows/android-perf.yml'
      - 'binding/android/PorcupineTestApp/**/build.gradle'

defaults:
  run:
    working-directory: binding/android/PorcupineTestApp/

jobs:
  build:
    name: Run Android Speed Tests on BrowserStack
    runs-on: ubuntu-latest

    strategy:
      matrix:
        device: [ android-perf ]
        include:
        - device: android-perf
          performanceThresholdSec: 0.5

    steps:
    - uses: actions/checkout@v3

    - name: Installing Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'
    - run:
        pip3 install requests

    - name: set up JDK 11
      uses: actions/setup-java@v3
      with:
        java-version: '11'
        distribution: 'temurin'

    - name: Copy test_resources
      run: ./copy_test_resources.sh

    - name: Inject AccessKey
      run: echo pvTestingAccessKey="${{secrets.PV_VALID_ACCESS_KEY}}" >> local.properties

    - name: Inject Android keystore variables
      run: |
        echo storePassword="${{secrets.ANDROID_RELEASE_KEYSTORE_PASSWORD}}" >> local.properties
        echo keyPassword="${{secrets.ANDROID_RELEASE_KEYSTORE_PASSWORD}}" >> local.properties
        echo keyAlias=picovoice >> local.properties
        echo storeFile=../picovoice.jks >> local.properties

    - name: Setup Android keystore file
      run: echo "${{secrets.ANDROID_RELEASE_KEYSTORE_FILE_B64}}" | base64 -d > picovoice.jks

    - name: Inject Number of Iterations
      run: echo numTestIterations="100" >> local.properties

    - name: Inject Performance Threshold
      run: echo performanceThresholdSec="${{ matrix.performanceThresholdSec }}" >> local.properties

    - name: Grant execute permission for gradlew
      run: chmod +x gradlew

    - name: Build app
      run: ./gradlew assembleEnDebug

    - name: Build androidTest
      run: ./gradlew assembleEnDebugAndroidTest -DtestBuildType=perf

    - name: Run tests on BrowserStack
      run: python3 ../../../script/automation/browserstack.py
        --type espresso
        --username "${{secrets.BROWSERSTACK_USERNAME}}"
        --access_key "${{secrets.BROWSERSTACK_ACCESS_KEY}}"
        --project_name "Porcupine-Android-Performance"
        --devices "${{ matrix.device }}"
        --app_path "porcupine-test-app/build/outputs/apk/en/debug/porcupine-test-app-en-debug.apk"
        --test_path "porcupine-test-app/build/outputs/apk/androidTest/en/debug/porcupine-test-app-en-debug-androidTest.apk"
