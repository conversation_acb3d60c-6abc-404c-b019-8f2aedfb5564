name: Swift Codestyle

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '**/*.swift'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '**/*.swift'

jobs:
  check-switch-codestyle:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Check swift codestyle
      uses: norio-nomura/action-swiftlint@3.2.1
      with:
        args: lint --config resources/.lint/swift/.swiftlint.yml --strict
