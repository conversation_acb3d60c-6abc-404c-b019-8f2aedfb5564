name: Java Performance

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '.github/workflows/java-perf.yml'
      - 'binding/java/**/PorcupinePerformanceTest.java'
      - 'lib/common/**'
      - 'lib/java/**'
      - 'resources/keyword_files/linux/**'
      - 'resources/keyword_files/mac/**'
      - 'resources/keyword_files/raspberry-pi/**'
      - 'resources/keyword_files/windows/**'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '.github/workflows/java-perf.yml'
      - 'lib/common/**'
      - 'lib/java/**'
      - 'resources/keyword_files/linux/**'
      - 'resources/keyword_files/mac/**'
      - 'resources/keyword_files/raspberry-pi/**'
      - 'resources/keyword_files/windows/**'

defaults:
  run:
    working-directory: binding/java

jobs:
  perf-github-hosted:
    runs-on: ${{ matrix.os }}

    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        include:
        - os: ubuntu-latest
          performance_threshold_sec: 0.5
        - os: windows-latest
          performance_threshold_sec: 0.5
        - os: macos-latest
          performance_threshold_sec: 0.5

    steps:
    - uses: actions/checkout@v3

    - name: Set up JDK 11
      uses: actions/setup-java@v3
      with:
        java-version: '11'
        distribution: 'temurin'

    - name: Build
      run: ./gradlew assemble

    - name: Test
      run: ./gradlew test --info --tests PorcupinePerformanceTest -DpvTestingAccessKey="${{secrets.PV_VALID_ACCESS_KEY}}" -DnumTestIterations="100" -DperformanceThresholdSec="${{matrix.performance_threshold_sec}}"

  perf-self-hosted:
    runs-on: ${{ matrix.machine }}

    strategy:
      fail-fast: false
      matrix:
        machine: [rpi3-32, rpi3-64, rpi4-32, rpi4-64, rpi5-64]
        include:
        - machine: rpi3-32
          num_test_iterations: 50
          performance_threshold_sec: 2.0
        - machine: rpi3-64
          num_test_iterations: 50
          performance_threshold_sec: 2.5
        - machine: rpi4-32
          num_test_iterations: 50
          performance_threshold_sec: 1.5
        - machine: rpi4-64
          num_test_iterations: 50
          performance_threshold_sec: 1.5
        - machine: rpi5-64
          num_test_iterations: 50
          performance_threshold_sec: 1.0

    steps:
    - uses: actions/checkout@v3

    - name: Build
      run: ./gradlew assemble

    - name: Machine state before
      working-directory: resources/scripts
      run: bash machine-state.sh

    - name: Test
      run: ./gradlew test --info --tests PorcupinePerformanceTest -DpvTestingAccessKey="${{secrets.PV_VALID_ACCESS_KEY}}" -DnumTestIterations="${{matrix.num_test_iterations}}" -DperformanceThresholdSec="${{matrix.performance_threshold_sec}}"

    - name: Machine state after
      working-directory: resources/scripts
      run: bash machine-state.sh

  perf-windows-arm64:
    runs-on: ${{ matrix.machine }}

    strategy:
      fail-fast: false
      matrix:
        machine: [pv-windows-arm64]
        include:
        - machine: pv-windows-arm64
          num_test_iterations: 50
          performance_threshold_sec: 0.5

    steps:
    - uses: actions/checkout@v3

    - name: Build
      run: ./gradlew assemble

    - name: Test
      run: ./gradlew test --info --tests PorcupinePerformanceTest -DpvTestingAccessKey="${{secrets.PV_VALID_ACCESS_KEY}}" -DnumTestIterations="${{matrix.num_test_iterations}}" -DperformanceThresholdSec="${{matrix.performance_threshold_sec}}"
