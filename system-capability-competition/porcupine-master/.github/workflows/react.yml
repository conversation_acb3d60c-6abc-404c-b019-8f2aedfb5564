name: React

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - 'binding/react/**'
      - '!binding/react/README.md'
      - 'lib/common/**'
      - 'lib/wasm/**'
      - 'resources/**'
      - '!resources/.lint/**'
      - '.github/workflows/react.yml'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - 'binding/react/**'
      - '!binding/react/README.md'
      - 'lib/common/**'
      - 'lib/wasm/**'
      - 'resources/**'
      - '!resources/.lint/**'
      - '.github/workflows/react.yml'

defaults:
  run:
    working-directory: binding/react

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.x, 18.x, 20.x]

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}

    - name: Pre-build dependencies
      run: npm install yarn

    - name: Install dependencies
      run: yarn install

    - name: Build
      run: yarn build

    - name: Set up test
      run: yarn setup-test

    - name: Run test
      run: yarn test --env ACCESS_KEY=${{secrets.PV_VALID_ACCESS_KEY}}
