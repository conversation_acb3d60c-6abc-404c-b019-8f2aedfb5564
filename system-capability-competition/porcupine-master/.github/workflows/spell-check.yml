name: SpellCheck

on:
  workflow_dispatch:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]

jobs:
  markdown:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install CSpell
        run: npm install -g cspell

      - name: Run CSpell
        run: cspell --config resources/.lint/spell-check/.cspell.json "**/*"
