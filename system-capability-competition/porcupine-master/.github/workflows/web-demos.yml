name: Web Demos

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - 'demo/web/**'
      - '!demo/web/README.md'
      - '.github/workflows/web-demos.yml'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - 'demo/web/**'
      - '!demo/web/README.md'
      - '.github/workflows/web-demos.yml'

defaults:
  run:
    working-directory: demo/web

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x, 22.x]

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}

    - name: Pre-build dependencies
      run: npm install yarn

    - name: Install dependencies
      run: yarn install
