name: Java

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '.github/workflows/java.yml'
      - 'binding/java/**'
      - '!binding/java/README.md'
      - 'lib/common/**'
      - 'lib/java/**'
      - 'resources/audio_samples/**'
      - 'resources/keyword_files*/linux/**'
      - 'resources/keyword_files*/mac/**'
      - 'resources/keyword_files*/raspberry-pi/**'
      - 'resources/keyword_files*/windows/**'
      - 'resources/.test/**'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '.github/workflows/java.yml'
      - 'binding/java/**'
      - '!binding/java/README.md'
      - 'lib/common/**'
      - 'lib/java/**'
      - 'resources/audio_samples/**'
      - 'resources/keyword_files*/linux/**'
      - 'resources/keyword_files*/mac/**'
      - 'resources/keyword_files*/raspberry-pi/**'
      - 'resources/keyword_files*/windows/**'
      - 'resources/.test/**'

defaults:
  run:
    working-directory: binding/java

jobs:
  build-github-hosted:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]

    steps:
    - uses: actions/checkout@v3

    - name: Set up JDK 11
      uses: actions/setup-java@v3
      with:
        java-version: '11'
        distribution: 'temurin'

    - name: Build
      run: ./gradlew assemble

    - name: Test
      run: ./gradlew test --info --tests PorcupineTest -DpvTestingAccessKey="${{secrets.PV_VALID_ACCESS_KEY}}"

  build-self-hosted:
    runs-on: ${{ matrix.machine }}

    strategy:
      matrix:
        machine: [rpi3-32, rpi3-64, rpi4-32, rpi4-64, rpi5-64, pv-windows-arm64]

    steps:
    - uses: actions/checkout@v3

    - name: Build
      run: ./gradlew assemble

    - name: Test
      run: ./gradlew test --info --tests PorcupineTest -DpvTestingAccessKey="${{secrets.PV_VALID_ACCESS_KEY}}"
