name: Android BrowserStack Tests

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '.github/workflows/android-browserstack.yml'
      - 'binding/android/PorcupineTestApp/**'
      - 'resources/.test/**'
      - 'resources/audio_samples/**'
      - 'script/automation/browserstack.py'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '.github/workflows/android-browserstack.yml'
      - 'binding/android/PorcupineTestApp/**'
      - 'resources/.test/**'
      - 'resources/audio_samples/**'
      - 'script/automation/browserstack.py'

defaults:
  run:
    working-directory: binding/android/PorcupineTestApp/

jobs:
  build:
    name: Run Android Tests on BrowserStack
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Installing Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'
    - run:
        pip3 install requests

    - name: set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Copy test_resources
      run: ./copy_test_resources.sh

    - name: Inject AccessKey
      run: echo pvTestingAccessKey="${{secrets.PV_VALID_ACCESS_KEY}}" >> local.properties

    - name: Inject Android keystore variables
      run: |
        echo storePassword="${{secrets.ANDROID_RELEASE_KEYSTORE_PASSWORD}}" >> local.properties
        echo keyPassword="${{secrets.ANDROID_RELEASE_KEYSTORE_PASSWORD}}" >> local.properties
        echo keyAlias=picovoice >> local.properties
        echo storeFile=../picovoice.jks >> local.properties

    - name: Setup Android keystore file
      run: echo "${{secrets.ANDROID_RELEASE_KEYSTORE_FILE_B64}}" | base64 -d > picovoice.jks

    - name: Grant execute permission for gradlew
      run: chmod +x gradlew

    - name: Build app
      run: ./gradlew assembleEnDebug

    - name: Build androidTest
      run: ./gradlew assembleEnDebugAndroidTest

    - name: Run tests on BrowserStack
      run: python3 ../../../script/automation/browserstack.py
        --type espresso
        --username "${{secrets.BROWSERSTACK_USERNAME}}"
        --access_key "${{secrets.BROWSERSTACK_ACCESS_KEY}}"
        --project_name "Porcupine-Android"
        --devices "android-min-max"
        --app_path "porcupine-test-app/build/outputs/apk/en/debug/porcupine-test-app-en-debug.apk"
        --test_path "porcupine-test-app/build/outputs/apk/androidTest/en/debug/porcupine-test-app-en-debug-androidTest.apk"

  build-integ:
    name: Run Android Integration Tests on BrowserStack
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Installing Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'
    - run:
        pip3 install requests

    - name: set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Copy test_resources
      run: ./copy_test_resources.sh

    - name: Inject AccessKey
      run: echo pvTestingAccessKey="${{secrets.PV_VALID_ACCESS_KEY}}" >> local.properties

    - name: Inject Android keystore variables
      run: |
        echo storePassword="${{secrets.ANDROID_RELEASE_KEYSTORE_PASSWORD}}" >> local.properties
        echo keyPassword="${{secrets.ANDROID_RELEASE_KEYSTORE_PASSWORD}}" >> local.properties
        echo keyAlias=picovoice >> local.properties
        echo storeFile=../picovoice.jks >> local.properties

    - name: Setup Android keystore file
      run: echo "${{secrets.ANDROID_RELEASE_KEYSTORE_FILE_B64}}" | base64 -d > picovoice.jks

    - name: Grant execute permission for gradlew
      run: chmod +x gradlew

    - name: Build app
      run: ./gradlew assembleEnRelease

    - name: Build androidTest
      run: ./gradlew assembleEnReleaseAndroidTest -DtestBuildType=integ

    - name: Run tests on BrowserStack
      run: python3 ../../../script/automation/browserstack.py
        --type espresso
        --username "${{secrets.BROWSERSTACK_USERNAME}}"
        --access_key "${{secrets.BROWSERSTACK_ACCESS_KEY}}"
        --project_name "Porcupine-Android-Integration"
        --devices "android-min-max"
        --app_path "porcupine-test-app/build/outputs/apk/en/release/porcupine-test-app-en-release.apk"
        --test_path "porcupine-test-app/build/outputs/apk/androidTest/en/release/porcupine-test-app-en-release-androidTest.apk"
