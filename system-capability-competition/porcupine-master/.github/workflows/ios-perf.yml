name: iOS Performance

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - 'lib/ios/**'
      - 'lib/common/**'
      - 'resources/keyword_files/ios/**'
      - '.github/workflows/ios-perf.yml'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - 'lib/ios/**'
      - 'lib/common/**'
      - 'resources/keyword_files/ios/**'
      - '.github/workflows/ios-perf.yml'

defaults:
  run:
    working-directory: binding/ios/PorcupineAppTest

jobs:
  build:
    name: Run iOS Tests on BrowserStack
    runs-on: macos-latest

    strategy:
      matrix:
        device: [ ios-perf ]
        include:
        - device: ios-perf
          performanceThresholdSec: 0.5

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Installing Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
      - run:
          pip3 install requests

      - name: Make build dir
        run: mkdir ddp

      - name: Install resource script dependency
        run: |
          brew update
          brew install convmv

      - name: Copy test_resources
        run: ./copy_test_resources.sh

      - name: Inject AccessKey
        run: sed -i '.bak' 's:{TESTING_ACCESS_KEY_HERE}:${{secrets.PV_VALID_ACCESS_KEY}}:'
          PerformanceTest/PerformanceTest.swift

      - name: Inject Number of Iterations
        run: sed -i '.bak' 's:{NUM_TEST_ITERATIONS}:100:'
          PerformanceTest/PerformanceTest.swift

      - name: Inject Performance Threshold
        run: sed -i '.bak' '1,/{PERFORMANCE_THRESHOLD_SEC}/s/{PERFORMANCE_THRESHOLD_SEC}/${{ matrix.performanceThresholdSec }}/'
          PerformanceTest/PerformanceTest.swift

      - name: XCode Build
        run: xcrun xcodebuild build-for-testing
          -configuration Debug
          -project PorcupineAppTest.xcodeproj
          -sdk iphoneos
          -scheme PerformanceTest
          -derivedDataPath ddp
          CODE_SIGNING_ALLOWED=NO

      - name: Generating ipa
        run: cd ddp/Build/Products/Debug-iphoneos/ &&
          mkdir Payload &&
          cp -r PorcupineAppTest.app Payload &&
          zip --symlinks -r PorcupineAppTest.ipa Payload &&
          rm -r Payload

      - name: Zipping Tests
        run: cd ddp/Build/Products/Debug-iphoneos/ &&
          zip --symlinks -r PerformanceTest.zip PerformanceTest-Runner.app

      - name: Run tests on BrowserStack
        run: python3 ../../../script/automation/browserstack.py
          --type xcuitest
          --username "${{secrets.BROWSERSTACK_USERNAME}}"
          --access_key "${{secrets.BROWSERSTACK_ACCESS_KEY}}"
          --project_name "Porcupine-iOS-Performance"
          --devices "${{ matrix.device }}"
          --app_path "ddp/Build/Products/Debug-iphoneos/PorcupineAppTest.ipa"
          --test_path "ddp/Build/Products/Debug-iphoneos/PerformanceTest.zip"
