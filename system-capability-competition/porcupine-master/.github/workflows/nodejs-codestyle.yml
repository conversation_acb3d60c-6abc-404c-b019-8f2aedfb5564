name: Node.js Codestyle

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '**/nodejs/*.js'
      - '**/nodejs/*.ts'
      - '.github/workflows/nodejs-codestyle.yml'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '**/nodejs/*.js'
      - '**/nodejs/*.ts'
      - '.github/workflows/nodejs-codestyle.yml'

jobs:
  check-nodejs-codestyle:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js LTS
      uses: actions/setup-node@v3
      with:
        node-version: lts/*

    - name: Pre-build dependencies
      run: npm install yarn

    - name: Run Binding Linter
      run: yarn && yarn lint
      working-directory: binding/nodejs
