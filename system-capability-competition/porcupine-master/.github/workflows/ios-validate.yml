name: Validate iOS Demo
on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '.github/workflows/ios-validate.yml'
      - 'demo/ios/ForegroundApp/**'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '.github/workflows/ios-validate.yml'
      - 'demo/ios/ForegroundApp/**'


jobs:
  validate-ios-demo:
    runs-on: macos-latest

    defaults:
      run:
        working-directory: demo/ios/ForegroundApp

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - uses: maxim-lobanov/setup-xcode@v1
        with:
          xcode-version: '16'

      - name: Install the Apple certificate and provisioning profile
        env:
          BUILD_CERTIFICATE_BASE64: ${{ secrets.CERTIFICATE_BASE64 }}
          P12_PASSWORD: ${{ secrets.P12_PASSWORD }}
          BUILD_PROVISION_PROFILE_BASE64: ${{ secrets.PROVISION_PROFILE_BASE64 }}
          KEYCHAIN_PASSWORD: ${{ secrets.KEYCHAIN_PASSWORD }}
        run: |
          CERTIFICATE_PATH=$RUNNER_TEMP/build_certificate.p12
          PP_PATH=$RUNNER_TEMP/build_pp.mobileprovision
          KEYCHAIN_PATH=$RUNNER_TEMP/app-signing.keychain-db

          echo -n "$BUILD_CERTIFICATE_BASE64" | base64 --decode -o $CERTIFICATE_PATH
          echo -n "$BUILD_PROVISION_PROFILE_BASE64" | base64 --decode -o $PP_PATH

          security create-keychain -p "$KEYCHAIN_PASSWORD" $KEYCHAIN_PATH
          security set-keychain-settings -lut 21600 $KEYCHAIN_PATH
          security unlock-keychain -p "$KEYCHAIN_PASSWORD" $KEYCHAIN_PATH

          security import $CERTIFICATE_PATH -P "$P12_PASSWORD" -A -t cert -f pkcs12 -k $KEYCHAIN_PATH
          security set-key-partition-list -S apple-tool:,apple: -k "$KEYCHAIN_PASSWORD" $KEYCHAIN_PATH
          security list-keychain -d user -s $KEYCHAIN_PATH

          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          cp $PP_PATH ~/Library/MobileDevice/Provisioning\ Profiles

      - name: Sed app id
        run: sed -E -i '' 's#(PRODUCT_BUNDLE_IDENTIFIER = )[^;]+;#\1ai.picovoice.validation;#' ForegroundApp.xcodeproj/project.pbxproj

      - name: Create ExportOptions.plist
        run: echo ${{ secrets.EXPORT_PLIST_BASE64 }} | base64 --decode -o ./ExportOptions.plist

      - name: Archive app
        run: xcodebuild -project ForegroundApp.xcodeproj -sdk iphoneos -scheme _enDemo -destination "generic/platform=ios" archive -archivePath "./archive" CODE_SIGNING_ALLOWED=No CODE_SIGNING_REQUIRED=Yes

      - name: Export App
        run: xcodebuild -exportArchive -archivePath ./archive.xcarchive -exportOptionsPlist ExportOptions.plist -exportPath ./output/

      - name: Validate app
        run: xcrun altool --validate-app -f ./output/ForegroundApp.ipa -t ios -u ${{ secrets.APPLE_ID }} -p ${{ secrets.APPLE_PASSWORD }}
