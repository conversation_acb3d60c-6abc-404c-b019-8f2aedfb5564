name: Web Codestyle

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '**/web/*.js'
      - '**/web/*.ts'
      - '.github/workflows/web-codestyle.yml'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '**/web/*.js'
      - '**/web/*.ts'
      - '.github/workflows/web-codestyle.yml'

jobs:
  check-web-codestyle:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js LTS
      uses: actions/setup-node@v3
      with:
        node-version: lts/*

    - name: Pre-build dependencies
      run: npm install yarn

    - name: Run Binding Linter
      run: yarn && yarn lint
      working-directory: binding/web
