name: Rust Demos

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '.github/workflows/rust-demos.yml'
      - 'demo/rust/**'
      - '!demo/rust/README.md'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '.github/workflows/rust-demos.yml'
      - 'demo/rust/**'
      - '!demo/rust/README.md'

env:
  CARGO_TERM_COLOR: always

defaults:
  run:
    working-directory: demo/rust
    shell: bash

jobs:
  build-github-hosted:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]

    steps:
    - uses: actions/checkout@v3

    - name: Rust dependencies
      if: matrix.os == 'ubuntu-latest'
      run: sudo apt install libasound2-dev -y

    - name: Install stable toolchain
      uses: actions-rs/toolchain@v1
      with:
        profile: minimal
        toolchain: stable
        override: true

    - name: Rust build micdemo
      run: cargo build --verbose
      working-directory: demo/rust/micdemo

    - name: Rust build filedemo
      run: cargo build --verbose
      working-directory: demo/rust/filedemo

    - name: Test
      run: cargo run --release -- --access_key ${{secrets.PV_VALID_ACCESS_KEY}} --input_audio_path ../../../resources/audio_samples/multiple_keywords.wav --keywords picovoice
      working-directory: demo/rust/filedemo

  build-self-hosted:
    runs-on: ${{ matrix.machine }}

    strategy:
      matrix:
        machine: [rpi3-32, rpi3-64, rpi4-32, rpi4-64, rpi5-64]

    steps:
    - uses: actions/checkout@v3

    - name: Install stable toolchain
      uses: actions-rs/toolchain@v1
      with:
        toolchain: nightly
        override: true

    - name: Rust build micdemo
      run: cargo build --verbose
      working-directory: demo/rust/micdemo

    - name: Rust build filedemo
      run: cargo build --verbose
      working-directory: demo/rust/filedemo

    - name: Test
      run: cargo run --release -- --access_key ${{secrets.PV_VALID_ACCESS_KEY}} --input_audio_path ../../../resources/audio_samples/multiple_keywords.wav --keywords picovoice
      working-directory: demo/rust/filedemo
