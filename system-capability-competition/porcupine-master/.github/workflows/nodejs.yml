name: Node.js

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '.github/workflows/nodejs.yml'
      - 'binding/nodejs/**'
      - '!binding/nodejs/README.md'
      - 'lib/node/**'
      - 'lib/common/**'
      - 'resources/audio_samples/**'
      - 'resources/keyword_files*/linux/**'
      - 'resources/keyword_files*/mac/**'
      - 'resources/keyword_files*/raspberry-pi/**'
      - 'resources/keyword_files*/windows/**'
      - 'resources/test/**'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '.github/workflows/nodejs.yml'
      - 'binding/nodejs/**'
      - '!binding/nodejs/README.md'
      - 'lib/node/**'
      - 'lib/common/**'
      - 'resources/audio_samples/**'
      - 'resources/keyword_files*/linux/**'
      - 'resources/keyword_files*/mac/**'
      - 'resources/keyword_files*/raspberry-pi/**'
      - 'resources/keyword_files*/windows/**'
      - 'resources/test/**'

defaults:
  run:
    working-directory: binding/nodejs

jobs:
  build-github-hosted:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        node-version: [18.x, 20.x, 22.x]

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}

    - name: Pre-build dependencies
      run: npm install yarn

    - name: Install dependencies
      run: yarn install

    - name: Test
      run: yarn test test/index.test.ts --access_key=${{secrets.PV_VALID_ACCESS_KEY}}

  build-self-hosted:
    runs-on: ${{ matrix.machine }}

    strategy:
      matrix:
        machine: [rpi3-32, rpi3-64, rpi4-32, rpi4-64, rpi5-64, pv-windows-arm64]

    steps:
    - uses: actions/checkout@v3

    - name: Pre-build dependencies
      run: npm install --global yarn

    - name: Install dependencies
      run: yarn install

    - name: Test
      run: yarn test test/index.test.ts --access_key=${{secrets.PV_VALID_ACCESS_KEY}}
