name: Flutter Tests

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '.github/workflows/flutter.yml'
      - 'demo/flutter/pubspec.yaml'
      - 'lib/common/**'
      - 'resources/.test/**'
      - 'resources/audio_samples/**'
      - 'resources/keyword_files*/android/**'
      - 'resources/keyword_files*/ios/**'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '.github/workflows/flutter.yml'
      - 'demo/flutter/pubspec.yaml'
      - 'lib/common/**'
      - 'resources/.test/**'
      - 'resources/audio_samples/**'
      - 'resources/keyword_files*/android/**'
      - 'resources/keyword_files*/ios/**'

defaults:
  run:
    working-directory: demo/flutter

jobs:
  test-android:
    name: Test Android
    runs-on: pv-android

    steps:
    - uses: actions/checkout@v3

    - name: Copy test_resources
      run: ./copy_test_resources.sh

    - name: Inject AppID
      run: sed -i 's:{TESTING_ACCESS_KEY_HERE}:${{secrets.PV_VALID_ACCESS_KEY}}:'
        integration_test/app_test.dart

    - name: Install dependencies
      run: flutter pub get

    - name: Prepare demo
      run: dart scripts/prepare_demo.dart en

    - name: Run integration tests
      run: flutter test integration_test

  test-ios:
    name: Test iOS
    runs-on: pv-ios

    steps:
    - uses: actions/checkout@v3

    - name: Copy test_resources
      run: ./copy_test_resources.sh

    - name: Inject AppID
      run: sed -i '.bak' 's:{TESTING_ACCESS_KEY_HERE}:${{secrets.PV_VALID_ACCESS_KEY}}:'
        integration_test/app_test.dart

    - name: Update Cocoapods repo
      run: pod repo update

    - name: Install dependencies
      run: flutter pub get

    - name: Prepare demo
      run: dart scripts/prepare_demo.dart en

    - name: Run integration tests
      run: flutter test integration_test
