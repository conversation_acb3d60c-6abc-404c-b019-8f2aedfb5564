name: Java CodeStyle

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '**/*.java'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '**/*.java'

jobs:
  check-java-codestyle:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up JDK 11
      uses: actions/setup-java@v3
      with:
        java-version: '11'
        distribution: 'temurin'

    - name: Check Java CodeStyle
      run: java -Dconfig_loc=resources/.lint/java/ -jar resources/.lint/java/checkstyle-10.5.0-all.jar -c resources/.lint/java/checkstyle.xml binding/android/ binding/java/ binding/flutter/android/ binding/react-native/android/ demo/android/ demo/java/
