name: Web Performance

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - 'lib/wasm/**'
      - 'lib/common/**'
      - '.github/workflows/web-perf.yml'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - 'lib/wasm/**'
      - 'lib/common/**'
      - '.github/workflows/web-perf.yml'

defaults:
  run:
    working-directory: binding/web

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [lts/*]
        include:
        - node-version: lts/*
          initPerformanceThresholdSec: 0.5
          procPerformanceThresholdSec: 0.65

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}

    - name: Pre-build dependencies
      run: npm install yarn

    - name: Install dependencies
      run: yarn install

    - name: Copy libs
      run: yarn copywasm && yarn copyppn

    - name: Build
      run: yarn build

    - name: Prepare Test
      run: yarn setup-test

    - name: Test
      run: yarn test-perf --env ACCESS_KEY=${{secrets.PV_VALID_ACCESS_KEY}},NUM_TEST_ITERATIONS=20,INIT_PERFORMANCE_THRESHOLD_SEC=${{matrix.initPerformanceThresholdSec}},PROC_PERFORMANCE_THRESHOLD_SEC=${{matrix.procPerformanceThresholdSec}}
