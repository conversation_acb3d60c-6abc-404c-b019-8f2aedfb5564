name: Rust

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '.github/workflows/rust.yml'
      - 'binding/rust/**'
      - 'binding/rust/**/*.rs'
      - '!binding/rust/README.md'
      - 'lib/common/**'
      - 'lib/linux/**'
      - 'lib/mac/**'
      - 'lib/raspberry-pi/**'
      - 'lib/windows/**'
      - 'resources/audio_samples/**'
      - 'resources/keyword_files*/linux/**'
      - 'resources/keyword_files*/mac/**'
      - 'resources/keyword_files*/raspberry-pi/**'
      - 'resources/keyword_files*/windows/**'
      - 'resources/.test/**'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '.github/workflows/rust.yml'
      - 'binding/rust/**'
      - 'binding/rust/**/*.rs'
      - '!binding/rust/README.md'
      - 'lib/common/**'
      - 'lib/linux/**'
      - 'lib/mac/**'
      - 'lib/raspberry-pi/**'
      - 'lib/windows/**'
      - 'resources/audio_samples/**'
      - 'resources/keyword_files*/linux/**'
      - 'resources/keyword_files*/mac/**'
      - 'resources/keyword_files*/raspberry-pi/**'
      - 'resources/keyword_files*/windows/**'
      - 'resources/.test/**'

env:
  CARGO_TERM_COLOR: always

defaults:
  run:
    working-directory: binding/rust
    shell: bash

jobs:
  build-github-hosted:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]

    steps:
    - uses: actions/checkout@v3

    - name: Rust pre-build
      run: bash copy.sh

    - name: Rust dependencies
      if: matrix.os == 'ubuntu-latest'
      run: sudo apt install libasound2-dev -y

    - name: Install stable toolchain
      uses: actions-rs/toolchain@v1
      with:
        profile: minimal
        toolchain: stable
        override: true

    - name: Rust build
      run: cargo build --verbose

    - name: Rust run tests
      run: PV_ACCESS_KEY=${{secrets.PV_VALID_ACCESS_KEY}} cargo test --verbose

  build-self-hosted:
    runs-on: ${{ matrix.machine }}

    strategy:
      matrix:
        machine: [rpi3-32, rpi3-64, rpi4-32, rpi4-64, rpi5-64]

    steps:
    - uses: actions/checkout@v3

    - name: Rust pre-build
      run: bash copy.sh

    - name: Install stable toolchain
      uses: actions-rs/toolchain@v1
      with:
        toolchain: nightly
        override: true

    - name: Rust build
      run: cargo build --verbose

    - name: Rust run tests
      run: PV_ACCESS_KEY=${{secrets.PV_VALID_ACCESS_KEY}} cargo test --verbose
