name: Android Demos

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '.github/workflows/android-demos.yml'
      - 'demo/android/**'
      - '!demo/android/Activity/README.md'
      - '!demo/android/Service/README.md'
      - '!demo/android/STT/README.md'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '.github/workflows/android-demos.yml'
      - 'demo/android/**'
      - '!demo/android/Activity/README.md'
      - '!demo/android/Service/README.md'
      - '!demo/android/STT/README.md'

jobs:
  build-activity-demo:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        java-version: [11, 17, 21]
        include:
        - java-version: 11
          gradle-version: 6.5
          agp-version: 4.1.3
        - java-version: 17
          gradle-version: 7.5
          agp-version: 7.4.2
        - java-version: 21
          gradle-version: 8.5
          agp-version: 8.2.2

    defaults:
      run:
        working-directory: demo/android/Activity

    steps:
    - uses: actions/checkout@v3
    
    - name: Override gradle settings
      run: sed -i "s/com.android.tools.build:gradle:[0-9]*\.[0-9]*\.[0-9]*/com.android.tools.build:gradle:${{ matrix.agp-version }}/g" build.gradle

    - name: set up JDK ${{ matrix.java-version }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ matrix.java-version }}
        distribution: 'temurin'

    - name: Use Gradle ${{ matrix.gradle-version }}
      uses: gradle/actions/setup-gradle@v3
      with:
        gradle-version: ${{ matrix.gradle-version }}


    - name: Build
      run: gradle assembleDebug

  build-service-demo:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        java-version: [11, 17, 21]
        include:
        - java-version: 11
          gradle-version: 6.5
          agp-version: 4.1.3
        - java-version: 17
          gradle-version: 7.5
          agp-version: 7.4.2
        - java-version: 21
          gradle-version: 8.5
          agp-version: 8.2.2

    defaults:
      run:
        working-directory: demo/android/Service

    steps:
    - uses: actions/checkout@v3
 
    - name: Override gradle settings
      run: sed -i "s/com.android.tools.build:gradle:[0-9]*\.[0-9]*\.[0-9]*/com.android.tools.build:gradle:${{ matrix.agp-version }}/g" build.gradle
      
    - name: set up JDK ${{ matrix.java-version }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ matrix.java-version }}
        distribution: 'temurin'

    - name: Use Gradle ${{ matrix.gradle-version }}
      uses: gradle/actions/setup-gradle@v3
      with:
        gradle-version: ${{ matrix.gradle-version }}

    - name: Build
      run: gradle assembleDebug

  build-stt-demo:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        java-version: [11, 17, 21]
        include:
        - java-version: 11
          gradle-version: 6.5
          agp-version: 4.1.3
        - java-version: 17
          gradle-version: 7.5
          agp-version: 7.4.2
        - java-version: 21
          gradle-version: 8.5
          agp-version: 8.2.2

    defaults:
      run:
        working-directory: demo/android/STT

    steps:
    - uses: actions/checkout@v3
 
    - name: Override gradle settings
      run: sed -i "s/com.android.tools.build:gradle:[0-9]*\.[0-9]*\.[0-9]*/com.android.tools.build:gradle:${{ matrix.agp-version }}/g" build.gradle

    - name: set up JDK ${{ matrix.java-version }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ matrix.java-version }}
        distribution: 'temurin'

    - name: Use Gradle ${{ matrix.gradle-version }}
      uses: gradle/actions/setup-gradle@v3
      with:
        gradle-version: ${{ matrix.gradle-version }}

    - name: Build
      run: gradle assembleDebug
