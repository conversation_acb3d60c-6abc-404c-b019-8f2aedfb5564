name: Java Demos

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '.github/workflows/java-demos.yml'
      - 'demo/java/**'
      - '!demo/java/README.md'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '.github/workflows/java-demos.yml'
      - 'demo/java/**'
      - '!demo/java/README.md'

defaults:
  run:
    working-directory: demo/java

jobs:
  build-github-hosted:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        java-version: [11, 17, 21]
        include:
        - java-version: 11
          gradle-version: 6.5
        - java-version: 17
          gradle-version: 7.3
        - java-version: 21
          gradle-version: 8.5

    steps:
    - uses: actions/checkout@v3

    - name: Set up JDK ${{ matrix.java-version }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ matrix.java-version }}
        distribution: 'temurin'

    - uses: gradle/actions/setup-gradle@v3
      with:
        gradle-version: ${{ matrix.gradle-version }}

    - name: Build
      run: gradle build

    - name: Run filedemo
      run: java -jar build/libs/porcupine-file-demo.jar -a  ${{secrets.PV_VALID_ACCESS_KEY}} -i ../../resources/audio_samples/multiple_keywords.wav -k picovoice

  build-self-hosted:
    runs-on: ${{ matrix.machine }}

    strategy:
      matrix:
        machine: [rpi3-32, rpi3-64, rpi4-32, rpi4-64, rpi5-64, pv-windows-arm64]

    steps:
    - uses: actions/checkout@v3

    - name: Build
      run: ./gradlew build

    - name: Run filedemo
      run: java -jar build/libs/porcupine-file-demo.jar -a  ${{secrets.PV_VALID_ACCESS_KEY}} -i ../../resources/audio_samples/multiple_keywords.wav -k picovoice
