name: C Codestyle

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - '**/*.c'
      - '.github/workflows/c-codestyle.yml'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - '**/*.c'
      - '.github/workflows/c-codestyle.yml'

jobs:
  check-c-codestyle:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'

    - name: Install dependencies
      run: sudo apt install clang-format

    - name: Check c codestyle
      run: python3 resources/.lint/c/formatter.py -c -v
