name: Unity

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - 'binding/unity/**'
      - '!binding/unity/README.md'
      - 'lib/common/**'
      - 'lib/android/**'
      - 'lib/ios/**'
      - 'lib/linux/**'
      - 'lib/mac/**'
      - 'lib/windows/**'
      - 'resources/.test/**'
      - 'resources/audio_samples/**'
      - 'resources/keyword_files*/android/**'
      - 'resources/keyword_files*/ios/**'
      - 'resources/keyword_files*/linux/**'
      - 'resources/keyword_files*/mac/**'
      - 'resources/keyword_files*/windows/**'
      - '.github/workflows/unity.yml'
  pull_request:
    branches: [ master, 'v[0-9]+.[0-9]+' ]
    paths:
      - 'binding/unity/**'
      - '!binding/unity/README.md'
      - 'lib/common/**'
      - 'lib/android/**'
      - 'lib/ios/**'
      - 'lib/linux/**'
      - 'lib/mac/**'
      - 'lib/windows/**'
      - 'resources/.test/**'
      - 'resources/audio_samples/**'
      - 'resources/keyword_files*/android/**'
      - 'resources/keyword_files*/ios/**'
      - 'resources/keyword_files*/linux/**'
      - 'resources/keyword_files*/mac/**'
      - 'resources/keyword_files*/windows/**'
      - '.github/workflows/unity.yml'

defaults:
  run:
    working-directory: binding/unity

jobs:
  playtest-linux:
    name: Run PlayTest unit tests on Linux
    runs-on: pv-android

    steps:
    - uses: actions/checkout@v3
      with:
        submodules: 'recursive'

    - name: Pre-build dependencies
      run: |
        ./copy.sh
        ./copy_test_resources.sh

    - name: Inject AppID
      run: sed -i 's:{TESTING_ACCESS_KEY_HERE}:${{secrets.PV_VALID_ACCESS_KEY}}:'
        Assets/Porcupine/Tests/Integration.cs

    - name: Run tests
      run: xvfb-run --auto-servernum --server-args='-screen 0 640x480x24' /home/<USER>/Unity/Hub/Editor/2021.3.31f1/Editor/Unity -runTests -batchmode -projectPath . -testResults unityresults.xml -testPlatform StandaloneLinux64 -logFile -

    - name: Check Results
      run: sed -n 2p unityresults.xml | grep 'result="Passed"'

  # NOTE: fix later as mac runner halts unity test app in between runs. Running locally works fine as well as first try after restarting runner.
  # playtest-mac:
  #   name: Run PlayTest unit tests on macOS
  #   runs-on: pv-ios

  #   steps:
  #   - uses: actions/checkout@v3
  #     with:
  #       submodules: 'recursive'

  #   - name: Pre-build dependencies
  #     run: |
  #       ./copy.sh
  #       ./copy_test_resources.sh

  #   - name: Inject AppID
  #     run: sed -i '.bak' 's:{TESTING_ACCESS_KEY_HERE}:${{secrets.PV_VALID_ACCESS_KEY}}:'
  #       Assets/Porcupine/Tests/Integration.cs

  #   - name: Run tests
  #     run: /Applications/Unity/Hub/Editor/2021.3.31f1/Unity.app/Contents/MacOS/Unity -runTests -batchmode -projectPath . -testResults unityresults.xml -testPlatform StandaloneOSX -logFile -

  #   - name: Check Results
  #     run: sed -n 2p unityresults.xml | grep 'result="Passed"'

  playtest-windows:
    name: Run PlayTest unit tests on Windows
    runs-on: pv-windows

    steps:
    - uses: actions/checkout@v3
      with:
        submodules: 'recursive'

    - name: Pre-build dependencies
      run: |
        dos2unix copy.sh
        bash copy.sh
        dos2unix copy_test_resources.sh
        bash copy_test_resources.sh

    - name: Inject AppID
      run: bash -c "sed -i 's:{TESTING_ACCESS_KEY_HERE}:${{secrets.PV_VALID_ACCESS_KEY}}:'
        Assets/Porcupine/Tests/Integration.cs"

    - name: Run tests
      run: >
        & "C:\Program Files\Unity\Hub\Editor\2021.3.31f1\Editor\Unity.exe" -runTests -batchmode -projectPath . -testResults unityresults.xml -testPlatform StandaloneWindows64 -logFile - | Out-Host

    - name: Check Results
      run: bash -c "sed -n 2p unityresults.xml | grep Passed"

  playtest-android:
    name: Run PlayTest unit tests on Android
    runs-on: pv-ios

    steps:
    - uses: actions/checkout@v3
      with:
        submodules: 'recursive'

    - name: Pre-build dependencies
      run: |
        ./copy.sh
        ./copy_test_resources.sh

    - name: Inject AppID
      run: sed -i '.bak' 's:{TESTING_ACCESS_KEY_HERE}:${{secrets.PV_VALID_ACCESS_KEY}}:'
        Assets/Porcupine/Tests/Integration.cs

    - name: Start Emulator
      run: /Users/<USER>/Library/Android/sdk/emulator/emulator -avd Pixel_6_API_33 &

    - name: Run tests
      run: /Applications/Unity/Hub/Editor/2021.3.31f1/Unity.app/Contents/MacOS/Unity -runTests -batchmode -buildTarget Android -projectPath . -testResults unityresults.xml -testPlatform Android -logFile -

    - name: Check Results
      run: sed -n 2p unityresults.xml | grep 'result="Passed"'
