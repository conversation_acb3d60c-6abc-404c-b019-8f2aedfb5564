apply plugin: 'com.android.library'

ext {
    PUBLISH_GROUP_ID = 'ai.picovoice'
    PUBLISH_VERSION = '3.0.3'
    PUBLISH_ARTIFACT_ID = 'porcupine-android'
}

android {
    compileSdkVersion defaultTargetSdkVersion

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion defaultTargetSdkVersion
        versionCode 1
        versionName "1.0"

        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

if (file("${rootDir}/publish-mavencentral.gradle").exists()) {
    apply from: "${rootDir}/publish-mavencentral.gradle"
}

dependencies {
    implementation "ai.picovoice:android-voice-processor:1.0.2"
}

task copyLibs(type: Copy) {
    from("${rootDir}/../../../lib/android")
    into("${rootDir}/porcupine/src/main/jniLibs")
}

task copyParams(type: Copy) {
    from("${rootDir}/../../../lib/common")
    include('porcupine_params.pv')
    into("${rootDir}/porcupine/src/main/res/raw")
}

task copyModels(type: Copy) {
    from("${rootDir}/../../../resources/keyword_files/android")
    include('alexa_android.ppn',
        'americano_android.ppn',
        'blueberry_android.ppn',
        'bumblebee_android.ppn',
        'computer_android.ppn',
        'grapefruit_android.ppn',
        'grasshopper_android.ppn',
        'hey google_android.ppn',
        'hey siri_android.ppn',
        'jarvis_android.ppn',
        'ok google_android.ppn',
        'picovoice_android.ppn',
        'porcupine_android.ppn',
        'terminator_android.ppn')
    into("${rootDir}/porcupine/src/main/res/raw")
    rename { String fileName ->
        fileName.replace("_android", "").replace(" ", "_")
    }
}

preBuild.dependsOn(copyLibs)
preBuild.dependsOn(copyParams)
preBuild.dependsOn(copyModels)
