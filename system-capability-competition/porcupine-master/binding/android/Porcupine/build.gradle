ext {
    defaultTargetSdkVersion = 31
}

buildscript {
    repositories {
        maven { url "https://plugins.gradle.org/m2/" }
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.1.3'
        classpath 'ai.picovoice:android-voice-processor:1.0.2'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
