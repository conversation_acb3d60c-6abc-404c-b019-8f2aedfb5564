<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/startGuideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.05"/>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/endGuideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.95"/>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/topGuideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.01"/>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/bottomGuideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.97"/>

    <TextView
        android:id="@+id/testResult"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="@id/endGuideline"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@id/startGuideline"
        app:layout_constraintTop_toBottomOf="@id/topGuideline"/>

    <TableLayout
        android:id="@+id/resultTable"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@id/endGuideline"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@id/startGuideline"
        app:layout_constraintTop_toBottomOf="@id/testResult" >

        <TableRow
            android:layout_width="fill_parent"
            android:layout_height="fill_parent">

            <TextView
                android:text="Passed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAlignment="center"
                android:background="@android:color/holo_green_dark"
                android:textColor="@android:color/white"
                android:layout_weight="1" />

            <TextView
                android:text="Failed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAlignment="center"
                android:background="@android:color/holo_red_dark"
                android:textColor="@android:color/white"
                android:layout_weight="1" />

        </TableRow>

        <TableRow
            android:layout_width="fill_parent"
            android:layout_height="fill_parent">

            <TextView
                android:id="@+id/testNumPassed"
                android:text="0"

                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAlignment="center"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/testNumFailed"
                android:text="0"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAlignment="center"
                android:layout_weight="1" />

        </TableRow>

    </TableLayout>

    <ListView
        android:id="@+id/resultList"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        app:layout_constraintEnd_toEndOf="@id/endGuideline"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@id/startGuideline"
        app:layout_constraintTop_toBottomOf="@id/resultTable"
        app:layout_constraintBottom_toTopOf="@id/testButton" />

    <Button
        android:id="@+id/testButton"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:background="@drawable/button_background"
        android:textColor="@android:color/white"
        android:text="@string/test"
        android:onClick="startTest"
        android:textSize="24sp"
        app:layout_constraintEnd_toEndOf="@id/endGuideline"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@id/startGuideline"
        app:layout_constraintBottom_toBottomOf="@id/bottomGuideline" />

</androidx.constraintlayout.widget.ConstraintLayout>