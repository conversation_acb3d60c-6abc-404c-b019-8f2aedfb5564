apply plugin: 'com.android.application'

Properties properties = new Properties()
if (rootProject.file("local.properties").exists()) {
    properties.load(rootProject.file("local.properties").newDataInputStream())
    if (project.hasProperty("pvTestingAccessKey")) {
        properties.put("pvTestingAccessKey", project.getProperty("pvTestingAccessKey"))
    }
    if (project.hasProperty("numTestIterations")) {
        properties.put("numTestIterations", project.getProperty("numTestIterations"))
    }
    if (project.hasProperty("performanceThresholdSec")) {
        properties.put("performanceThresholdSec", project.getProperty("performanceThresholdSec"))
    }

    if (project.hasProperty("storePassword")) {
        properties.put("storePassword", project.getProperty("storePassword"))
    }
    if (project.hasProperty("storeFile")) {
        properties.put("storeFile", project.getProperty("storeFile"))
    }
    if (project.hasProperty("keyAlias")) {
        properties.put("keyAlias", project.getProperty("keyAlias"))
    }
    if (project.hasProperty("keyPassword")) {
        properties.put("keyPassword", project.getProperty("keyPassword"))
    }
}

android {
    compileSdkVersion defaultTargetSdkVersion

    defaultConfig {
        applicationId "ai.picovoice.porcupine.testapp"
        minSdkVersion 21
        targetSdkVersion defaultTargetSdkVersion
        versionCode 7
        versionName "3.0.0"
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

        resValue 'string', 'pvTestingAccessKey', properties.getProperty("pvTestingAccessKey", "")
        resValue 'string', 'numTestIterations', properties.getProperty("numTestIterations", "")
        resValue 'string', 'performanceThresholdSec', properties.getProperty("performanceThresholdSec", "")
    }

    signingConfigs {
        release {
            storePassword properties.getProperty("storePassword")
            storeFile file(properties.getProperty("storeFile", ".dummy.jks"))
            keyAlias properties.getProperty("keyAlias")
            keyPassword properties.getProperty("keyPassword")
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.release
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }

    if (System.getProperty("testBuildType", "debug") == "integ") {
        testBuildType("release")
    }

    def testDataFile = file('../../../../resources/.test/test_data.json')
    def parsedJson = new groovy.json.JsonSlurper().parseText(testDataFile.text)
    def languages = []
    parsedJson.tests.singleKeyword.each { a ->
        languages.add(a.language)
    }

    flavorDimensions "language"
    productFlavors {
        languages.each { language ->
            "$language" {
                applicationIdSuffix ".$language"

            }
        }

        all { flavor ->
            delete fileTree("$projectDir/src/main/assets") {
                exclude '**/.gitkeep'
            }
            String suffix = (flavor.name != "en") ? "_${flavor.name}" : ""
            task("${flavor.name}CopyParams", type: Copy) {
                from("$projectDir/../../../../lib/common/")
                include("porcupine_params${suffix}.pv")
                into("$projectDir/src/main/assets/models")
            }
            task("${flavor.name}CopyKeywords", type: Copy) {
                description = "Copy ${flavor.name} resources"
                from("$projectDir/../../../../resources/keyword_files${suffix}/android")
                include('*.ppn')
                into("$projectDir/src/main/assets/keywords")
                rename { String fileName ->
                    fileName.replace("_android", "").replace(" ", "_")
                }
            }
            task("${flavor.name}CopyAudio", type: Copy) {
                description = "Copy ${flavor.name} audio resources"
                from("$projectDir/../../../../resources/audio_samples/")
                include("multiple_keywords${suffix}.wav")
                into("$projectDir/src/main/assets/audio_samples")
            }
        }
    }
    sourceSets {
        androidTest {
            java {
                if (System.getProperty("testBuildType", "debug") == "perf") {
                    exclude "**/StandardTests.java"
                    exclude "**/LanguageTests.java"
                    exclude "**/IntegrationTest.java"
                } else if (System.getProperty("testBuildType", "debug") == "integ") {
                    exclude "**/StandardTests.java"
                    exclude "**/LanguageTests.java"
                    exclude "**/PerformanceTest.java"
                } else {
                    exclude "**/IntegrationTest.java"
                    exclude "**/PerformanceTest.java"
                }
            }
        }
    }
    lint {
        abortOnError false
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'androidx.appcompat:appcompat:1.3.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'com.google.code.gson:gson:2.10'
    implementation 'ai.picovoice:porcupine-android:3.0.3'

    // Espresso UI Testing
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation('androidx.test.espresso:espresso-core:3.2.0', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })
    androidTestImplementation('androidx.test.espresso:espresso-intents:3.5.1')
}


afterEvaluate {
    android.productFlavors.all {
        flavor ->
            tasks."merge${flavor.name.capitalize()}DebugAssets".dependsOn "${flavor.name}CopyParams"
            tasks."merge${flavor.name.capitalize()}ReleaseAssets".dependsOn "${flavor.name}CopyParams"
            tasks."merge${flavor.name.capitalize()}DebugAssets".dependsOn "${flavor.name}CopyKeywords"
            tasks."merge${flavor.name.capitalize()}ReleaseAssets".dependsOn "${flavor.name}CopyKeywords"
            tasks."merge${flavor.name.capitalize()}DebugAssets".dependsOn "${flavor.name}CopyAudio"
            tasks."merge${flavor.name.capitalize()}ReleaseAssets".dependsOn "${flavor.name}CopyAudio"
    }
}
