# Porcupine Binding for Android

## Porcupine

Porcupine is a highly accurate and lightweight wake word engine. It enables building always-listening voice-enabled applications using cutting edge voice AI.

Porcupine is:

- private and offline
- [accurate](https://github.com/Picovoice/wake-word-benchmark)
- [resource efficient](https://www.youtube.com/watch?v=T0tAnh8tUQg) (runs even on microcontrollers)
- data efficient (wake words can be easily generated by simply typing them, without needing thousands of hours of bespoke audio training data and manual effort)
- scalable to many simultaneous wake-words / always-on voice commands
- cross-platform

To learn more about Po<PERSON>upine, see the [product](https://picovoice.ai/products/porcupine/), [documentation](https://picovoice.ai/docs/), and [GitHub](https://github.com/Picovoice/porcupine/) pages.

## Compatibility

- Android 5.0+ (API 21+)

## Installation

Porcupine can be found on Maven Central. To include the package in your Android project, ensure you have included `mavenCentral()` in your top-level `build.gradle` file and then add the following to your app's `build.gradle`:

```groovy
dependencies {
    // ...
    implementation 'ai.picovoice:porcupine-android:${LATEST_VERSION}'
}
```

## AccessKey

Porcupine requires a valid Picovoice `AccessKey` at initialization. `AccessKey` acts as your credentials when using Porcupine SDKs.
You can get your `AccessKey` for free. Make sure to keep your `AccessKey` secret.
Signup or Login to [Picovoice Console](https://console.picovoice.ai/) to get your `AccessKey`.

## Permissions

To enable AccessKey validation and recording with your Android device's microphone, you must add the following line to your `AndroidManifest.xml` file:
```xml
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.INTERNET" />
```

## Usage

The module provides you with two levels of API to choose from depending on your needs.

### High-Level API

[PorcupineManager](Porcupine/porcupine/src/main/java/ai/picovoice/porcupine/PorcupineManager.java) provides a high-level API that takes care of audio recording and wake word detection. This class is the quickest way to get started.

To create an instance of PorcupineManager, use the PorcupineManager Builder:
```java
import ai.picovoice.porcupine.*;

final String accessKey = "${ACCESS_KEY}";

try {
    Porcupine.BuiltInKeyword[] keywords = new Porcupine.BuiltInKeyword[]{
        Porcupine.BuiltInKeyword.PICOVOICE,
        Porcupine.BuiltInKeyword.PORCUPINE
    }

    PorcupineManager porcupineManager = new PorcupineManager.Builder()
                        .setAccessKey(accessKey)
                        .setKeywords(keywords)
                        .build(context, wakeWordCallback);
} catch (PorcupineException e) { }
```

The `context` parameter is the Android application context - this is used to extract Porcupine resources from the APK. The `wakeWordCallback` parameter is `PorcupineManagerCallback` that will be invoked when Porcupine has detected one of the keywords.
The callback should accept a single integer, keywordIndex, which specifies which wake word has been detected.

```java
PorcupineManagerCallback wakeWordCallback = new PorcupineManagerCallback() {
            @Override
            public void invoke(int keywordIndex) {
                if(keywordIndex == 0){
                    // picovoice detected!
                }
                else if(keywordIndex == 1){
                    // porcupine detected!
                }
            }
}
```

Available built-in keywords are accessible via the Porcupine.BuiltInKeyword enum.

To create an instance of PorcupineManager that detects custom keywords, you can use the `setKeywordPaths()` builder argument instead. Custom keyword files (`.ppn`) are obtained from the [Picovoice Console](https://console.picovoice.ai/). Once you exported a custom keyword, add it to your Android assets folder (`src/main/assets`) and pass the relative path into the Builder.

In this example our `.ppn` files are stored in our assets folder, under a subdirectory called `keywords`:
```java
final String accessKey = "${ACCESS_KEY}";

try {
    String[] keywordPaths = new String[]{
        "keywords/one.ppn",
        "keywords/two.ppn"
    }

    PorcupineManager porcupineManager = new PorcupineManager.Builder()
                        .setAccessKey(accessKey)
                        .setKeywordPaths(keywordPaths)
                        .build(context, wakeWordCallback);
} catch (PorcupineException e) { }
```

In addition to custom keywords, you can override the default Porcupine english model file and/or keyword sensitivities.

Sensitivity is the parameter that enables trading miss rate for the false alarm rate. It is a floating-point number within [0, 1]. A higher sensitivity reduces the miss rate at the cost of increased false alarm rate.

The model file (`.pv`) contains the parameters for the wake word engine. To change the language that Porcupine understands, you'll pass in a different model file. Like custom keywords, store the files in Android assets folder (`src/main/assets`) and pass in the relative path.

There is also the option to pass an error callback, which will be invoked if an error is encountered while PorcupineManager is processing audio.

These optional parameters can be set like so:
```java
final String accessKey = "${ACCESS_KEY}";

try {
    PorcupineManager porcupineManager = new PorcupineManager.Builder()
                        .setAccessKey(accessKey)
                        .setKeywordPaths(keywordPaths)
                        .setModelPath("models/porcupine_model.pv")
                        .setSensitivities(new float[] { 0.6f, 0.35f })
                        .setErrorCallback(new PorcupineManagerErrorCallback() {
                            @Override
                            public void invoke(PorcupineException e) {
                                // process error
                            }
                        })
                        .build(context, wakeWordCallback);
} catch (PorcupineException e) { }
```

Alternatively, if the model files are deployed to the device with a different method, the absolute paths to the files on device can be used.

Once you have instantiated a PorcupineManager, you can start audio capture and wake word detection by calling:

```java
porcupineManager.start();
```

And then stop it by calling:

```java
try{
    porcupineManager.start();
} catch (PorcupineException e) { }
```

Once the app is done with using an instance of PorcupineManager, be sure you explicitly release the resources allocated to Porcupine:
```java
porcupineManager.delete();
```

### Low-Level API

[Porcupine](Porcupine/porcupine/src/main/java/ai/picovoice/porcupine/Porcupine.java) provides low-level access to the wake word engine for those who want to incorporate wake word detection into a already existing audio processing pipeline.

`Porcupine` uses a Builder pattern to construct instances.

```java
import ai.picovoice.porcupine.*;

final String accessKey = "${ACCESS_KEY}";

try {
    Porcupine porcupine = new Porcupine.Builder()
                        .setAccessKey(accessKey)
                        .setKeyword(Porcupine.BuiltInKeyword.PICOVOICE)
                        .build(context);
} catch (PorcupineException e) { }
```

To search for a keyword in audio, you must pass frames of audio to Porcupine using the `process` function. The `keywordIndex` returned will either be -1 if no detection was made or an integer specifying which keyword was detected.

```java
short[] getNextAudioFrame(){
    // .. get audioFrame
    return audioFrame;
}

while(true) {
    try {
        int keywordIndex = porcupine.process(getNextAudioFrame());
        if(keywordIndex >= 0) {
            // .. detection made!
        }
    } catch (PorcupineException e) { }
}
```

For `process` to work correctly, the audio data must be in the audio format required by Picovoice.
The required audio format is found by calling `.getSampleRate()` to get the required sample rate and `.getFrameLength()` to get the required number of samples per input frame. Audio must be single-channel and 16-bit linearly-encoded.

Once you're done with Porcupine, ensure you release its resources explicitly:
```java
porcupine.delete();
```

## Custom Wake Word Integration

To add a custom wake word or model file to your application, add the files to your assets folder (`src/main/assets`) and then pass the relative paths to the Porcupine Builder.

In this example our files are located in the assets folder under subdirectory `picovoice_files`:

```java
final String accessKey = "${ACCESS_KEY}";

try {
    Porcupine porcupine = new Porcupine.Builder()
                        .setAccessKey(accessKey)
                        .setKeywordPath("picovoice_files/keyword.ppn")
                        .setModelPath("picovoice_files/model.pv")
                        .build(context);
} catch (PorcupineException e) { }
```
Alternatively, if the keyword file is deployed to the device with a different method, the absolute path to the file on device can be used.

## Non-English Wake Words

In order to detect non-English wake words you need to use the corresponding model file. The model files for all supported languages are available [here](../../lib/common).

## Demo Apps

For example usage refer to the
[Activity demo](../../demo/android/Activity), [Service demo](../../demo/android/Service) or [STT demo](../../demo/android/STT).

## Resource Usage

The following profile graph was captured running the [Porcupine Activity demo](../../demo/android/Activity) on a Google Pixel 3:

![](android_profiling.gif)

- CPU <= 1%
- Battery Usage <= LOW
- Memory <= 128 MB
