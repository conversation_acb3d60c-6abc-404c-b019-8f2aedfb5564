# Porcupine Binding for iOS

## Porcupine

Porcupine is a highly accurate and lightweight wake word engine. It enables building always-listening voice-enabled applications using cutting edge voice AI.

Porcupine is:

- private and offline
- [accurate](https://github.com/Picovoice/wake-word-benchmark)
- [resource efficient](https://www.youtube.com/watch?v=T0tAnh8tUQg) (runs even on microcontrollers)
- data efficient (wake words can be easily generated by simply typing them, without needing thousands of hours of bespoke audio training data and manual effort)
- scalable to many simultaneous wake-words / always-on voice commands
- cross-platform

To learn more about Porcupine, see the [product](https://picovoice.ai/products/porcupine/), [documentation](https://picovoice.ai/docs/), and [GitHub](https://github.com/Picovoice/porcupine/) pages.

## Installation
<!-- markdown-link-check-disable -->
The Porcupine iOS binding is available via [Swift Package Manger](https://www.swift.org/documentation/package-manager/) or [CocoaPods](https://cocoapods.org/pods/Porcupine-iOS).
<!-- markdown-link-check-enable -->

To import the package using SPM, open up your project's Package Dependencies in XCode and add:
```
https://github.com/Picovoice/porcupine.git
```

To import it into your iOS project using CocoaPods, add the following line to your Podfile:
```ruby
pod 'Porcupine-iOS'
```

## AccessKey

Porcupine requires a valid Picovoice `AccessKey` at initialization. `AccessKey` acts as your credentials when using Porcupine SDKs.
You can get your `AccessKey` for free. Make sure to keep your `AccessKey` secret.
Signup or Login to [Picovoice Console](https://console.picovoice.ai/) to get your `AccessKey`.

## Permissions

To enable recording with your iOS device's microphone you must add the following to your app's `Info.plist` file:
```xml
<key>NSMicrophoneUsageDescription</key>
<string>[Permission explanation]</string>
```

## Usage

The module provides you with two levels of API to choose from depending on your needs.

### High-Level API

[PorcupineManager](PorcupineManager.swift) provides a high-level API that takes care of audio recording and wake word detection. This class is the quickest way to get started.

To create an instance of PorcupineManager pass the desired keyword to the constructor:
```swift
import Porcupine

let accessKey = "${ACCESS_KEY}" // Obtained from Picovoice Console (https://console.picovoice.ai)

do {
    let porcupineManager = try PorcupineManager(
        accessKey: accessKey,
        keyword: Porcupine.BuiltInKeyword.picovoice,
        onDetection: wakeWordCallback)
} catch { }
```

The `wakeWordCallback` parameter is function that will be invoked when Porcupine has detected one of the keywords.
The callback should accept a single integer, which specifies which wake word has been detected.

```swift
let wakeWordCallback: ((Int32) -> Void) = { keywordIndex in
                if keywordIndex == 0 {
                    // wake word 0 detected!
                }
            }
}
```

Available built-in keywords are accessible via the Porcupine.BuiltInKeyword enum.

To create an instance of PorcupineManager that detects custom keywords, you can use the `keywordPaths` parameter instead:
```swift
do {
    let accessKey = "${ACCESS_KEY}" // Obtained from Picovoice Console (https://console.picovoice.ai)
    let paths = ["path/to/keyword/one.ppn", "path/to/keyword/two.ppn"]
    let porcupineManager = try PorcupineManager(
        accessKey: accessKey,
        keywordPaths: paths,
        onDetection: wakeWordCallback)
} catch { }
```

In addition to custom keywords, you can override the default Porcupine English model file and/or keyword sensitivities.

Sensitivity is the parameter that enables trading miss rate for the false alarm rate. It is a floating-point number within [0, 1]. A higher sensitivity reduces the miss rate at the cost of increased false alarm rate.

The model file contains the parameters for the wake word engine. To change the language that Porcupine understands, pass in a different model file.

These optional parameters can be set like so:
```swift
do {
    let accessKey = "${ACCESS_KEY}" // Obtained from Picovoice Console (https://console.picovoice.ai)
    let paths = ["path/to/keyword/one.ppn", "path/to/keyword/two.ppn"]
    let porcupineManager = try PorcupineManager(
        accessKey: accessKey,
        keywordPaths: paths,
        modelPath: "path/to/model/file.pv",
        sensitivities: [0.7, 0.35],
        onDetection: wakeWordCallback)
} catch { }
```

Once you have instantiated a PorcupineManager, you can start audio capture and wake word detection by calling:

```swift
do {
    porcupineManager.start()
} catch { }
```

And then stop it by calling:

```swift
porcupineManager.stop()
```

Once the app is done with using an instance of PorcupineManager you can release the native resources manually rather than waiting for the garbage collector:
```swift
porcupineManager.delete()
```

### Low-Level API

[Porcupine](Porcupine.swift) provides low-level access to the wake word engine for those who want to incorporate wake word detection into an already existing audio processing pipeline.

To construct an instance of Porcupine, pass it a keyword.

```swift
import Porcupine

do {
    let accessKey = "${ACCESS_KEY}" // Obtained from Picovoice Console (https://console.picovoice.ai)
    let porcupine = try Porcupine(accessKey: accessKey, keyword: Porcupine.BuiltInKeyword.picovoice)
} catch { }
```

To search for a keyword in audio, you must pass frames of audio to Porcupine using the `process` function. The `keywordIndex` returned will either be -1 if no detection was made or an integer specifying which keyword was detected.

```swift
func getNextAudioFrame() -> [Int16] {
    // .. get audioFrame
    return audioFrame;
}

while true {
    do {
        let keywordIndex = try porcupine.process(getNextAudioFrame())
        if keywordIndex >= 0 {
            // .. detection made!
        }
    } catch { }
}
```

For `process` to work correctly, the audio data must be in the audio format required by Picovoice.
The required audio format is found by using `Porcupine.sampleRate` to get the required sample rate and `Porcupine.frameLength` to get the required number of samples per input frame. Audio must be single-channel and 16-bit linearly-encoded.

Once you're done with Porcupine you can force it to release its native resources rather than waiting for the garbage collector:
```swift
porcupine.delete();
```

## Custom Wake Word Integration

To add a custom wake word to your iOS application you can include it in your app as a bundled resource (found by selecting in Build Phases > Copy Bundle Resources). Then in code, get its path like so:

```swift
// file is called keyword_ios.ppn
let keywordPath = Bundle.main.path(forResource: "keyword_ios", ofType: "ppn")
```

Alternatively, if the model files are deployed to the device with a different method, the absolute paths to the files on device can be used.

## Non-English Contexts

In order to detect non-English wake words you need to use the corresponding model file. The model files for all supported languages are available [here](../../lib/common).

## Running Unit Tests

Open [`PorcupineAppTest.xcodeproj`](PorcupineAppTest/PorcupineAppTest.xcodeproj) with XCode and run the tests with `Product > Test`.

## Demo App

For example usage refer to our [iOS demo application](../../demo/ios).
