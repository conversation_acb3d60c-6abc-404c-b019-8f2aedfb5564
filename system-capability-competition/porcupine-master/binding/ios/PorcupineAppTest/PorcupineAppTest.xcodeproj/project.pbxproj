// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		02C7054B2CCB14450002B3E4 /* Porcupine in Frameworks */ = {isa = PBXBuildFile; productRef = 02C7054A2CCB14450002B3E4 /* Porcupine */; };
		02C7054E2CCB14980002B3E4 /* Porcupine in Frameworks */ = {isa = PBXBuildFile; productRef = 02C7054D2CCB14980002B3E4 /* Porcupine */; };
		02C705502CCB14B50002B3E4 /* Porcupine in Frameworks */ = {isa = PBXBuildFile; productRef = 02C7054F2CCB14B50002B3E4 /* Porcupine */; };
		02C705532CCB15430002B3E4 /* Porcupine in Frameworks */ = {isa = PBXBuildFile; productRef = 02C705522CCB15430002B3E4 /* Porcupine */; };
		02C705562CCB156D0002B3E4 /* Porcupine in Frameworks */ = {isa = PBXBuildFile; productRef = 02C705552CCB156D0002B3E4 /* Porcupine */; };
		1E5B7AA127FFC12000F8BDDB /* PerformanceTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 32769C288BB32DEA6645EC92 /* PerformanceTest.swift */; };
		1E5B7ABA27FFC18400F8BDDB /* multiple_keywords.wav in Resources */ = {isa = PBXBuildFile; fileRef = 1E5B7AB927FFC18400F8BDDB /* multiple_keywords.wav */; };
		1E7744B427CD9D7E00491D0B /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1E7744B327CD9D7E00491D0B /* AppDelegate.swift */; };
		1E7744B827CD9D7E00491D0B /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1E7744B727CD9D7E00491D0B /* ViewController.swift */; };
		1E7744BB27CD9D7E00491D0B /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 1E7744B927CD9D7E00491D0B /* Main.storyboard */; };
		1E7744BD27CD9D7F00491D0B /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1E7744BC27CD9D7F00491D0B /* Assets.xcassets */; };
		1E7744C027CD9D7F00491D0B /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 1E7744BE27CD9D7F00491D0B /* LaunchScreen.storyboard */; };
		1E7744D527CD9D7F00491D0B /* PorcupineAppTestUITests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1E7744D427CD9D7F00491D0B /* PorcupineAppTestUITests.swift */; };
		A52A751425D956A94947A6AF /* PorcupineLanguageTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = A52A78D82E189D1EA9423736 /* PorcupineLanguageTests.swift */; };
		A52A78ED18AA4EDB33D95C4D /* BaseTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = A52A7583A49E7ACAA9C7A0C5 /* BaseTest.swift */; };
		C765AF9029AD7B75009D2BC2 /* test_resources in Resources */ = {isa = PBXBuildFile; fileRef = C765AF8F29AD7B75009D2BC2 /* test_resources */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		1E5B7A9D27FFC12000F8BDDB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1E7744A827CD9D7E00491D0B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1E7744AF27CD9D7E00491D0B;
			remoteInfo = PorcupineAppTest;
		};
		1E7744D127CD9D7F00491D0B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1E7744A827CD9D7E00491D0B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1E7744AF27CD9D7E00491D0B;
			remoteInfo = PorcupineAppTest;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		1E5B7AB727FFC12000F8BDDB /* PerformanceTest.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PerformanceTest.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		1E5B7AB827FFC15100F8BDDB /* info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = info.plist; sourceTree = "<group>"; };
		1E5B7AB927FFC18400F8BDDB /* multiple_keywords.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; name = multiple_keywords.wav; path = ../../../../resources/audio_samples/multiple_keywords.wav; sourceTree = "<group>"; };
		1E7744B027CD9D7E00491D0B /* PorcupineAppTest.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PorcupineAppTest.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1E7744B327CD9D7E00491D0B /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		1E7744B727CD9D7E00491D0B /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		1E7744BA27CD9D7E00491D0B /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		1E7744BC27CD9D7F00491D0B /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		1E7744BF27CD9D7F00491D0B /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		1E7744C127CD9D7F00491D0B /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		1E7744D027CD9D7F00491D0B /* PorcupineAppTestUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PorcupineAppTestUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		1E7744D427CD9D7F00491D0B /* PorcupineAppTestUITests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PorcupineAppTestUITests.swift; sourceTree = "<group>"; };
		1E7744E327CDA13600491D0B /* info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = info.plist; sourceTree = "<group>"; };
		32769C288BB32DEA6645EC92 /* PerformanceTest.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PerformanceTest.swift; sourceTree = "<group>"; };
		A52A7583A49E7ACAA9C7A0C5 /* BaseTest.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BaseTest.swift; sourceTree = "<group>"; };
		A52A78D82E189D1EA9423736 /* PorcupineLanguageTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PorcupineLanguageTests.swift; sourceTree = "<group>"; };
		C765AF8F29AD7B75009D2BC2 /* test_resources */ = {isa = PBXFileReference; lastKnownFileType = folder; path = test_resources; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1E5B7AA227FFC12000F8BDDB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				02C705502CCB14B50002B3E4 /* Porcupine in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1E7744AD27CD9D7E00491D0B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				02C705562CCB156D0002B3E4 /* Porcupine in Frameworks */,
				02C705532CCB15430002B3E4 /* Porcupine in Frameworks */,
				02C7054B2CCB14450002B3E4 /* Porcupine in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1E7744CD27CD9D7F00491D0B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				02C7054E2CCB14980002B3E4 /* Porcupine in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1E5B7A9A27FFC0C400F8BDDB /* PerformanceTest */ = {
			isa = PBXGroup;
			children = (
				32769C288BB32DEA6645EC92 /* PerformanceTest.swift */,
				1E5B7AB827FFC15100F8BDDB /* info.plist */,
				1E5B7AB927FFC18400F8BDDB /* multiple_keywords.wav */,
			);
			path = PerformanceTest;
			sourceTree = "<group>";
		};
		1E7744A727CD9D7E00491D0B = {
			isa = PBXGroup;
			children = (
				1E5B7A9A27FFC0C400F8BDDB /* PerformanceTest */,
				1E7744B227CD9D7E00491D0B /* PorcupineAppTest */,
				1E7744D327CD9D7F00491D0B /* PorcupineAppTestUITests */,
				1E7744B127CD9D7E00491D0B /* Products */,
			);
			sourceTree = "<group>";
		};
		1E7744B127CD9D7E00491D0B /* Products */ = {
			isa = PBXGroup;
			children = (
				1E7744B027CD9D7E00491D0B /* PorcupineAppTest.app */,
				1E7744D027CD9D7F00491D0B /* PorcupineAppTestUITests.xctest */,
				1E5B7AB727FFC12000F8BDDB /* PerformanceTest.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1E7744B227CD9D7E00491D0B /* PorcupineAppTest */ = {
			isa = PBXGroup;
			children = (
				1E7744B327CD9D7E00491D0B /* AppDelegate.swift */,
				1E7744B727CD9D7E00491D0B /* ViewController.swift */,
				1E7744B927CD9D7E00491D0B /* Main.storyboard */,
				1E7744BC27CD9D7F00491D0B /* Assets.xcassets */,
				1E7744BE27CD9D7F00491D0B /* LaunchScreen.storyboard */,
				1E7744C127CD9D7F00491D0B /* Info.plist */,
			);
			path = PorcupineAppTest;
			sourceTree = "<group>";
		};
		1E7744D327CD9D7F00491D0B /* PorcupineAppTestUITests */ = {
			isa = PBXGroup;
			children = (
				C765AF8F29AD7B75009D2BC2 /* test_resources */,
				1E7744D427CD9D7F00491D0B /* PorcupineAppTestUITests.swift */,
				1E7744E327CDA13600491D0B /* info.plist */,
				A52A78D82E189D1EA9423736 /* PorcupineLanguageTests.swift */,
				A52A7583A49E7ACAA9C7A0C5 /* BaseTest.swift */,
			);
			path = PorcupineAppTestUITests;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1E5B7A9B27FFC12000F8BDDB /* PerformanceTest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1E5B7AB427FFC12000F8BDDB /* Build configuration list for PBXNativeTarget "PerformanceTest" */;
			buildPhases = (
				1E5B7A9F27FFC12000F8BDDB /* Sources */,
				1E5B7AA227FFC12000F8BDDB /* Frameworks */,
				1E5B7AA427FFC12000F8BDDB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1E5B7A9C27FFC12000F8BDDB /* PBXTargetDependency */,
			);
			name = PerformanceTest;
			productName = PorcupineAppTestUITests;
			productReference = 1E5B7AB727FFC12000F8BDDB /* PerformanceTest.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
		1E7744AF27CD9D7E00491D0B /* PorcupineAppTest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1E7744DA27CD9D7F00491D0B /* Build configuration list for PBXNativeTarget "PorcupineAppTest" */;
			buildPhases = (
				1E7744AC27CD9D7E00491D0B /* Sources */,
				1E7744AD27CD9D7E00491D0B /* Frameworks */,
				1E7744AE27CD9D7E00491D0B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PorcupineAppTest;
			productName = PorcupineAppTest;
			productReference = 1E7744B027CD9D7E00491D0B /* PorcupineAppTest.app */;
			productType = "com.apple.product-type.application";
		};
		1E7744CF27CD9D7F00491D0B /* PorcupineAppTestUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1E7744E027CD9D7F00491D0B /* Build configuration list for PBXNativeTarget "PorcupineAppTestUITests" */;
			buildPhases = (
				1E7744CC27CD9D7F00491D0B /* Sources */,
				1E7744CD27CD9D7F00491D0B /* Frameworks */,
				1E7744CE27CD9D7F00491D0B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1E7744D227CD9D7F00491D0B /* PBXTargetDependency */,
			);
			name = PorcupineAppTestUITests;
			productName = PorcupineAppTestUITests;
			productReference = 1E7744D027CD9D7F00491D0B /* PorcupineAppTestUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1E7744A827CD9D7E00491D0B /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1320;
				LastUpgradeCheck = 1320;
				TargetAttributes = {
					1E5B7A9B27FFC12000F8BDDB = {
						TestTargetID = 1E7744AF27CD9D7E00491D0B;
					};
					1E7744AF27CD9D7E00491D0B = {
						CreatedOnToolsVersion = 13.2.1;
					};
					1E7744CF27CD9D7F00491D0B = {
						CreatedOnToolsVersion = 13.2.1;
						TestTargetID = 1E7744AF27CD9D7E00491D0B;
					};
				};
			};
			buildConfigurationList = 1E7744AB27CD9D7E00491D0B /* Build configuration list for PBXProject "PorcupineAppTest" */;
			compatibilityVersion = "Xcode 13.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1E7744A727CD9D7E00491D0B;
			packageReferences = (
				02C705542CCB156D0002B3E4 /* XCLocalSwiftPackageReference "../../../../porcupine" */,
			);
			productRefGroup = 1E7744B127CD9D7E00491D0B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1E7744AF27CD9D7E00491D0B /* PorcupineAppTest */,
				1E7744CF27CD9D7F00491D0B /* PorcupineAppTestUITests */,
				1E5B7A9B27FFC12000F8BDDB /* PerformanceTest */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1E5B7AA427FFC12000F8BDDB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1E5B7ABA27FFC18400F8BDDB /* multiple_keywords.wav in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1E7744AE27CD9D7E00491D0B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1E7744C027CD9D7F00491D0B /* LaunchScreen.storyboard in Resources */,
				1E7744BD27CD9D7F00491D0B /* Assets.xcassets in Resources */,
				1E7744BB27CD9D7E00491D0B /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1E7744CE27CD9D7F00491D0B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C765AF9029AD7B75009D2BC2 /* test_resources in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1E5B7A9F27FFC12000F8BDDB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1E5B7AA127FFC12000F8BDDB /* PerformanceTest.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1E7744AC27CD9D7E00491D0B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1E7744B827CD9D7E00491D0B /* ViewController.swift in Sources */,
				1E7744B427CD9D7E00491D0B /* AppDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1E7744CC27CD9D7F00491D0B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1E7744D527CD9D7F00491D0B /* PorcupineAppTestUITests.swift in Sources */,
				A52A751425D956A94947A6AF /* PorcupineLanguageTests.swift in Sources */,
				A52A78ED18AA4EDB33D95C4D /* BaseTest.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		1E5B7A9C27FFC12000F8BDDB /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1E7744AF27CD9D7E00491D0B /* PorcupineAppTest */;
			targetProxy = 1E5B7A9D27FFC12000F8BDDB /* PBXContainerItemProxy */;
		};
		1E7744D227CD9D7F00491D0B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1E7744AF27CD9D7E00491D0B /* PorcupineAppTest */;
			targetProxy = 1E7744D127CD9D7F00491D0B /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		1E7744B927CD9D7E00491D0B /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				1E7744BA27CD9D7E00491D0B /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		1E7744BE27CD9D7F00491D0B /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				1E7744BF27CD9D7F00491D0B /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		1E5B7AB527FFC12000F8BDDB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 65723695GD;
				GENERATE_INFOPLIST_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ai.picovoice.PorcupineAppTestUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = PorcupineAppTest;
			};
			name = Debug;
		};
		1E5B7AB627FFC12000F8BDDB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 65723695GD;
				GENERATE_INFOPLIST_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ai.picovoice.PorcupineAppTestUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = PorcupineAppTest;
			};
			name = Release;
		};
		1E7744D827CD9D7F00491D0B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_INCLUDE_PATHS = "${SRCROOT}";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1E7744D927CD9D7F00491D0B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_INCLUDE_PATHS = "${SRCROOT}";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1E7744DB27CD9D7F00491D0B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 65723695GD;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PorcupineAppTest/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				PRODUCT_BUNDLE_IDENTIFIER = ai.picovoice.PorcupineAppTest;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		1E7744DC27CD9D7F00491D0B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 65723695GD;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PorcupineAppTest/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ai.picovoice.PorcupineAppTest;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		1E7744E127CD9D7F00491D0B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 65723695GD;
				GENERATE_INFOPLIST_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ai.picovoice.PorcupineAppTestUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = PorcupineAppTest;
			};
			name = Debug;
		};
		1E7744E227CD9D7F00491D0B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 65723695GD;
				GENERATE_INFOPLIST_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ai.picovoice.PorcupineAppTestUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = PorcupineAppTest;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1E5B7AB427FFC12000F8BDDB /* Build configuration list for PBXNativeTarget "PerformanceTest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1E5B7AB527FFC12000F8BDDB /* Debug */,
				1E5B7AB627FFC12000F8BDDB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1E7744AB27CD9D7E00491D0B /* Build configuration list for PBXProject "PorcupineAppTest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1E7744D827CD9D7F00491D0B /* Debug */,
				1E7744D927CD9D7F00491D0B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1E7744DA27CD9D7F00491D0B /* Build configuration list for PBXNativeTarget "PorcupineAppTest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1E7744DB27CD9D7F00491D0B /* Debug */,
				1E7744DC27CD9D7F00491D0B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1E7744E027CD9D7F00491D0B /* Build configuration list for PBXNativeTarget "PorcupineAppTestUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1E7744E127CD9D7F00491D0B /* Debug */,
				1E7744E227CD9D7F00491D0B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		02C705542CCB156D0002B3E4 /* XCLocalSwiftPackageReference "../../../../porcupine" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = ../../../../porcupine;
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCRemoteSwiftPackageReference section */
		02C705512CCB15430002B3E4 /* XCRemoteSwiftPackageReference "porcupine" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Picovoice/porcupine";
			requirement = {
				kind = revision;
				revision = 90fb6b91266c68ed26d4fa0ba7eb67195c362cc9;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		02C7054A2CCB14450002B3E4 /* Porcupine */ = {
			isa = XCSwiftPackageProductDependency;
			productName = Porcupine;
		};
		02C7054D2CCB14980002B3E4 /* Porcupine */ = {
			isa = XCSwiftPackageProductDependency;
			productName = Porcupine;
		};
		02C7054F2CCB14B50002B3E4 /* Porcupine */ = {
			isa = XCSwiftPackageProductDependency;
			productName = Porcupine;
		};
		02C705522CCB15430002B3E4 /* Porcupine */ = {
			isa = XCSwiftPackageProductDependency;
			package = 02C705512CCB15430002B3E4 /* XCRemoteSwiftPackageReference "porcupine" */;
			productName = Porcupine;
		};
		02C705552CCB156D0002B3E4 /* Porcupine */ = {
			isa = XCSwiftPackageProductDependency;
			productName = Porcupine;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 1E7744A827CD9D7E00491D0B /* Project object */;
}
