{"compilerOptions": {"lib": ["esnext"], "target": "esnext", "module": "commonjs", "moduleResolution": "node", "outDir": "./dist", "allowJs": false, "allowSyntheticDefaultImports": true, "downlevelIteration": true, "isolatedModules": false, "noEmit": false, "removeComments": false, "resolveJsonModule": true, "sourceMap": true, "strict": true, "noImplicitAny": true}, "exclude": ["node_modules", "dist", "test"]}