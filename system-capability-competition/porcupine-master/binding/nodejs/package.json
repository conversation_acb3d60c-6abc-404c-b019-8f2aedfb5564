{"name": "@picovoice/porcupine-node", "version": "3.0.6", "description": "Picovoice Porcupine Node.js binding", "main": "dist/index.js", "types": "dist/types/index.d.ts", "keywords": ["porcupine", "picovoice", "wake word", "hotword", "trigger word", "offline", "private", "voice", "ai", "speech recognition"], "author": "Picovoice, Inc.", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/Picovoice/porcupine.git", "directory": "binding/nodejs"}, "homepage": "https://picovoice.ai/products/porcupine/", "bugs": {"url": "https://github.com/Picovoice/porcupine/issues"}, "scripts": {"build": "npm-run-all --parallel build:**", "build:all": "tsc", "build:types": "tsc --declaration --declarationMap --emitDeclarationOnly --outDir ./dist/types", "prepack": "npm run build", "prepare": "node copy.js", "test": "jest --no-cache", "lint": "eslint . --ext .js,.ts"}, "devDependencies": {"@types/jest": "^27.4.1", "@types/node": "^18.11.9", "@typescript-eslint/eslint-plugin": "^5.19.0", "@typescript-eslint/parser": "^5.19.0", "eslint": "^8.13.0", "eslint-plugin-jest": "^27.1.6", "jest": "^27.5.1", "mkdirp": "^3.0.1", "ncp": "^2.0.0", "npm-run-all": "^4.1.5", "prettier": "^2.6.2", "ts-jest": "^27.1.3", "typescript": "^4.6.2", "wavefile": "^11.0.0"}, "engines": {"node": ">=18.0.0"}, "cpu": ["!ia32", "!mips", "!ppc", "!ppc64"], "dependencies": {}}