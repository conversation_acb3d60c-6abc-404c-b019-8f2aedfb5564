<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <ItemGroup>
        <Content Include="$(MSBuildThisFileDirectory)resources\**">
            <Link>resources\%(RecursiveDir)%(Filename)%(Extension)</Link>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Include="$(MSBuildThisFileDirectory)lib\**">
            <Link>lib\%(RecursiveDir)%(Filename)%(Extension)</Link>
            <PackagePath>content\picovoice\%(RecursiveDir)%(Filename)%(Extension)</PackagePath>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <Visible>false</Visible>
        </Content>
        <Content Include="$(MSBuildThisFileDirectory)\..\common\porcupine_params.pv">
            <Link>lib\common\porcupine_params.pv</Link>
            <PackagePath>content\picovoice\common\porcupine_params.pv</PackagePath>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <Visible>false</Visible>
        </Content>
    </ItemGroup>
</Project>