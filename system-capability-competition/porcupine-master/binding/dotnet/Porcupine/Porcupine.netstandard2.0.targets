<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <ItemGroup>
        <Content Include="$(MSBuildThisFileDirectory)..\..\lib\netstandard2.0\Porcupine.dll">
            <Link>Porcupine.dll</Link>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <Visible>False</Visible>
        </Content>
        <Content Include="$(MSBuildThisFileDirectory)libpv_porcupine.dll">
            <Link>libpv_porcupine.dll</Link>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <Visible>false</Visible>
        </Content>
        <Content Include="$(MSBuildThisFileDirectory)libpv_porcupine.dylib">
            <Link>libpv_porcupine.dylib</Link>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <Visible>false</Visible>
        </Content>
    </ItemGroup>
    <ItemGroup>
        <Content Include="$(MSBuildThisFileDirectory)resources\**">
            <Link>resources\%(RecursiveDir)%(Filename)%(Extension)</Link>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Include="$(MSBuildThisFileDirectory)lib\**">
            <PackagePath>content\picovoice\%(RecursiveDir)%(Filename)%(Extension)</PackagePath>
            <Link>lib\%(RecursiveDir)%(Filename)%(Extension)</Link>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <Visible>false</Visible>
        </Content>
        <Content Include="$(MSBuildThisFileDirectory)\..\common\porcupine_params.pv">
            <Link>lib\common\porcupine_params.pv</Link>
            <PackagePath>content\picovoice\common\porcupine_params.pv</PackagePath>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <Visible>false</Visible>
        </Content>
    </ItemGroup>
</Project>