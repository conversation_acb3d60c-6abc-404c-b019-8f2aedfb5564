﻿/*
    Copyright 2020-2025 Picovoice Inc.

    You may not use this file except in compliance with the license. A copy of the license is located in the "LICENSE"
    file accompanying this source.

    Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
    an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
    specific language governing permissions and limitations under the License.
*/

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;

namespace Pv
{
    public static class Utils
    {
        private static Architecture _arch => RuntimeInformation.ProcessArchitecture;

        private static bool _isArm => _arch is Architecture.Arm || _arch is Architecture.Arm64;
        private static string _env => RuntimeInformation.IsOSPlatform(OSPlatform.OSX) ? "mac" :
                                                 RuntimeInformation.IsOSPlatform(OSPlatform.Windows) ? "windows" :
                                                 RuntimeInformation.IsOSPlatform(OSPlatform.Linux) && _arch == Architecture.X64 ? "linux" :
                                                 RuntimeInformation.IsOSPlatform(OSPlatform.Linux) && _isArm ? PvLinuxEnv() : "";

        public static string PvModelPath()
        {
            return Path.Combine(AppContext.BaseDirectory, "lib/common/porcupine_params.pv");
        }

        public static Dictionary<BuiltInKeyword, string> PvKeywordPaths()
        {
            Dictionary<BuiltInKeyword, string> keywordPaths = new Dictionary<BuiltInKeyword, string>();

            string keywordFilesDir = Path.Combine(AppContext.BaseDirectory, "resources/keyword_files", _env);
            if (Directory.Exists(keywordFilesDir))
            {
                foreach (string keywordFile in Directory.GetFiles(keywordFilesDir))
                {
                    string enumName = Path.GetFileName(keywordFile).Split('_')[0].Replace(" ", "_").ToUpper();
                    BuiltInKeyword builtin = (BuiltInKeyword)Enum.Parse(typeof(BuiltInKeyword), enumName);
                    keywordPaths.Add(builtin, Path.Combine(keywordFilesDir, keywordFile));
                }
            }

            return keywordPaths;
        }

        public static string PvLibraryPath(string libName)
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows) && _arch == Architecture.X64)
            {
                return Path.Combine(AppContext.BaseDirectory, $"lib/{_env}/amd64/{libName}.dll");
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows) && _arch == Architecture.Arm64)
            {
                return Path.Combine(AppContext.BaseDirectory, $"lib/{_env}/arm64/{libName}.dll");
            }
            if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX) && _arch == Architecture.X64)
            {
                return Path.Combine(AppContext.BaseDirectory, $"lib/{_env}/x86_64/{libName}.dylib");
            }
            if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX) && _isArm)
            {
                return Path.Combine(AppContext.BaseDirectory, $"lib/{_env}/arm64/{libName}.dylib");
            }
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                return Path.Combine(AppContext.BaseDirectory, $"lib/{_env}/{PvLinuxMachine()}/{libName}.so");
            }
            throw new PlatformNotSupportedException($"{RuntimeInformation.OSDescription} ({RuntimeInformation.OSArchitecture}) is not currently supported.\n" +
                                                    "Visit https://picovoice.ai/docs/api/porcupine-dotnet/ to see a list of supported platforms.");
        }

        public static string PvLinuxMachine()
        {
            string archInfo = "";
            if (_arch == Architecture.X64)
            {
                return "x86_64";
            }

            if (_arch == Architecture.Arm64)
            {
                archInfo = "-aarch64";

            }
            string cpuPart = GetCpuPart();
            switch (cpuPart)
            {
                case "0xd03": return "cortex-a53" + archInfo;
                case "0xd08": return "cortex-a72" + archInfo;
                case "0xd0b": return "cortex-a76" + archInfo;
                default:
                    throw new PlatformNotSupportedException($"This device (CPU part = {cpuPart}) is not supported by Picovoice.");
            }
        }

        public static string PvLinuxEnv()
        {
            string cpuPart = GetCpuPart();
            switch (cpuPart)
            {
                case "0xd03":
                case "0xd08":
                case "0xd0b": return "raspberry-pi";
                default:
                    throw new PlatformNotSupportedException($"This device (CPU part = {cpuPart}) is not supported by Picovoice.");
            }
        }

        public static IntPtr GetPtrFromUtf8String(string s)
        {
            byte[] utf8Bytes = Encoding.UTF8.GetBytes(s + '\0');
            IntPtr p = Marshal.AllocHGlobal(utf8Bytes.Length);
            Marshal.Copy(utf8Bytes, 0, p, utf8Bytes.Length);

            return p;
        }

        public static string GetUtf8StringFromPtr(IntPtr p)
        {
            int i = 0;
            List<byte> data = new List<byte>();
            while (true)
            {
                byte b = Marshal.ReadByte(p, i++);
                if (b == 0)
                {
                    break;
                }
                data.Add(b);
            }

            return Encoding.UTF8.GetString(data.ToArray());
        }
        private static string GetCpuPart()
        {
            string cpuInfo = File.ReadAllText("/proc/cpuinfo");
            string[] cpuPartList = cpuInfo.Split('\n').Where(x => x.Contains("CPU part")).ToArray();
            if (cpuPartList.Length == 0)
            {
                throw new PlatformNotSupportedException($"Unsupported CPU.\n{cpuInfo}");
            }

            string cpuPart = cpuPartList[0].Split(' ').Last().ToLower();
            return cpuPart;
        }
    }
}