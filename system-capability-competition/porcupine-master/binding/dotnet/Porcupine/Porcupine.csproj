﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net8.0;net6.0;netcoreapp3.0;netstandard2.0</TargetFrameworks>
        <Version>3.0.10</Version>
        <Authors>Picovoice</Authors>
        <Company />
        <Product>Porcupine Wake Word Engine</Product>
        <PackageLicenseExpression>Apache-2.0</PackageLicenseExpression>
        <PackageProjectUrl>https://github.com/Picovoice/porcupine</PackageProjectUrl>
        <RepositoryUrl>https://github.com/Picovoice/porcupine.git</RepositoryUrl>
        <RepositoryType>git</RepositoryType>
        <PackageTags>porcupine, picovoice, wake word, hotword, trigger word, offline, private, voice ai, speech recognition</PackageTags>
        <PackageReleaseNotes>See https://github.com/Picovoice/porcupine/ </PackageReleaseNotes>
        <Copyright>Picovoice 2020-2025</Copyright>
        <Description>
            Porcupine is a highly-accurate and lightweight wake word engine. It enables building always-listening voice-enabled applications.

            Porcupine is:
            - using deep neural networks trained in real-world environments.
            - compact and computationally-efficient making it perfect for IoT.
            - scalable. It can detect multiple always-listening voice commands
            with no added CPU/memory footprint.
            - self-service. Developers can train custom wake phrases using Picovoice Console.
        </Description>
        <PackageRequireLicenseAcceptance>true</PackageRequireLicenseAcceptance>
        <PackageIcon>pv_circle_512.png</PackageIcon>
        <PackageReadmeFile>README.md</PackageReadmeFile>
    </PropertyGroup>

    <!--Target files-->
    <ItemGroup>
        <Content Include="Porcupine.netstandard2.0.targets">
            <PackagePath>
                buildTransitive\netstandard2.0\Porcupine.targets;
            </PackagePath>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Include="Porcupine.targets">
            <PackagePath>
                buildTransitive\netcoreapp3.0\Porcupine.targets;
                buildTransitive\net6.0\Porcupine.targets;
                buildTransitive\net8.0\Porcupine.targets;
            </PackagePath>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
    </ItemGroup>

    <!--.NET Standard 2.0+ libs-->
    <ItemGroup>
        <Content Include="..\..\..\lib\windows\amd64\libpv_porcupine.dll">
            <PackagePath>
                buildTransitive\netstandard2.0\libpv_porcupine.dll;
                buildTransitive\netcoreapp3.0\lib\windows\amd64\libpv_porcupine.dll;
                buildTransitive\net6.0\lib\windows\amd64\libpv_porcupine.dll;
                buildTransitive\net8.0\lib\windows\amd64\libpv_porcupine.dll;
            </PackagePath>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <Link>lib\windows\amd64\libpv_porcupine.dll</Link>
            <Visible>false</Visible>
        </Content>
        <Content Include="..\..\..\lib\mac\x86_64\libpv_porcupine.dylib">
            <PackagePath>
                buildTransitive\netstandard2.0\libpv_porcupine.dylib;
                buildTransitive\netcoreapp3.0\lib\mac\x86_64\libpv_porcupine.dylib;
                buildTransitive\net6.0\lib\mac\x86_64\libpv_porcupine.dylib;
                buildTransitive\net8.0\lib\mac\x86_64\libpv_porcupine.dylib;
            </PackagePath>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <Link>lib\mac\x86_64\libpv_porcupine.dylib</Link>
            <Visible>false</Visible>
        </Content>
    </ItemGroup>

    <!--.NET 6.0+ libs-->
    <ItemGroup>
        <Content Include="..\..\..\lib\mac\arm64\libpv_porcupine.dylib">
            <PackagePath>
                buildTransitive\net6.0\lib\mac\arm64\libpv_porcupine.dylib;
                buildTransitive\net8.0\lib\mac\arm64\libpv_porcupine.dylib;
            </PackagePath>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <Link>lib\mac\arm64\libpv_porcupine.dylib</Link>
            <Visible>false</Visible>
        </Content>
        <Content Include="..\..\..\lib\windows\arm64\libpv_porcupine.dll">
            <PackagePath>
                buildTransitive\net6.0\lib\windows\arm64\libpv_porcupine.dll;
                buildTransitive\net8.0\lib\windows\arm64\libpv_porcupine.dll;
            </PackagePath>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <Link>lib\windows\arm64\libpv_porcupine.dll</Link>
            <Visible>false</Visible>
        </Content>
        <Content Include="..\..\..\lib\linux\x86_64\libpv_porcupine.so">
            <PackagePath>
                buildTransitive\net6.0\lib\linux\x86_64\libpv_porcupine.so;
                buildTransitive\net8.0\lib\linux\x86_64\libpv_porcupine.so;
            </PackagePath>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <Link>lib\linux\x86_64\libpv_porcupine.so</Link>
            <Visible>false</Visible>
        </Content>
        <Content Include="..\..\..\lib\raspberry-pi\**\*" Exclude="..\..\..\lib\raspberry-pi\arm11\*">
            <PackagePath>
                buildTransitive\net6.0\lib\raspberry-pi;
                buildTransitive\net8.0\lib\raspberry-pi;
            </PackagePath>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <Link>lib\raspberry-pi\%(RecursiveDir)%(Filename)%(Extension)</Link>
            <Visible>false</Visible>
        </Content>
    </ItemGroup>


    <!--Resources-->
    <ItemGroup>
        <Content Include="..\..\..\resources\keyword_files\windows\alexa_windows.ppn;
                          ..\..\..\resources\keyword_files\windows\americano_windows.ppn;
                          ..\..\..\resources\keyword_files\windows\blueberry_windows.ppn;
                          ..\..\..\resources\keyword_files\windows\bumblebee_windows.ppn;
                          ..\..\..\resources\keyword_files\windows\computer_windows.ppn;
                          ..\..\..\resources\keyword_files\windows\grapefruit_windows.ppn;
                          ..\..\..\resources\keyword_files\windows\grasshopper_windows.ppn;
                          ..\..\..\resources\keyword_files\windows\hey google_windows.ppn;
                          ..\..\..\resources\keyword_files\windows\hey siri_windows.ppn;
                          ..\..\..\resources\keyword_files\windows\jarvis_windows.ppn;
                          ..\..\..\resources\keyword_files\windows\ok google_windows.ppn;
                          ..\..\..\resources\keyword_files\windows\picovoice_windows.ppn;
                          ..\..\..\resources\keyword_files\windows\porcupine_windows.ppn;
                          ..\..\..\resources\keyword_files\windows\terminator_windows.ppn;">
            <PackagePath>
                buildTransitive\netstandard2.0\resources\keyword_files\windows;
                buildTransitive\netcoreapp3.0\resources\keyword_files\windows;
                buildTransitive\net6.0\resources\keyword_files\windows;
                buildTransitive\net8.0\resources\keyword_files\windows;
            </PackagePath>
            <Link>resources\keyword_files\windows\%(Filename)%(Extension)</Link>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Include="..\..\..\resources\keyword_files\mac\alexa_mac.ppn;
                          ..\..\..\resources\keyword_files\mac\americano_mac.ppn;
                          ..\..\..\resources\keyword_files\mac\blueberry_mac.ppn;
                          ..\..\..\resources\keyword_files\mac\bumblebee_mac.ppn;
                          ..\..\..\resources\keyword_files\mac\computer_mac.ppn;
                          ..\..\..\resources\keyword_files\mac\grapefruit_mac.ppn;
                          ..\..\..\resources\keyword_files\mac\grasshopper_mac.ppn;
                          ..\..\..\resources\keyword_files\mac\hey google_mac.ppn;
                          ..\..\..\resources\keyword_files\mac\hey siri_mac.ppn;
                          ..\..\..\resources\keyword_files\mac\jarvis_mac.ppn;
                          ..\..\..\resources\keyword_files\mac\ok google_mac.ppn;
                          ..\..\..\resources\keyword_files\mac\picovoice_mac.ppn;
                          ..\..\..\resources\keyword_files\mac\porcupine_mac.ppn;
                          ..\..\..\resources\keyword_files\mac\terminator_mac.ppn;">
            <PackagePath>
                buildTransitive\netstandard2.0\resources\keyword_files\mac;
                buildTransitive\netcoreapp3.0\resources\keyword_files\mac;
                buildTransitive\net6.0\resources\keyword_files\mac;
                buildTransitive\net8.0\resources\keyword_files\mac;
            </PackagePath>
            <Link>resources\keyword_files\mac\%(Filename)%(Extension)</Link>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Include="..\..\..\resources\keyword_files\linux\alexa_linux.ppn;
                          ..\..\..\resources\keyword_files\linux\americano_linux.ppn;
                          ..\..\..\resources\keyword_files\linux\blueberry_linux.ppn;
                          ..\..\..\resources\keyword_files\linux\bumblebee_linux.ppn;
                          ..\..\..\resources\keyword_files\linux\computer_linux.ppn;
                          ..\..\..\resources\keyword_files\linux\grapefruit_linux.ppn;
                          ..\..\..\resources\keyword_files\linux\grasshopper_linux.ppn;
                          ..\..\..\resources\keyword_files\linux\hey google_linux.ppn;
                          ..\..\..\resources\keyword_files\linux\hey siri_linux.ppn;
                          ..\..\..\resources\keyword_files\linux\jarvis_linux.ppn;
                          ..\..\..\resources\keyword_files\linux\ok google_linux.ppn;
                          ..\..\..\resources\keyword_files\linux\picovoice_linux.ppn;
                          ..\..\..\resources\keyword_files\linux\porcupine_linux.ppn;
                          ..\..\..\resources\keyword_files\linux\terminator_linux.ppn;">
            <PackagePath>
                buildTransitive\netstandard2.0\resources\keyword_files\linux;
                buildTransitive\netcoreapp3.0\resources\keyword_files\linux;
                buildTransitive\net6.0\resources\keyword_files\linux;
                buildTransitive\net8.0\resources\keyword_files\linux;
            </PackagePath>
            <Link>resources\keyword_files\linux\%(Filename)%(Extension)</Link>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Include="..\..\..\resources\keyword_files\raspberry-pi\alexa_raspberry-pi.ppn;
                          ..\..\..\resources\keyword_files\raspberry-pi\americano_raspberry-pi.ppn;
                          ..\..\..\resources\keyword_files\raspberry-pi\blueberry_raspberry-pi.ppn;
                          ..\..\..\resources\keyword_files\raspberry-pi\bumblebee_raspberry-pi.ppn;
                          ..\..\..\resources\keyword_files\raspberry-pi\computer_raspberry-pi.ppn;
                          ..\..\..\resources\keyword_files\raspberry-pi\grapefruit_raspberry-pi.ppn;
                          ..\..\..\resources\keyword_files\raspberry-pi\grasshopper_raspberry-pi.ppn;
                          ..\..\..\resources\keyword_files\raspberry-pi\hey google_raspberry-pi.ppn;
                          ..\..\..\resources\keyword_files\raspberry-pi\hey siri_raspberry-pi.ppn;
                          ..\..\..\resources\keyword_files\raspberry-pi\jarvis_raspberry-pi.ppn;
                          ..\..\..\resources\keyword_files\raspberry-pi\ok google_raspberry-pi.ppn;
                          ..\..\..\resources\keyword_files\raspberry-pi\picovoice_raspberry-pi.ppn;
                          ..\..\..\resources\keyword_files\raspberry-pi\porcupine_raspberry-pi.ppn;
                          ..\..\..\resources\keyword_files\raspberry-pi\terminator_raspberry-pi.ppn;">
            <PackagePath>
                buildTransitive\netstandard2.0\resources\keyword_files\raspberry-pi;
                buildTransitive\netcoreapp3.0\resources\keyword_files\raspberry-pi;
                buildTransitive\net6.0\resources\keyword_files\raspberry-pi;
                buildTransitive\net8.0\resources\keyword_files\raspberry-pi;
            </PackagePath>
            <Link>resources\keyword_files\raspberry-pi\%(Filename)%(Extension)</Link>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Include="..\..\..\lib\common\porcupine_params.pv">
            <PackagePath>
                buildTransitive\common\porcupine_params.pv;
            </PackagePath>
            <Link>lib\common\porcupine_params.pv</Link>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
    </ItemGroup>
    <ItemGroup>
        <Folder Include="Content\" />
    </ItemGroup>
    <ItemGroup>
        <None Include="Content\pv_circle_512.png">
            <Pack>True</Pack>
            <PackagePath></PackagePath>
        </None>
        <None Include="..\README.md">
            <Pack>True</Pack>
            <PackagePath>README.md</PackagePath>
        </None>
    </ItemGroup>
</Project>
