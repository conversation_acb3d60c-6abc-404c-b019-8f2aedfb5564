﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net8.0;net6.0;net5.0;netcoreapp3.1;netcoreapp3.0;netcoreapp2.1;</TargetFrameworks>
        <IsPackable>false</IsPackable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.1.0" />
        <PackageReference Include="MSTest.TestAdapter" Version="2.2.8" />
        <PackageReference Include="MSTest.TestFramework" Version="2.2.8" />
        <PackageReference Include="coverlet.collector" Version="3.1.2" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Porcupine\Porcupine.csproj" />
    </ItemGroup>
    <ItemGroup>
        <Content Include="..\..\..\resources\audio_samples\*">
            <Link>resources\audio_samples\%(Filename)%(Extension)</Link>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
    </ItemGroup>
    <ItemGroup>
        <Content Include="..\..\..\lib\windows\amd64\libpv_porcupine.dll">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <Link>libpv_porcupine.dll</Link>
            <Visible>false</Visible>
        </Content>
        <Content Include="..\..\..\lib\linux\x86_64\libpv_porcupine.so">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <Link>libpv_porcupine.so</Link>
            <Visible>false</Visible>
        </Content>
        <Content Include="..\..\..\lib\mac\x86_64\libpv_porcupine.dylib">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <Link>libpv_porcupine.dylib</Link>
            <Visible>false</Visible>
        </Content>
    </ItemGroup>
</Project>
