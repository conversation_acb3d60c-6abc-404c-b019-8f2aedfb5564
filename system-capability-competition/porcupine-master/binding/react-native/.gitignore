# OSX
#
.DS_Store

# XDE
.expo/

# VSCode
.vscode/
jsconfig.json

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace
PvPorcupine.xcframework
ios/resources/**


# Android/IJ
#
.idea
.gradle
local.properties
android.iml
android/src/main/jniLibs/**
android/src/main/res/**
Porcupine.java
PorcupineException.java

# node.js
#
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# BUCK
buck-out/
\.buckd/
android/app/libs
android/keystores/debug.keystore

# Expo
.expo/*

# generated by build
lib/
pkg/*.tgz
