{"compilerOptions": {"baseUrl": ".", "paths": {"react-native-porcupine": ["./src/index"]}, "allowUnreachableCode": false, "allowUnusedLabels": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "jsx": "react", "lib": ["esnext"], "module": "esnext", "moduleResolution": "node", "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noImplicitUseStrict": false, "noStrictGenericChecks": false, "noUnusedLocals": true, "noUnusedParameters": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "esnext"}, "exclude": ["test-app"]}