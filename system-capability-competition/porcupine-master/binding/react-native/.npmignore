# JS
node_modules
copy.js

# Config files
.babelrc
babel.config.js
.editorconfig
.eslintrc
.flowconfig
.watchmanconfig
jsconfig.json
.npmrc
.gitattributes
.circleci
*.coverage.json
.opensource
.circleci
.eslintignore
codecov.yml

# Android
android/.project
android/*/build/
android/gradlew
android/build
android/gradlew.bat
android/gradle/
android/local.properties
android/.gradle/
android/.signing/
android/.idea/
android/*.iml
android/.settings

# iOS
ios/*.xcodeproj/xcuserdata
*.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3
*.xcuserstate
*.xcworkspacedata
project.xcworkspace/
xcuserdata/

# Misc
.DS_Store
.DS_Store?
*.DS_Store
coverage.android.json
coverage.ios.json
coverage
npm-debug.log
.github
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
docs
.idea
tests/
codorials
.vscode
.nyc_output
**/__tests__
pkg
copy.js

test-app/
