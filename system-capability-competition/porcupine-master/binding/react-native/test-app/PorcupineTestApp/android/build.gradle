// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
        kotlinVersion = "2.0.21"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
    }
}

allprojects {
    afterEvaluate {
        if (it.hasProperty('android')){
            android {
                defaultConfig {
                    minSdkVersion 24
                }
            }
        }
    }
}

allprojects {
    repositories {
        maven {
            url("$rootDir/../node_modules/detox/Detox-android")
        }
    }
}

apply plugin: "com.facebook.react.rootproject"
