buildscript {

  repositories {
    google()
    jcenter()
    mavenCentral()
  }

  dependencies {
    classpath 'com.android.tools.build:gradle:7.4.2'
  }
}

apply plugin: 'com.android.library'

def getExtOrDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : project.properties['Porcupine_' + name]
}

def getExtOrIntegerDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : (project.properties['Porcupine_' + name]).toInteger()
}

android {
  def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
  if (agpVersion.tokenize('.')[0].toInteger() >= 7) {
      namespace "ai.picovoice.reactnative.porcupine"
  }

  if (agpVersion.tokenize('.')[0].toInteger() >= 8) {
      buildFeatures {
          buildConfig = true
      }
  }

  compileSdkVersion getExtOrIntegerDefault('compileSdkVersion')
  buildToolsVersion getExtOrDefault('buildToolsVersion')
  defaultConfig {
    minSdkVersion 24
    targetSdkVersion getExtOrIntegerDefault('targetSdkVersion')
    versionCode 1
    versionName "1.0"

  }

  buildTypes {
    release {
      minifyEnabled false
    }
  }
  lintOptions {
    disable 'GradleCompatible'
  }
  compileOptions {
    sourceCompatibility JavaVersion.VERSION_1_8
    targetCompatibility JavaVersion.VERSION_1_8
  }
}

repositories {
  mavenCentral()
  jcenter()
  google()

  def found = false
  def defaultDir = null
  def androidSourcesName = 'React Native sources'

  if (rootProject.ext.has('reactNativeAndroidRoot')) {
    defaultDir = rootProject.ext.get('reactNativeAndroidRoot')
  } else {
    defaultDir = new File(
            projectDir,
            '/../../../node_modules/react-native/android'
    )
  }

  if (defaultDir.exists()) {
    maven {
      url defaultDir.toString()
      name androidSourcesName
    }

    logger.info(":${project.name}:reactNativeAndroidRoot ${defaultDir.canonicalPath}")
    found = true
  } else {
    def parentDir = rootProject.projectDir

    1.upto(5, {
      if (found) return true
      parentDir = parentDir.parentFile

      def androidSourcesDir = new File(
              parentDir,
              'node_modules/react-native'
      )

      def androidPrebuiltBinaryDir = new File(
              parentDir,
              'node_modules/react-native/android'
      )

      if (androidPrebuiltBinaryDir.exists()) {
        maven {
          url androidPrebuiltBinaryDir.toString()
          name androidSourcesName
        }

        logger.info(":${project.name}:reactNativeAndroidRoot ${androidPrebuiltBinaryDir.canonicalPath}")
        found = true
      } else if (androidSourcesDir.exists()) {
        maven {
          url androidSourcesDir.toString()
          name androidSourcesName
        }

        logger.info(":${project.name}:reactNativeAndroidRoot ${androidSourcesDir.canonicalPath}")
        found = true
      }
    })
  }

  if (!found) {
    throw new GradleException(
            "${project.name}: unable to locate React Native android sources. " +
                    "Ensure you have you installed React Native as a dependency in your project and try again."
    )
  }
}

dependencies {
  // noinspection GradleDynamicVersion
  api 'com.facebook.react:react-native:+'
  implementation 'ai.picovoice:porcupine-android:3.0.3'
}
