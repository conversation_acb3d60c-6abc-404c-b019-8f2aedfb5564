{"name": "@picovoice/porcupine-react-native", "version": "3.0.4", "description": "Picovoice Porcupine React Native binding", "main": "lib/commonjs/index", "module": "lib/module/index", "types": "lib/typescript/index.d.ts", "react-native": "src/index", "source": "src/index", "scripts": {"test": "jest", "typescript": "tsc --noEmit", "lint": "eslint \"**/*.{js,ts,tsx}\"", "prepare": "bob build", "pkg": "cd pkg && npm pack .. && cd .."}, "keywords": ["react-native", "ios", "android", "porcupine", "picovoice", "wake word", "hotword", "trigger word", "offline", "private", "voice ai", "speech recognition"], "repository": "https://github.com/Picovoice/porcupine", "author": "Picovoice <<EMAIL>> (https://picovoice.ai)", "license": "Apache-2.0", "bugs": {"url": "https://github.com/Picovoice/porcupine/issues"}, "homepage": "https://github.com/Picovoice/porcupine/#readme", "devDependencies": {"@picovoice/react-native-voice-processor": "^1.2.3", "@react-native-community/eslint-config": "^3.2.0", "@types/jest": "^26.0.0", "@types/react": "^16.9.19", "@types/react-native": "0.68.5", "eslint": "^8.28.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.3", "jest": "^26.0.1", "mkdirp": "^1.0.4", "ncp": "^2.0.0", "pod-install": "^0.1.0", "prettier": "^2.0.5", "react": "17.0.2", "react-native": "0.68.7", "react-native-builder-bob": "^0.18.3", "typescript": "^4.9.3"}, "peerDependencies": {"@picovoice/react-native-voice-processor": ">= 1.2.3", "react": ">= 17.0", "react-native": ">= 0.63"}, "jest": {"preset": "react-native", "modulePathIgnorePatterns": ["<rootDir>/lib/", "<rootDir>/test-app/"]}, "eslintConfig": {"extends": ["@react-native-community", "prettier"], "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "eslintIgnore": ["node_modules/", "lib/", "test-app/"], "prettier": {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}, "dependencies": {}, "files": ["*", "!node_modules", "!pkg", "!test-app"], "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", "typescript"]}}