plugins {
    id 'checkstyle'
    id 'java'
    id 'java-library'
    id 'maven-publish'
    id 'signing'
}

ext {
    PUBLISH_GROUP_ID = 'ai.picovoice'
    PUBLISH_VERSION = '3.0.5'
    PUBLISH_ARTIFACT_ID = 'porcupine-java'
}

java {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}

repositories {
    mavenCentral()
}

sourceSets {
    main {
        java {
            srcDirs = ['src']
        }
    }
    test {
        java {
            srcDirs = ['test']
        }
    }
}

javadoc.options.addStringOption('Xdoclint:none', '-quiet')

def outputDir = "$buildDir/classes/java/main"

task copyLinuxKeywordResources(type: Copy) {
    from('../../resources/keyword_files/linux')
    include('alexa_linux.ppn',
            'americano_linux.ppn',
            'blueberry_linux.ppn',
            'bumblebee_linux.ppn',
            'computer_linux.ppn',
            'grapefruit_linux.ppn',
            'grasshopper_linux.ppn',
            'hey google_linux.ppn',
            'hey siri_linux.ppn',
            'jarvis_linux.ppn',
            'ok google_linux.ppn',
            'picovoice_linux.ppn',
            'porcupine_linux.ppn',
            'terminator_linux.ppn')
    into("${outputDir}/porcupine/resources/keyword_files/linux/")
}
task copyMacKeywordResources(type: Copy) {
    from('../../resources/keyword_files/mac')
    include('alexa_mac.ppn',
            'americano_mac.ppn',
            'blueberry_mac.ppn',
            'bumblebee_mac.ppn',
            'computer_mac.ppn',
            'grapefruit_mac.ppn',
            'grasshopper_mac.ppn',
            'hey google_mac.ppn',
            'hey siri_mac.ppn',
            'jarvis_mac.ppn',
            'ok google_mac.ppn',
            'picovoice_mac.ppn',
            'porcupine_mac.ppn',
            'terminator_mac.ppn')
    into("${outputDir}/porcupine/resources/keyword_files/mac/")
}
task copyWindowsKeywordResources(type: Copy) {
    from('../../resources/keyword_files/windows')
    include('alexa_windows.ppn',
            'americano_windows.ppn',
            'blueberry_windows.ppn',
            'bumblebee_windows.ppn',
            'computer_windows.ppn',
            'grapefruit_windows.ppn',
            'grasshopper_windows.ppn',
            'hey google_windows.ppn',
            'hey siri_windows.ppn',
            'jarvis_windows.ppn',
            'ok google_windows.ppn',
            'picovoice_windows.ppn',
            'porcupine_windows.ppn',
            'terminator_windows.ppn')
    into("${outputDir}/porcupine/resources/keyword_files/windows/")
}
task copyRPiKeywordResources(type: Copy) {
    from('../../resources/keyword_files/raspberry-pi')
    include('alexa_raspberry-pi.ppn',
            'americano_raspberry-pi.ppn',
            'blueberry_raspberry-pi.ppn',
            'bumblebee_raspberry-pi.ppn',
            'computer_raspberry-pi.ppn',
            'grapefruit_raspberry-pi.ppn',
            'grasshopper_raspberry-pi.ppn',
            'hey google_raspberry-pi.ppn',
            'hey siri_raspberry-pi.ppn',
            'jarvis_raspberry-pi.ppn',
            'ok google_raspberry-pi.ppn',
            'picovoice_raspberry-pi.ppn',
            'porcupine_raspberry-pi.ppn',
            'terminator_raspberry-pi.ppn')
    into("${outputDir}/porcupine/resources/keyword_files/raspberry-pi/")
}

task copyDefaultModel(type: Copy) {
    from('../../lib/common/porcupine_params.pv') into "${outputDir}/porcupine/lib/common/"
}
task copyLinuxLib(type: Copy) {
    from('../../lib/java/linux/x86_64/libpv_porcupine_jni.so') into "${outputDir}/porcupine/lib/java/linux/x86_64/"
}
task copyMacLib(type: Copy) {
    from('../../lib/java/mac/')
    include('x86_64/libpv_porcupine_jni.dylib',
            'arm64/libpv_porcupine_jni.dylib')
    into "${outputDir}/porcupine/lib/java/mac/"
}
task copyWindowsLib(type: Copy) {
    from('../../lib/java/windows/')
    include('amd64/pv_porcupine_jni.dll',
            'arm64/pv_porcupine_jni.dll')
    into "${outputDir}/porcupine/lib/java/windows/"
}
task copyRPiLib(type: Copy) {
    from('../../lib/java/raspberry-pi/')
    include('cortex-a7/libpv_porcupine_jni.so',
            'cortex-a53/libpv_porcupine_jni.so',
            'cortex-a53-aarch64/libpv_porcupine_jni.so',
            'cortex-a72/libpv_porcupine_jni.so',
            'cortex-a72-aarch64/libpv_porcupine_jni.so',
            'cortex-a76/libpv_porcupine_jni.so',
            'cortex-a76-aarch64/libpv_porcupine_jni.so')
    into("${outputDir}/porcupine/lib/java/raspberry-pi/")
}

task copyTasks(type: GradleBuild) {
    tasks = ['copyLinuxKeywordResources',
             'copyMacKeywordResources',
             'copyWindowsKeywordResources',
             'copyRPiKeywordResources',
             'copyDefaultModel',
             'copyLinuxLib',
             'copyMacLib',
             'copyWindowsLib',
             'copyRPiLib']
}

jar {
    dependsOn 'copyTasks'
}

javadoc {
    dependsOn 'copyTasks'
}

if (file("${rootDir}/publish-mavencentral.gradle").exists()) {
    apply from: "${rootDir}/publish-mavencentral.gradle"
}

dependencies {
    testImplementation 'com.google.code.gson:gson:2.10.1'
    testImplementation 'org.junit.jupiter:junit-jupiter:5.4.2'
    testImplementation 'org.junit.jupiter:junit-jupiter-params:5.8.2'
}

compileTestJava {
    dependsOn 'assemble'
}

test {
    systemProperty 'pvTestingAccessKey', System.getProperty('pvTestingAccessKey')
    systemProperty 'numTestIterations', System.getProperty('numTestIterations')
    systemProperty 'performanceThresholdSec', System.getProperty('performanceThresholdSec')
    useJUnitPlatform()
}

compileJava.options.encoding = "UTF-8"
compileTestJava.options.encoding = "UTF-8"
