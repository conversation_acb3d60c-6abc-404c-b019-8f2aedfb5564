## [2.0.0] - 2021-11-23
* Porcupine v2.0 initial release
* Version 1 can be accessed through: https://pub.dev/packages/porcupine

## [2.0.1] - 2021-11-23
* Fix bug for iOS built-in keywords with spaces

## [2.0.2] - 2021-11-23
* Fix android compilation warning

## [2.0.3] - 2021-11-23
* Fix bug for android with lower case built-in keywords

## [2.0.4] - 2021-11-25
* Run formatter

## [2.0.5] - 2021-12-22
* Update Android native package

## [2.0.6] - 2022-01-11
* Android support patch

## [2.1.0] - 2022-01-19
* Android/iOS dev support patch

## [2.1.1] - 2022-03-11
* Docs update

## [2.1.2] - 2022-05-13
* Android/iOS lib patch

## [2.1.3] - 2022-06-30
* Additional language support added (it, ja, ko, pt)

## [2.1.4] - 2022-08-02
* Improve documentation

## [2.1.5] - 2022-08-02
* Add documentation and repository links

## [2.1.6] - 2022-09-21
* Update Android libs

## [2.2.0] - 2023-04-12
* Additional language support added (ar, hi, nl, pl, ru, sv, vn, zh)

## [2.2.1] - 2023-08-09
* Update flutter-voice-processor

## [2.2.2] - 2023-08-24
* Update native packages

## [3.0.0] - 2023-10-25
* Engine improvements
* Improved error reporting

## [3.0.1] - 2023-11-17
* Update native packages

## [3.0.2] - 2024-01-15
* Address locale issue

## [3.0.3] - 2024-02-08
* Additional gradle plugin build support

## [3.0.4] - 2024-10-28
* Update native packages

## [3.0.5] - 2024-10-28
* Explicitly set Locale to ENGLISH for upper casing