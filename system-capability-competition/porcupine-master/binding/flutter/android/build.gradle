group 'ai.picovoice.flutter.porcupine'
version '3.0.5'

buildscript {
    repositories {
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
    }
}

apply plugin: 'com.android.library'

android {
    def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
    if (agpVersion.tokenize('.')[0].toInteger() >= 7) {
        namespace "ai.picovoice.flutter.porcupine"
    }

    if (agpVersion.tokenize('.')[0].toInteger() >= 8) {
        buildFeatures {
            buildConfig = true
        }
    }

    compileSdkVersion 31

    defaultConfig {
        minSdkVersion 21
    }

    buildTypes {
        release {
            minifyEnabled false
        }
    }
    lintOptions {
        disable 'GradleCompatible'
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation 'ai.picovoice:porcupine-android:3.0.3'
}
