# Porcupine Binding for React

## Porcupine wake word engine

Made in Vancouver, Canada by [Picovoice](https://picovoice.ai)

Porcupine is a highly accurate and lightweight wake word engine. It enables building always-listening voice-enabled
applications using cutting edge voice AI.

Porcupine is:

- private and offline
- [accurate](https://github.com/Picovoice/wake-word-benchmark)
- [resource efficient](https://www.youtube.com/watch?v=T0tAnh8tUQg) (runs even on microcontrollers)
- data efficient (wake words can be easily generated by simply typing them, without needing thousands of hours of
  bespoke audio training data and manual effort)
- scalable to many simultaneous wake-words / always-on voice commands
- cross-platform

## Compatibility

- Chrome / Edge
- Firefox
- Safari

### Restrictions

IndexedDB and WebWorkers are required to use `Porcupine React`. Browsers without support (i.e. Firefox Incognito Mode) 
should use the [`PorcupineWeb binding`](https://github.com/Picovoice/porcupine/tree/master/binding/web) main thread method.

## Installation

### Package

Using `Yarn`:

```console
yarn add @picovoice/porcupine-react @picovoice/web-voice-processor
```

or using `npm`:

```console
npm install --save @picovoice/porcupine-react @picovoice/web-voice-processor
```

### AccessKey

Porcupine requires a valid Picovoice `AccessKey` at initialization. `AccessKey` acts as your credentials when using
Porcupine SDKs.
You can get your `AccessKey` for free. Make sure to keep your `AccessKey` secret.
Signup or Login to [Picovoice Console](https://console.picovoice.ai/) to get your `AccessKey`.

## Usage

There are two methods to initialize Porcupine:

### Public Directory

**NOTE**: Due to modern browser limitations of using a file URL, this method does __not__ work if used without hosting a
server.

This method fetches [the model file](https://github.com/Picovoice/porcupine/blob/master/lib/common/porcupine_params.pv)
from the public directory and feeds it to Porcupine.
Copy the model file into the public directory:

```console
cp ${PORCUPINE_MODEL_FILE} ${PATH_TO_PUBLIC_DIRECTORY}
```

### Base64

**NOTE**: This method works without hosting a server, but increases the size of the model file roughly by 33%.

This method uses a base64 string of the model file and feeds it to Porcupine. Use the built-in script `pvbase64` to
base64 your model file:

```console
npx pvbase64 -i ${PORCUPINE_MODEL_FILE} -o ${OUTPUT_DIRECTORY}/${MODEL_NAME}.js
```

The output will be a js file which you can import into any file of your project. For detailed information
about `pvbase64`,
run:

```console
npx pvbase64 -h
```

### Porcupine Model

Porcupine saves and caches your parameter model file (`.pv`) in IndexedDB to be used by Web Assembly.
Use a different `customWritePath` variable to hold multiple model values and set the `forceWrite` value to true to force
re-save the model file.
If the model file changes, `version` should be incremented to force the cached models to be updated.
Either `base64` or `publicPath` must be set to instantiate Porcupine. If both are set, Porcupine will use the `base64`
model.

```typescript
// Model (.pv)
const porcupineModel = {
  publicPath: ${MODEL_RELATIVE_PATH},
  // or
  base64: ${MODEL_BASE64_STRING},

  // Optional
  customWritePath: 'custom_model',
  forceWrite: true,
  version: 1,
}
```

### Initialize Porcupine

Use `usePorcupine` and `init` to initialize `Porcupine`:

```typescript
import { BuiltInKeyword } from '@picovoice/porcupine-web';
import { usePorcupine } from '@picovoice/porcupine-react';

const {
  keywordDetection,
  isLoaded,
  isListening,
  error,
  init,
  start,
  stop,
  release,
} = usePorcupine();

await init(
  ${ACCESS_KEY},
  [BuiltInKeyword.Porcupine],
  porcupineModel
);
```

In case of any errors, use `error` state to check the error message, else
use the `isLoaded` variable to check if `Porcupine` has loaded.

### Process Audio Frames

Porcupine React binding uses [WebVoiceProcessor](https://github.com/Picovoice/web-voice-processor) to record audio.
To start detecting wake word, run the `start` function:

```typescript
await start();
```

If `WebVoiceProcessor` has started correctly, `isListening` will be set to true.
Use the `keywordDetection` state to get wake word detection results:

```typescript
useEffect(() => {
  if (keywordDetection !== null) {
    console.log(keywordDetection.label);
  }
}, [keywordDetection])
```

### Stop

Run `stop` to stop keyword detection:

```typescript
await stop();
```

If `WebVoiceProcessor` has stopped correctly, `isListening` will be set to false.

### Release

While running in a component, you can call `release` to clean up all resources used by Porcupine and WebVoiceProcessor:

```typescript
await release();
```

This will set `isLoaded` and `isListening` to false.

You do not need to call `release` when your component is unmounted - the hook will clean up automatically on unmount.

## Custom Keywords

Create custom keywords using the [Picovoice Console](https://console.picovoice.ai/).
Train and download a Porcupine keyword model (`.ppn`) for the target platform `Web (WASM)`.
This model file can be used directly with `publicPath`, but, if `base64` is preferable, convert the `.ppn` file to a base64
JavaScript variable using the built-in `pvbase64` script:

```console
npx pvbase64 -i ${KEYWORD_FILE}.ppn -o ${KEYWORD_BASE64}.js -n ${KEYWORD_BASE64_VAR_NAME}
```

Similar to the model file (`.pv`), keyword files (`.ppn`) are saved in IndexedDB to be used by Web Assembly.
Either `base64` or `publicPath` must be set for each keyword to instantiate Porcupine.
If both are set, Porcupine will use the `base64` model.
An arbitrary `label` is required to identify the keyword once the detection occurs.

```typescript
// custom keyword (.ppn)
const keywordModel = {
  publicPath: ${KEYWORD_RELATIVE_PATH},
  // or
  base64: ${KEYWORD_BASE64_STRING},
  label: ${KEYWORD_LABEL},
  // Optional
  customWritePath: 'custom_keyword',
  forceWrite: true,
  version: 1,
}
```

Then, initialize an instance of `Porcupine`:

```typescript
const {
  keywordDetection,
  isLoaded,
  isListening,
  error,
  init,
  start,
  stop,
  release,
} = usePorcupine();

await init(
        ${ACCESS_KEY},
        keywordModel,
        porcupineModel
);
```

## Non-English Languages

In order to detect non-English wake words you need to use the corresponding model file (`.pv`). The model files for all
supported languages are available [here](https://github.com/Picovoice/porcupine/tree/master/lib/common).

## Demo

For example usage refer to our [Web react application](https://github.com/Picovoice/porcupine/tree/master/demo/react).
