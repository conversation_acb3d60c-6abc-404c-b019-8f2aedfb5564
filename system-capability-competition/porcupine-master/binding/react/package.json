{"name": "@picovoice/porcupine-react", "version": "3.0.3", "description": "React component for Porcupine Web SDK", "entry": "src/index.ts", "module": "dist/esm/index.js", "iife": "dist/iife/index.js", "types": "dist/types/index.d.ts", "license": "Apache-2.0", "keywords": ["porcupine", "web", "voice", "speech", "recognition", "ai", "hotword", "wake", "word", "offline", "hooks", "react", "dom"], "author": "Picovoice Inc", "scripts": {"build:all": "rollup --config", "build:types": "tsc --declaration --declarationMap --emitDeclarationOnly --outDir ./dist/types", "build": "npm-run-all --parallel build:**", "lint": "eslint . --ext .js,.ts", "prepack": "npm-run-all build", "start": "cross-env TARGET='debug' rollup --config --watch", "watch": "rollup --config --watch", "format": "prettier --write \"**/*.{js,ts,json}\"", "setup-test": "node scripts/setup_test.js && npx pvbase64 -i ./test/porcupine_params.pv -o ./test/porcupine_params.js", "test": "cypress run --component"}, "dependencies": {"@picovoice/porcupine-web": "=3.0.3"}, "devDependencies": {"@babel/core": "^7.21.3", "@babel/plugin-transform-runtime": "^7.21.0", "@babel/preset-env": "^7.20.2", "@babel/runtime": "^7.21.0", "@picovoice/web-voice-processor": "~4.0.8", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-commonjs": "^24.0.1", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-terser": "^0.4.0", "@rollup/pluginutils": "^5.0.2", "@testing-library/react-hooks": "^8.0.1", "@types/react": "^17.0.2", "@typescript-eslint/eslint-plugin": "^5.51.0", "@typescript-eslint/parser": "^5.51.0", "async-mutex": "^0.4.0", "cross-env": "^7.0.3", "cypress": "^12.8.1", "eslint": "^8.22.0", "eslint-plugin-cypress": "^2.12.1", "npm-run-all": "^4.1.5", "prettier": "^2.8.3", "react": "^17.0.2", "react-dom": "^17.0.2", "rollup": "^2.79.1", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.34.1", "rollup-plugin-web-worker-loader": "^1.6.1", "tslib": "^2.5.0", "typescript": "^4.9.5", "vite": "^4.5.10"}, "peerDependencies": {"@picovoice/web-voice-processor": "~4.0.8", "react": ">=17", "react-dom": ">=17"}}