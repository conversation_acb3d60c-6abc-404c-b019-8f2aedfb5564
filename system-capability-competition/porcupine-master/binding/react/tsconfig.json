{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "downlevelIteration": true, "isolatedModules": false, "lib": ["esnext", "dom"], "module": "esnext", "moduleResolution": "node", "noEmit": false, "outDir": "./dist", "removeComments": false, "resolveJsonModule": true, "sourceMap": true, "strict": true, "target": "esnext", "noImplicitAny": true, "types": ["cypress", "node"]}, "include": ["src"], "exclude": ["node_modules", "dist", "cypress", "test", "cypress.config.ts"]}