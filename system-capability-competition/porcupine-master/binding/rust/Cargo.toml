[package]
name = "pv_porcupine"
version = "3.0.3"
edition = "2018"
description = "The Rust bindings for Picovoice's Porcupine library"
license = "Apache-2.0"
homepage = "https://picovoice.ai/platform/porcupine/"
repository = "https://github.com/Picovoice/porcupine"
keywords = [
	"wake-word-engine",
	"hotword-detection",
	"keyword-spotting",
	"wake-word-detection",
	"voice-commands",
]
publish = true

include = [
	".gitignore",
	"build.rs",
	"Cargo.toml",
	"data/",
	"README.md",
	"src/",
]

[lib]
name = "porcupine"
path = "src/lib.rs"
crate_type = ["lib"]

[dependencies]
libc = "0.2"
libloading = "0.8"

[dev-dependencies]
rodio = "0.17"
itertools = "0.11"
serde_json = "1.0"
